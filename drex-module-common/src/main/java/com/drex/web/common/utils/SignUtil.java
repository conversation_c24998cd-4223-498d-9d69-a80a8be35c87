package com.drex.web.common.utils;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bitcoinj.core.Base58;
import org.sol4k.PublicKey;
import org.web3j.crypto.ECDSASignature;
import org.web3j.crypto.Hash;
import org.web3j.crypto.Keys;
import org.web3j.crypto.Sign;
import org.web3j.utils.Numeric;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

@Slf4j
public class SignUtil {

    private static final String PERSONAL_MESSAGE_PREFIX = "\u0019Ethereum Signed Message:\n";

    /** 100% ai代码率
     * evm personal_sign 验签，适用于小狐狸等签名
     * @param signature
     * @param message
     * @param address
     * @return
     */
    public static boolean checkEvmPersonalSign(String signature, String message, String address) {
        String prefix = PERSONAL_MESSAGE_PREFIX + message.length();
        byte[] msgHash = Hash.sha3((prefix + message).getBytes());

        byte[] signatureBytes = Numeric.hexStringToByteArray(signature);
        byte v = signatureBytes[64];
        if (v < 27) {
            v += 27;
        }

        Sign.SignatureData sd = new Sign.SignatureData(v,
                Arrays.copyOfRange(signatureBytes, 0, 32),
                Arrays.copyOfRange(signatureBytes, 32, 64));

        String addressRecovered = null;
        boolean match = false;

        for (int i = 0; i < 4; i++) {
            BigInteger publicKey = Sign.recoverFromSignature((byte) i,
                    new ECDSASignature(new BigInteger(1, sd.getR()),
                            new BigInteger(1, sd.getS())),
                    msgHash);
            if (publicKey != null) {
                addressRecovered = "0x" + Keys.getAddress(publicKey);

                if (addressRecovered.equalsIgnoreCase(address)) {
                    match = true;
                    break;
                }
            }
        }
        return match;
    }

    public static boolean verifyEOASignature(String message, String signature, String address) {
        try {
            // 准备前缀消息
            String prefix = PERSONAL_MESSAGE_PREFIX + message.length();
            byte[] prefixedMessage = (prefix + message).getBytes();
            byte[] signatureBytes = Numeric.hexStringToByteArray(signature);

            // 提取v, r, s
            byte v = signatureBytes[64];
            if (v < 27) {
                v += 27;
            }

            byte[] r = Arrays.copyOfRange(signatureBytes, 0, 32);
            byte[] s = Arrays.copyOfRange(signatureBytes, 32, 64);

            Sign.SignatureData signatureData = new Sign.SignatureData(v, r, s);

            // 恢复公钥
            BigInteger publicKey = Sign.signedMessageToKey(prefixedMessage, signatureData);
            String recoveredAddress = "0x" + Keys.getAddress(publicKey);

            // 比较地址
            boolean isValid = recoveredAddress.equalsIgnoreCase(address);
            log.info("EOA签名验证结果: {}, 恢复地址: {}, 期望地址: {}",
                    isValid, recoveredAddress, address);

            return isValid;
        } catch (Exception e) {
            log.error("EOA签名验证失败: {}", e.getMessage());
            return false;
        }
    }

    /** ai代码率： 100%
     * Verifies an ERC-6492 signature for a smart contract wallet
     * @param message The original message that was signed
     * @param signature The signature bytes (65 bytes)
     * @param expectedAddress The expected signing address
     * @return true if signature is valid, false otherwise
     */
    public static boolean verifyERC6492Signature(String message, byte[] signature, String expectedAddress) {
        try {
            // Extract r, s, v from signature
            if (signature.length != 65) {
                return false;
            }

            byte v = signature[64];
            if (v < 27) {
                v += 27;
            }

            byte[] r = new byte[32];
            byte[] s = new byte[32];
            System.arraycopy(signature, 0, r, 0, 32);
            System.arraycopy(signature, 32, s, 0, 32);

            // Recover the signer address
            Sign.SignatureData signatureData = new Sign.SignatureData(v, r, s);
            String recoveredAddress = Sign.signedMessageToKey(message.getBytes(), signatureData)
                    .toString(16);
            recoveredAddress = Keys.toChecksumAddress("0x" + recoveredAddress);

            // Compare with expected address
            return recoveredAddress.equalsIgnoreCase(expectedAddress);
        } catch (Exception e) {
            return false;
        }
    }

    public static boolean verifyHmacSignature(String data, String signature, String key) {
        log.info("verifyHmacSignature data:{}, signature:{}, key:{}", data, signature, key);
        HMac verifyHmac = SecureUtil.hmac(HmacAlgorithm.HmacSHA256, key);
        String verifySigned = verifyHmac.digestHex(data);

        // 比较两个签名是否相等
        boolean verify = StringUtils.equals(signature, verifySigned);
        log.info("verifyHmacSignature result: " + verify);
        return verify;
    }

    /**
     * 验证 Solana 签名
     * @param publicKey58 签名者的公钥，Base58 编码格式
     * @param message     被签名的原始消息字符串
     * @param signature58 签名的 Base58 编码字符串
     * @return 如果签名有效则返回 true，否则返回 false
     */
    public static boolean verifySolanaSignature(String publicKey58, String message, String signature58) {
        try {
            byte[] messageBytes = message.getBytes(StandardCharsets.UTF_8);
            PublicKey publicKey = new PublicKey(publicKey58);
            byte[] signatureBytes = Base58.decode(signature58);
            return publicKey.verify(signatureBytes, messageBytes);
        } catch (Exception e) {
            // 如果 Base58 解码失败或公钥格式错误，会抛出异常
            log.error("Solana signature verification failed: {}", e.getMessage());
            return false;
        }
    }

}
