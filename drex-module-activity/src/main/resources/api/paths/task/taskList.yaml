get:
  summary: 获取任务列表
  deprecated: false
  description: ''
  operationId: taskList
  tags:
    - Activity
  parameters:
    - name: app_id
      in: header
      description: app_id
      required: false
      example: trex
      schema:
        type: string
  responses:
    '200':
      description: Successful operation
      content:
        application/json:
          schema:
            $ref: '../../components/task/TaskListResponse.yaml'