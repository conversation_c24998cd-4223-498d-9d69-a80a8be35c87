package com.drex.web.common.config;


import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Slf4j
@Component
@ConfigurationProperties(prefix = "web")
public class WebProperties {

    private Integer chainId;

    private String anchorClientId;

    private String anchorHost;

    private String secretKeyFull = "r8PJqx2IchPDvRA9V6oXIYLyedyTooh5BrXjBAUIpmmoqJyPrHz555LY5s3EAPeE";

    private String saasId = "trex";

    private String appKey = "wjoEJIFqJ1UPsh9PW8GLJsaH0lpt85AF";

    private String inviteRule = "get 50 per ${maize_icon} sign up";
}
