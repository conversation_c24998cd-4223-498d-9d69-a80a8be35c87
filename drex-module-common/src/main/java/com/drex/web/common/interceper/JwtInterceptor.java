package com.drex.web.common.interceper;

import com.drex.customer.api.RemotePassportService;
import com.drex.customer.api.response.PassportDTO;
import com.drex.web.common.JwtHolder;
import com.drex.web.common.PassportHolder;
import com.drex.web.common.utils.JwtUtils;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
public class JwtInterceptor extends AbstractInterceptor {

    private static final String HEADER_NAME = "Authorization";
    @DubboReference
    private RemotePassportService remotePassportService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // Allow OPTIONS requests to pass through for CORS preflight
        if ("OPTIONS".equalsIgnoreCase(request.getMethod())) {
            return true;
        }

        String token = request.getHeader(HEADER_NAME);
        if (Objects.nonNull(token) && token.startsWith("Bearer ")) {
            token = token.substring(7);
            try {
                Jws<Claims> accessToken = JwtUtils.getAccessToken(token);
                if (accessToken == null) {
                    response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Invalid JWT token");
                    return false;
                }
                JwtHolder.bind(token);
                String passportId = accessToken.getBody().getSubject();
                PassportDTO passportDTO;
                passportDTO = remotePassportService.getPassportById(passportId);
                PassportHolder.bind(passportDTO);
                return true;
            } catch (Exception e) {
                log.error("Invalid JWT token", e);
                response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Invalid JWT token");
                return false;
            }
        }
        response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "JWT token is missing");
        return false;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        PassportHolder.clear();
    }
}
