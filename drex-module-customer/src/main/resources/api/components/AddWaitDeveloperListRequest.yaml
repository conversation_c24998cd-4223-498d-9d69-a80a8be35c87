type: object
properties:
  projectName:
    type: string
    description: 项目名称
  projectCategory:
    type: array
    items:
      type: string
    description: 项目类别，可多选
  projectCategoryOther:
    type: string
    description: 若项目类别选择 'Others'，请填写具体内容
  projectDescription:
    type: string
    description: 简要描述您的项目、使命及其解决的问题
  ecosystemAlignment:
    type: string
    description: 解释您的项目如何利用 T-Rex 并为生态系统带来什么价值
  projectStage:
    type: string
    description: 项目所处阶段
  projectWebsite:
    type: string
    description: 项目网站 URL
  projectTwitter:
    type: string
    description: 项目 Twitter URL
  projectCommunity:
    type: string
    description: 项目社区（Telegram 群组、Discord 等）URL
  primaryContactName:
    type: string
    description: 主要联系人姓名
  contactEmail:
    type: string
    format: email
    description: 联系人邮箱
  contactTelegram:
    type: string
    description: 联系人 Telegram 用户名（可选）
  supportType:
    type: array
    items:
      type: string
    description: 寻求的支持类型，可多选
  supportTypeOther:
    type: string
    description: 若支持类型选择 'Other'，请填写具体内容
  useOfFunds:
    type: string
    description: 简要概述计划如何使用申请的资金
  teamBackground:
    type: string
    description: 介绍您的核心团队及相关经验
  supportingDocuments:
    type: string
    description: 上传项目推介材料或支持文档（文件上传或 URL）
  additionalComments:
    type: string
    description: 其他需要我们了解的信息（可选）
required:
  - projectName
  - projectCategory
  - projectDescription
  - ecosystemAlignment
  - projectStage
  - projectWebsite
  - primaryContactName
  - contactEmail
  - supportType
  - useOfFunds
  - teamBackground
