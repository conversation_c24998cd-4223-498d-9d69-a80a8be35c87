package com.drex.web.common.config;

import com.drex.web.common.interceper.JwtInterceptor;
import com.drex.web.common.interceper.IpInterceptor;
import com.drex.web.common.interceper.S2SInterceptor;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class DrexWebMvcConfig implements WebMvcConfigurer {

    private static final String HEALTH_CHECK_URL = "/actuator/**";
    private static final String SWAGGER_URL = "/swagger-ui/**";
    private static final String API_DOC_URL = "/*/api-docs/**";
    private static final String ERROR_URL = "/error";

    @Resource
    private JwtInterceptor jwtInterceptor;
    @Resource
    private IpInterceptor ipInterceptor;
    @Resource
    private S2SInterceptor s2SInterceptor;

    public void addInterceptors(InterceptorRegistry registry) {
        registInterceptor(registry, jwtInterceptor, jwtAddPatterns(), jwtExcludePatterns());
        registInterceptor(registry, ipInterceptor, Lists.newArrayList("/v1/auth/login","/v1/video/session/init"), null);
        registInterceptor(registry, s2SInterceptor, Lists.newArrayList("/*/s2s/**"), new ArrayList<>());
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOrigins("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*");
        log.info("CORS configuration added for all origins");
    }

    private void registInterceptor(InterceptorRegistry registry, HandlerInterceptor interceptor, List<String> addPatterns, List<String> excludePatterns) {
        if (interceptor != null) {
            InterceptorRegistration registration = registry.addInterceptor(interceptor);
            if (!CollectionUtils.isEmpty(addPatterns)) {
                registration = registration.addPathPatterns(addPatterns);
            }
            if (!CollectionUtils.isEmpty(excludePatterns)) {
                log.info("registry{}, excludePatterns:{}", registry.getClass(), excludePatterns);
                registration.excludePathPatterns(excludePatterns);
            }
        }
    }

    protected List<String> jwtAddPatterns() {
        return Lists.newArrayList("/**");
    }

    protected List<String> jwtExcludePatterns() {
        return Lists.newArrayList(HEALTH_CHECK_URL, SWAGGER_URL, API_DOC_URL, ERROR_URL,
                "/v1/auth/challenge", "/v1/auth/login", "/v1/user/verifyCode",
                "/v1/user/verifyCodeImage", "/v1/customers/addWaitList", "/v1/auth/refresh",
                "/v1/tx/faucet", "/v1/customers/addDeveloperWaitList",
                "/v1/customers/addDeveloperWaitList/upload", "/v1/customers/addCreateWaitList",
                "/v1/news/discovery/events","/v1/news/dicovery/blog", "/v1/auth/qr/login",
                "/*/s2s/**");
    }
}
