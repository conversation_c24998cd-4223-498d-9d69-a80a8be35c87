type: object
properties:
  sessionId:
    type: string
    description: 会话ID
  customerId:
    type: string
    description: 用户ID
  signature:
    type: string
    description: 数据签名
  clientTimestamp:
    type: integer
    format: int64
    description: 客户端时间戳
  socialPlatform:
    type: string
    description: 社交平台
    enum:
      - X
      - YouTube
  socialEvent:
    type: string
    description: 社交事件
    enum:
      - replay
      - watch
  events:
    type: array
    description: 事件列表
    items:
      $ref: 'EventData.yaml'
  deviceFinger:
    type: string
    description: 设备指纹
  clientIp:
    type: string
    description: 客户端IP
required:
  - sessionId
  - customerId
  - encryptedData
  - signature
  - clientTimestamp
  - events
