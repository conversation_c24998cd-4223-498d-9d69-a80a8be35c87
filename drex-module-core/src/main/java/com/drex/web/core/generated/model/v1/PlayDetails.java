package com.drex.web.core.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonTypeName;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * PlayDetails
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class PlayDetails implements EventDataDetails {

  /**
   * 播放状态
   */
  public enum NewStateEnum {
    PLAYING("PLAYING");

    private String value;

    NewStateEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static NewStateEnum fromValue(String value) {
      for (NewStateEnum b : NewStateEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  private NewStateEnum newState;

  private BigDecimal currentTime;

  private BigDecimal playbackRate;

  /**
   * Default constructor
   * @deprecated Use {@link PlayDetails#PlayDetails(NewStateEnum, BigDecimal, BigDecimal)}
   */
  @Deprecated
  public PlayDetails() {
    super();
  }

  /**
   * Constructor with only required parameters
   */
  public PlayDetails(NewStateEnum newState, BigDecimal currentTime, BigDecimal playbackRate) {
    this.newState = newState;
    this.currentTime = currentTime;
    this.playbackRate = playbackRate;
  }

  public PlayDetails newState(NewStateEnum newState) {
    this.newState = newState;
    return this;
  }

  /**
   * 播放状态
   * @return newState
  */
  @NotNull 
  @Schema(name = "newState", example = "PLAYING", description = "播放状态", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("newState")
  public NewStateEnum getNewState() {
    return newState;
  }

  public void setNewState(NewStateEnum newState) {
    this.newState = newState;
  }

  public PlayDetails currentTime(BigDecimal currentTime) {
    this.currentTime = currentTime;
    return this;
  }

  /**
   * 当前播放时间（秒）
   * @return currentTime
  */
  @NotNull @Valid 
  @Schema(name = "currentTime", example = "120.5", description = "当前播放时间（秒）", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("currentTime")
  public BigDecimal getCurrentTime() {
    return currentTime;
  }

  public void setCurrentTime(BigDecimal currentTime) {
    this.currentTime = currentTime;
  }

  public PlayDetails playbackRate(BigDecimal playbackRate) {
    this.playbackRate = playbackRate;
    return this;
  }

  /**
   * 播放速率
   * @return playbackRate
  */
  @NotNull @Valid 
  @Schema(name = "playbackRate", example = "1.0", description = "播放速率", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("playbackRate")
  public BigDecimal getPlaybackRate() {
    return playbackRate;
  }

  public void setPlaybackRate(BigDecimal playbackRate) {
    this.playbackRate = playbackRate;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PlayDetails playDetails = (PlayDetails) o;
    return Objects.equals(this.newState, playDetails.newState) &&
        Objects.equals(this.currentTime, playDetails.currentTime) &&
        Objects.equals(this.playbackRate, playDetails.playbackRate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(newState, currentTime, playbackRate);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PlayDetails {\n");
    sb.append("    newState: ").append(toIndentedString(newState)).append("\n");
    sb.append("    currentTime: ").append(toIndentedString(currentTime)).append("\n");
    sb.append("    playbackRate: ").append(toIndentedString(playbackRate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

