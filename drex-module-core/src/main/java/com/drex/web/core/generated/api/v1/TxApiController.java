package com.drex.web.core.generated.api.v1;

import com.drex.web.core.generated.model.v1.FaucetDripResponse;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.CookieValue;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.*;
import javax.validation.Valid;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
@Controller
@RequestMapping("${openapi.kweb.base-path:/v1}")
public class TxApiController implements TxApi {

    private final TxApiDelegate delegate;

    public TxApiController(@Autowired(required = false) TxApiDelegate delegate) {
        this.delegate = Optional.ofNullable(delegate).orElse(new TxApiDelegate() {});
    }

    @Override
    public TxApiDelegate getDelegate() {
        return delegate;
    }

}
