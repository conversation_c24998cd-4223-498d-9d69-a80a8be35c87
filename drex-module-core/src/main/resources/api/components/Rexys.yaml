type: object
properties:
  id:
    type: string
    description: 恐龙id
  code:
    type: string
    description: 恐龙编号
  image:
    type: string
    description: 图片
  miniImage:
    type: string
    description: 小图
  level:
    type: string
    description: level
  rate:
    type: integer
    description: 生产速率
  limit:
    type: integer
    description: 篮子上限
  status:
    type: integer
    description: 状态, 0:coming soon 1:lock 2:open
  has:
    type: boolean
    description: 是否拥有
  selected:
    type: boolean
    description: 是否选中

