package com.drex.web.core.delegate;

import com.drex.core.api.RemoteManageService;
import com.drex.core.api.request.NoticeDTO;
import com.drex.web.common.PassportHolder;
import com.drex.web.core.converter.CoreConverter;
import com.drex.web.core.generated.api.v1.NoticeApiDelegate;
import com.drex.web.core.generated.model.v1.NoticeResponse;
import com.kikitrade.framework.common.model.Response;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
public class NoticeApiDelegateImpl implements NoticeApiDelegate {

    @DubboReference
    private RemoteManageService remoteManageService;

    @Override
    public ResponseEntity<NoticeResponse> getNotices(Long lastFetchNotifyTime) {
        NoticeResponse noticeResponse = new NoticeResponse();
        String customerId = PassportHolder.passport().getPassportId();
        Response<List<NoticeDTO>> noticeListResponse = remoteManageService.getNotices(customerId, lastFetchNotifyTime);
        if (noticeListResponse.isSuccess()) {
            List<NoticeDTO> data = noticeListResponse.getData();
            noticeResponse = CoreConverter.toNoticeResponse(data);
            return ResponseEntity.ok(noticeResponse);
        } else {
            noticeResponse.fail(noticeListResponse.getCode(), noticeListResponse.getMessage());
            return ResponseEntity.badRequest().body(noticeResponse);
        }
    }
}
