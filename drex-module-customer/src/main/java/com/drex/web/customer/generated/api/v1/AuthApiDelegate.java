package com.drex.web.customer.generated.api.v1;

import com.drex.web.customer.generated.model.v1.AccessTokenResponse;
import com.drex.web.customer.generated.model.v1.BindWalletRequest;
import com.drex.web.customer.generated.model.v1.BindWalletResponse;
import com.drex.web.customer.generated.model.v1.ChallengeResponse;
import com.drex.web.customer.generated.model.v1.LoginRequest;
import com.drex.web.customer.generated.model.v1.LogoutResponse;
import com.drex.web.customer.generated.model.v1.ThirdAuthorizeRequest;
import com.drex.web.customer.generated.model.v1.ThirdAuthorizeResponse;
import com.drex.web.customer.generated.model.v1.ThirdBindingsResponse;
import com.drex.web.customer.generated.model.v1.WalletTypeEnum;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Generated;

/**
 * A delegate to be called by the {@link AuthApiController}}.
 * Implement this interface with a {@link org.springframework.stereotype.Service} annotated class.
 */
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public interface AuthApiDelegate {

    default Optional<NativeWebRequest> getRequest() {
        return Optional.empty();
    }

    /**
     * POST /auth/login : 登录
     * 登录
     *
     * @param loginRequest  (required)
     * @param osDeviceId 设备id (optional)
     * @return OK (status code 200)
     * @see AuthApi#authLogin
     */
    default ResponseEntity<AccessTokenResponse> authLogin(LoginRequest loginRequest,
        String osDeviceId) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /passport/wallet/bind : 绑定钱包
     * 绑定钱包
     *
     * @param bindWalletRequest  (optional)
     * @return OK (status code 200)
     * @see AuthApi#bindWallet
     */
    default ResponseEntity<BindWalletResponse> bindWallet(BindWalletRequest bindWalletRequest) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /auth/challenge : 获取一个密钥
     * 获取一个密钥
     *
     * @param address 地址 (required)
     * @param type 登录送wallet or social，绑定钱包送bind_wallet (optional, default to social)
     * @param walletType 钱包类型, 默认送EVM (optional)
     * @return OK (status code 200)
     * @see AuthApi#challenge
     */
    default ResponseEntity<ChallengeResponse> challenge(String address,
        String type,
        WalletTypeEnum walletType) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /auth/logout : 登出
     * 登出
     *
     * @return OK (status code 200)
     * @see AuthApi#logout
     */
    default ResponseEntity<LogoutResponse> logout() {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /auth/qr/login : 登录
     * 登录
     *
     * @param loginRequest  (required)
     * @param osDeviceId 设备id (optional)
     * @return OK (status code 200)
     * @see AuthApi#qrAuthLogin
     */
    default ResponseEntity<AccessTokenResponse> qrAuthLogin(LoginRequest loginRequest,
        String osDeviceId) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /auth/refresh : 刷新accessToken
     * 刷新
     *
     * @param accessToken accessToken (optional)
     * @param refreshToken refreshToken (optional)
     * @return OK (status code 200)
     * @see AuthApi#refreshAccessToken
     */
    default ResponseEntity<AccessTokenResponse> refreshAccessToken(String accessToken,
        String refreshToken) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /auth/third/authorize : /customer/auth/third/authorize
     * third platform authorize
     *
     * @param thirdAuthorizeRequest  (optional)
     * @return Successful operation (status code 200)
     * @see AuthApi#thirdAuthorize
     */
    default ResponseEntity<ThirdAuthorizeResponse> thirdAuthorize(ThirdAuthorizeRequest thirdAuthorizeRequest) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /auth/third/bindings : /customer/auth/third/bindings
     * third platform bind lists
     *
     * @param authorization authorization (optional)
     * @return OK (status code 200)
     * @see AuthApi#thirdBindings
     */
    default ResponseEntity<ThirdBindingsResponse> thirdBindings(String authorization) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

}
