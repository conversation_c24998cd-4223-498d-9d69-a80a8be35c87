package com.drex.web.common.utils;

import com.drex.web.common.JwtToken;
import com.drex.web.common.config.JwtProperties;
import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.security.KeyFactory;
import java.security.interfaces.ECPrivateKey;
import java.security.interfaces.ECPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.Date;
import java.util.concurrent.TimeUnit;

@Slf4j
public class JwtUtils {

    // 假设的密钥，实际使用中应妥善保管
    private static ECPrivateKey privateKey;
    private static ECPublicKey publicKey;

    public JwtUtils(JwtProperties jwtProperties) {
        try {
            // Parse private key
            String privateKeyPEM = jwtProperties.getPrivateKey()
                .replace("-----B<PERSON><PERSON> PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "")
                .replaceAll("\\s", "");

            byte[] privateKeyBytes = Base64.getDecoder().decode(privateKeyPEM);
            PKCS8EncodedKeySpec privateKeySpec = new PKCS8EncodedKeySpec(privateKeyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("EC");
            privateKey = (ECPrivateKey) keyFactory.generatePrivate(privateKeySpec);

            // Parse public key
            String publicKeyPEM = jwtProperties.getPublicKey()
                .replace("-----BEGIN PUBLIC KEY-----", "")
                .replace("-----END PUBLIC KEY-----", "")
                .replaceAll("\\s", "");

            byte[] publicKeyBytes = Base64.getDecoder().decode(publicKeyPEM);
            X509EncodedKeySpec publicKeySpec = new X509EncodedKeySpec(publicKeyBytes);
            publicKey = (ECPublicKey) keyFactory.generatePublic(publicKeySpec);

        } catch (Exception e) {
            log.error("Failed to initialize JWT keys", e);
            throw new RuntimeException("Failed to initialize JWT keys", e);
        }
    }

    /**
     * 生成 accessToken 和 refreshToken 对
     *
     * @param accessSubject 令牌的主题，可以是用户ID等信息
     * @return 包含 accessToken 和 refreshToken 的 Map
     */
    public static JwtToken generateTokenPair(String accessSubject, String refreshSubject) {
        // 生成 accessToken，1天过期
        String accessToken = generateToken(accessSubject, TimeUnit.DAYS.toMillis(1));
        // 生成 refreshToken，90天过期
        String refreshToken = generateToken(refreshSubject, TimeUnit.DAYS.toMillis(90));
        return JwtToken.builder().accessToken(accessToken).refreshToken(refreshToken).build();
    }

    /**
     * 验证 accessToken 是否有效
     *
     * @param accessToken 待验证的 accessToken
     * @return 如果 token 有效返回 true，否则返回 false
     */
    public static Jws<Claims> getAccessToken(String accessToken) {
        try {
            log.info("Access Token: {}", accessToken);
            return Jwts.parserBuilder()
                    .setSigningKey(publicKey)
                    .build()
                    .parseClaimsJws(accessToken);
        } catch (ExpiredJwtException e) {
            log.error("Access Token Expired:{}", e.getMessage(), e);
        } catch (UnsupportedJwtException e) {
            log.error("Unsupported Access Token:{} ",e.getMessage(), e);
        } catch (MalformedJwtException e) {
            log.error("Malformed Access Token 格式:{}" ,e.getMessage(), e);
        } catch (SignatureException e) {
            log.error("Signature Access Token 签名:{}", e.getMessage(), e);
        } catch (IllegalArgumentException e) {
            log.error("Access Token 为空:{}", e.getMessage(), e);
        }
        return null;
    }


    /**
     * 生成单个 JWT 令牌
     *
     * @param subject        令牌的主题
     * @param expirationTime 令牌的过期时间（毫秒）
     * @return 生成的 JWT 令牌
     */
    private static String generateToken(String subject, long expirationTime) {
        return Jwts.builder()
                .setSubject(subject)
                .setIssuedAt(new Date())
                .setIssuer("trex")
                .setExpiration(new Date(System.currentTimeMillis() + expirationTime))
                .signWith(privateKey, SignatureAlgorithm.ES256)
                .compact();
    }

    public static void main(String[] args) {
        // This main method needs to be updated or removed since the class is now Spring-managed
        // For testing, you should use proper Spring tests instead
    }
}
