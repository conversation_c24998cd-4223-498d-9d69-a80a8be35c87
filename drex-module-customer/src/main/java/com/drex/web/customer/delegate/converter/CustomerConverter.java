package com.drex.web.customer.delegate.converter;

import com.drex.customer.api.response.PassportConnectDTO;
import com.drex.web.customer.generated.model.v1.PassportConnectInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public class CustomerConverter {

    public static List<PassportConnectInfo> toPassportConnectInfo(List<PassportConnectDTO> data) {
        List<PassportConnectInfo> result = new ArrayList<>();
        if(CollectionUtils.isEmpty(data)){
            return result;
        }
        data.forEach(passportConnectDTO -> {
            PassportConnectInfo passportConnectInfo = new PassportConnectInfo();
            BeanUtils.copyProperties(passportConnectDTO, passportConnectInfo);
            result.add(passportConnectInfo);
        });
        return result;
    }
}
