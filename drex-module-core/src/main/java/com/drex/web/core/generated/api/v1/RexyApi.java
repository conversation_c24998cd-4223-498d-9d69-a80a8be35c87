/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (6.6.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
package com.drex.web.core.generated.api.v1;

import com.drex.web.core.generated.model.v1.ClaimBuildResponse;
import com.drex.web.core.generated.model.v1.ClaimResponse;
import com.drex.web.core.generated.model.v1.RewardCollectResponse;
import com.drex.web.core.generated.model.v1.RexyClaimRequest;
import com.drex.web.core.generated.model.v1.RexyInfoResponse;
import com.drex.web.core.generated.model.v1.RexyRewardCollectRequest;
import com.drex.web.core.generated.model.v1.RexyRewardLastResponse;
import com.drex.web.core.generated.model.v1.RexysResponse;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
@Validated
@Tag(name = "Rexy", description = "恐龙相关接口描述")
public interface RexyApi {

    default RexyApiDelegate getDelegate() {
        return new RexyApiDelegate() {};
    }

    /**
     * GET /rexys/me : 查询当前用户宠物信息
     * 查询当前用户宠物信息
     *
     * @return OK (status code 200)
     */
    @Operation(
        operationId = "rexy",
        summary = "查询当前用户宠物信息",
        description = "查询当前用户宠物信息",
        tags = { "Rexy" },
        responses = {
            @ApiResponse(responseCode = "200", description = "OK", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = RexyInfoResponse.class))
            })
        },
        security = {
            @SecurityRequirement(name = "jwtBearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/rexys/me",
        produces = { "application/json" }
    )
    default ResponseEntity<RexyInfoResponse> rexy(
        
    ) {
        return getDelegate().rexy();
    }


    /**
     * POST /rexys/claim : 领取积分奖励
     * 领取积分奖励
     *
     * @param rexyClaimRequest  (required)
     * @return OK (status code 200)
     */
    @Operation(
        operationId = "rexyClaim",
        summary = "领取积分奖励",
        description = "领取积分奖励",
        tags = { "Rexy" },
        responses = {
            @ApiResponse(responseCode = "200", description = "OK", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = ClaimResponse.class))
            })
        },
        security = {
            @SecurityRequirement(name = "jwtBearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/rexys/claim",
        produces = { "application/json" },
        consumes = { "application/json" }
    )
    default ResponseEntity<ClaimResponse> rexyClaim(
        @Parameter(name = "RexyClaimRequest", description = "", required = true) @Valid @RequestBody RexyClaimRequest rexyClaimRequest
    ) {
        return getDelegate().rexyClaim(rexyClaimRequest);
    }


    /**
     * POST /rexys/claim/build/{basketType} : 生成交易业务id
     * 生成交易业务id
     *
     * @param basketType 交易业务场景(normal, invite) (required)
     * @param address 钱包地址 (optional)
     * @return OK (status code 200)
     */
    @Operation(
        operationId = "rexyClaimBuild",
        summary = "生成交易业务id",
        description = "生成交易业务id",
        tags = { "Rexy" },
        responses = {
            @ApiResponse(responseCode = "200", description = "OK", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = ClaimBuildResponse.class))
            })
        },
        security = {
            @SecurityRequirement(name = "jwtBearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/rexys/claim/build/{basketType}",
        produces = { "application/json" }
    )
    default ResponseEntity<ClaimBuildResponse> rexyClaimBuild(
        @Parameter(name = "basketType", description = "交易业务场景(normal, invite)", required = true, in = ParameterIn.PATH) @PathVariable("basketType") String basketType,
        @Parameter(name = "address", description = "钱包地址", in = ParameterIn.QUERY) @Valid @RequestParam(value = "address", required = false) String address
    ) {
        return getDelegate().rexyClaimBuild(basketType, address);
    }


    /**
     * POST /rexys/reward/collect : 恐龙收集奖励
     * 恐龙收集奖励
     *
     * @param rexyRewardCollectRequest  (optional)
     * @return OK (status code 200)
     */
    @Operation(
        operationId = "rexyRewardCollect",
        summary = "恐龙收集奖励",
        description = "恐龙收集奖励",
        tags = { "Rexy" },
        responses = {
            @ApiResponse(responseCode = "200", description = "OK", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = RewardCollectResponse.class))
            })
        },
        security = {
            @SecurityRequirement(name = "jwtBearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/rexys/reward/collect",
        produces = { "application/json" },
        consumes = { "application/json" }
    )
    default ResponseEntity<RewardCollectResponse> rexyRewardCollect(
        @Parameter(name = "RexyRewardCollectRequest", description = "") @Valid @RequestBody(required = false) RexyRewardCollectRequest rexyRewardCollectRequest
    ) {
        return getDelegate().rexyRewardCollect(rexyRewardCollectRequest);
    }


    /**
     * GET /rexys/reward/last : 查询最新恐龙蛋
     * 查询最新恐龙蛋
     *
     * @param platform 平台，可选值：X 或 YouTube (required)
     * @param contentId 内容id (optional)
     * @return OK (status code 200)
     */
    @Operation(
        operationId = "rexyRewardLast",
        summary = "查询最新恐龙蛋",
        description = "查询最新恐龙蛋",
        tags = { "Rexy" },
        responses = {
            @ApiResponse(responseCode = "200", description = "OK", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = RexyRewardLastResponse.class))
            })
        },
        security = {
            @SecurityRequirement(name = "jwtBearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/rexys/reward/last",
        produces = { "application/json" }
    )
    default ResponseEntity<RexyRewardLastResponse> rexyRewardLast(
        @NotNull @Parameter(name = "platform", description = "平台，可选值：X 或 YouTube", required = true, in = ParameterIn.QUERY) @Valid @RequestParam(value = "platform", required = true) String platform,
        @Parameter(name = "content_id", description = "内容id", in = ParameterIn.QUERY) @Valid @RequestParam(value = "content_id", required = false) String contentId
    ) {
        return getDelegate().rexyRewardLast(platform, contentId);
    }


    /**
     * GET /rexys/avatar : 查询恐龙列表
     * 查询恐龙列表
     *
     * @return OK (status code 200)
     */
    @Operation(
        operationId = "rexys",
        summary = "查询恐龙列表",
        description = "查询恐龙列表",
        tags = { "Rexy" },
        responses = {
            @ApiResponse(responseCode = "200", description = "OK", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = RexysResponse.class))
            })
        },
        security = {
            @SecurityRequirement(name = "jwtBearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/rexys/avatar",
        produces = { "application/json" }
    )
    default ResponseEntity<RexysResponse> rexys(
        
    ) {
        return getDelegate().rexys();
    }

}
