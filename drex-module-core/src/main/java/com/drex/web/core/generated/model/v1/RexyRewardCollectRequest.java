package com.drex.web.core.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * RexyRewardCollectRequest
 */

@JsonTypeName("rexyRewardCollect_request")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class RexyRewardCollectRequest {

  private String rewardId;

  public RexyRewardCollectRequest rewardId(String rewardId) {
    this.rewardId = rewardId;
    return this;
  }

  /**
   * 奖励ID
   * @return rewardId
  */
  
  @Schema(name = "rewardId", description = "奖励ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("rewardId")
  public String getRewardId() {
    return rewardId;
  }

  public void setRewardId(String rewardId) {
    this.rewardId = rewardId;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RexyRewardCollectRequest rexyRewardCollectRequest = (RexyRewardCollectRequest) o;
    return Objects.equals(this.rewardId, rexyRewardCollectRequest.rewardId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(rewardId);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RexyRewardCollectRequest {\n");
    sb.append("    rewardId: ").append(toIndentedString(rewardId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

