/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (6.6.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
package com.drex.web.core.generated.api.v1;

import com.drex.web.core.generated.model.v1.FaucetDripResponse;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
@Validated
@Tag(name = "Tx", description = "the Tx API")
public interface TxApi {

    default TxApiDelegate getDelegate() {
        return new TxApiDelegate() {};
    }

    /**
     * GET /tx/faucet : 水龙头领水
     * 水龙头领水
     *
     * @param address 领取水的钱包地址 (required)
     * @return OK (status code 200)
     */
    @Operation(
        operationId = "faucetDrip",
        summary = "水龙头领水",
        description = "水龙头领水",
        tags = { "Tx" },
        responses = {
            @ApiResponse(responseCode = "200", description = "OK", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = FaucetDripResponse.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/tx/faucet",
        produces = { "application/json" }
    )
    default ResponseEntity<FaucetDripResponse> faucetDrip(
        @NotNull @Parameter(name = "address", description = "领取水的钱包地址", required = true, in = ParameterIn.QUERY) @Valid @RequestParam(value = "address", required = true) String address
    ) {
        return getDelegate().faucetDrip(address);
    }

}
