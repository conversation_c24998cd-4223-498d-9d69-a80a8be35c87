package com.drex.web.common.config;

import com.drex.web.common.utils.JwtUtils;
import com.drex.web.common.utils.UploadAliyunOssUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/26 16:28
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(JwtProperties.class)
public class WebConfiguration {

    @Bean(name = "uploadAliyunOssUtil")
    public UploadAliyunOssUtil uploadAliyunOssUtil(AliyunOssProperties aliyunOssProperties){
        log.info("init uploadAliyunOssUtil:{}", aliyunOssProperties);
        return new UploadAliyunOssUtil(aliyunOssProperties.getEndpoint(), aliyunOssProperties.getBucketName(), aliyunOssProperties.getRoleName());
    }

    @Bean(name = "jwtUtils")
    public JwtUtils jwtUtils(JwtProperties jwtProperties){
        return new JwtUtils(jwtProperties);
    }
}
