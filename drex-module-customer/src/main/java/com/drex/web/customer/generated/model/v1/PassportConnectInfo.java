package com.drex.web.customer.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * PassportConnectInfo
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class PassportConnectInfo {

  private String walletAddress;

  private String walletType;

  private String connectType;

  private String subConnectProvider;

  public PassportConnectInfo walletAddress(String walletAddress) {
    this.walletAddress = walletAddress;
    return this;
  }

  /**
   * 钱包地址
   * @return walletAddress
  */
  
  @Schema(name = "walletAddress", description = "钱包地址", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("walletAddress")
  public String getWalletAddress() {
    return walletAddress;
  }

  public void setWalletAddress(String walletAddress) {
    this.walletAddress = walletAddress;
  }

  public PassportConnectInfo walletType(String walletType) {
    this.walletType = walletType;
    return this;
  }

  /**
   * 钱包类型 eg. EVM / Solana / Social
   * @return walletType
  */
  
  @Schema(name = "walletType", description = "钱包类型 eg. EVM / Solana / Social", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("walletType")
  public String getWalletType() {
    return walletType;
  }

  public void setWalletType(String walletType) {
    this.walletType = walletType;
  }

  public PassportConnectInfo connectType(String connectType) {
    this.connectType = connectType;
    return this;
  }

  /**
   * 连接类型 eg. BIND / KEY
   * @return connectType
  */
  
  @Schema(name = "connectType", description = "连接类型 eg. BIND / KEY", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("connectType")
  public String getConnectType() {
    return connectType;
  }

  public void setConnectType(String connectType) {
    this.connectType = connectType;
  }

  public PassportConnectInfo subConnectProvider(String subConnectProvider) {
    this.subConnectProvider = subConnectProvider;
    return this;
  }

  /**
   * 子连接提供商 eg. google / facebook / apple ...
   * @return subConnectProvider
  */
  
  @Schema(name = "subConnectProvider", description = "子连接提供商 eg. google / facebook / apple ...", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("subConnectProvider")
  public String getSubConnectProvider() {
    return subConnectProvider;
  }

  public void setSubConnectProvider(String subConnectProvider) {
    this.subConnectProvider = subConnectProvider;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PassportConnectInfo passportConnectInfo = (PassportConnectInfo) o;
    return Objects.equals(this.walletAddress, passportConnectInfo.walletAddress) &&
        Objects.equals(this.walletType, passportConnectInfo.walletType) &&
        Objects.equals(this.connectType, passportConnectInfo.connectType) &&
        Objects.equals(this.subConnectProvider, passportConnectInfo.subConnectProvider);
  }

  @Override
  public int hashCode() {
    return Objects.hash(walletAddress, walletType, connectType, subConnectProvider);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PassportConnectInfo {\n");
    sb.append("    walletAddress: ").append(toIndentedString(walletAddress)).append("\n");
    sb.append("    walletType: ").append(toIndentedString(walletType)).append("\n");
    sb.append("    connectType: ").append(toIndentedString(connectType)).append("\n");
    sb.append("    subConnectProvider: ").append(toIndentedString(subConnectProvider)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

