package com.drex.web.core.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.drex.web.core.generated.model.v1.EventDataDetails;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * EventData
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class EventData {

  private String eventType;

  private Long timestamp;

  private Integer sequence;

  private EventDataDetails details;

  public EventData eventType(String eventType) {
    this.eventType = eventType;
    return this;
  }

  /**
   * 事件类型
   * @return eventType
  */
  
  @Schema(name = "eventType", description = "事件类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("eventType")
  public String getEventType() {
    return eventType;
  }

  public void setEventType(String eventType) {
    this.eventType = eventType;
  }

  public EventData timestamp(Long timestamp) {
    this.timestamp = timestamp;
    return this;
  }

  /**
   * 事件时间戳
   * @return timestamp
  */
  
  @Schema(name = "timestamp", description = "事件时间戳", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("timestamp")
  public Long getTimestamp() {
    return timestamp;
  }

  public void setTimestamp(Long timestamp) {
    this.timestamp = timestamp;
  }

  public EventData sequence(Integer sequence) {
    this.sequence = sequence;
    return this;
  }

  /**
   * 事件序号
   * @return sequence
  */
  
  @Schema(name = "sequence", description = "事件序号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("sequence")
  public Integer getSequence() {
    return sequence;
  }

  public void setSequence(Integer sequence) {
    this.sequence = sequence;
  }

  public EventData details(EventDataDetails details) {
    this.details = details;
    return this;
  }

  /**
   * Get details
   * @return details
  */
  @Valid 
  @Schema(name = "details", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("details")
  public EventDataDetails getDetails() {
    return details;
  }

  public void setDetails(EventDataDetails details) {
    this.details = details;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    EventData eventData = (EventData) o;
    return Objects.equals(this.eventType, eventData.eventType) &&
        Objects.equals(this.timestamp, eventData.timestamp) &&
        Objects.equals(this.sequence, eventData.sequence) &&
        Objects.equals(this.details, eventData.details);
  }

  @Override
  public int hashCode() {
    return Objects.hash(eventType, timestamp, sequence, details);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class EventData {\n");
    sb.append("    eventType: ").append(toIndentedString(eventType)).append("\n");
    sb.append("    timestamp: ").append(toIndentedString(timestamp)).append("\n");
    sb.append("    sequence: ").append(toIndentedString(sequence)).append("\n");
    sb.append("    details: ").append(toIndentedString(details)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

