package com.drex.web.common;

public interface Constant {

    public static final String STATISTICS_ORIGINAL_DATA = "statistics_original_data";

    enum ChallengeType {
        social,
        wallet,
        bind_wallet
    }

    enum SocialPlatform {
        X("twitter"),
        Discord("discord"),
        Telegram("telegram"),
        Line("line"),
        Google("google"),
        Apple("apple"),
        Facebook("facebook"),
        Email("email");

        private final String platformName;

        SocialPlatform(String platformName) {
            this.platformName = platformName;
        }

        public String getPlatformName() {
            return platformName;
        }

        // 根据平台名称获取对应的枚举值
        public static SocialPlatform fromPlatformName(String platformName) {
            for (SocialPlatform platform : SocialPlatform.values()) {
                if (platform.getPlatformName().equalsIgnoreCase(platformName)) {
                    return platform;
                }
            }
            return null;
        }

    }
}
