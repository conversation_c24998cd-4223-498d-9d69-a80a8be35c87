description: all nfts in series
type: object
properties:
  mintedCount:
    format: int64
    description: minted Count
    type: integer
  seriesLogo:
    description: nft series logo
    type: string
  claimableId:
    description: claimable id
    type: string
  type:
    description: nft type
    type: string
  tier:
    format: int32
    description: nft tier
    type: integer
  price:
    description: price
    type: string
  logo:
    description: nft gateway logo
    type: string
  reward:
    format: int32
    description: reward
    type: integer
  newTag:
    description: show new tag
    type: boolean
  process:
    description: nft complete process
    type: number
  address:
    description: nft contract address
    type: string
  tokenId:
    description: nft token id
    type: string
  level:
    description: level
    type: string
  seriesName:
    description: nft series name
    type: string
  totalSupply:
    format: int64
    description: total supply
    type: integer
  maxCond:
    description: nft condition max
    type: number
  claimTime:
    format: int64
    description: nft claimed time
    type: integer
  token:
    description: currency
    type: string
  series:
    description: nft series type
    type: string
  minCond:
    description: nft condition min
    type: number
  name:
    description: nft name
    type: string
  progress:
    description: nft conditions and progress
    type: array
    items:
      $ref: '../components/NftProgressVO.yaml'
  category:
    description: category
    type: string
  desc:
    description: nft desc
    type: string
  status:
    description: nft status(pending、claimable、claimed)
    type: string
