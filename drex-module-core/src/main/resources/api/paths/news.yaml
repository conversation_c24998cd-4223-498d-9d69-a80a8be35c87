get:
  tags:
    - News
  summary: 查询新闻列表
  description: 查询新闻列表
  operationId: news
  security:
    - jwtBearerAuth: [ ]
  parameters:
    - name: offset
      in: query
      description: 偏移量
      required: false
      schema:
        type: integer
        format: int64
        default: 0
    - name: limit
      in: query
      description: 查询数量
      required: false
      schema:
        type: integer
        format: int64
        default: 10
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            $ref: '../components/NewsInfoResponse.yaml'

