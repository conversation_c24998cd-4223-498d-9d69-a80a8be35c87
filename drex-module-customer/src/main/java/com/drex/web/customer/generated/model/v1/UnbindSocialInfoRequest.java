package com.drex.web.customer.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * UnbindSocialInfoRequest
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class UnbindSocialInfoRequest {

  private String customerId;

  private String platform;

  /**
   * Default constructor
   * @deprecated Use {@link UnbindSocialInfoRequest#UnbindSocialInfoRequest(String, String)}
   */
  @Deprecated
  public UnbindSocialInfoRequest() {
    super();
  }

  /**
   * Constructor with only required parameters
   */
  public UnbindSocialInfoRequest(String customerId, String platform) {
    this.customerId = customerId;
    this.platform = platform;
  }

  public UnbindSocialInfoRequest customerId(String customerId) {
    this.customerId = customerId;
    return this;
  }

  /**
   * Get customerId
   * @return customerId
  */
  @NotNull 
  @Schema(name = "customerId", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("customerId")
  public String getCustomerId() {
    return customerId;
  }

  public void setCustomerId(String customerId) {
    this.customerId = customerId;
  }

  public UnbindSocialInfoRequest platform(String platform) {
    this.platform = platform;
    return this;
  }

  /**
   * 平台
   * @return platform
  */
  @NotNull 
  @Schema(name = "platform", description = "平台", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("platform")
  public String getPlatform() {
    return platform;
  }

  public void setPlatform(String platform) {
    this.platform = platform;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    UnbindSocialInfoRequest unbindSocialInfoRequest = (UnbindSocialInfoRequest) o;
    return Objects.equals(this.customerId, unbindSocialInfoRequest.customerId) &&
        Objects.equals(this.platform, unbindSocialInfoRequest.platform);
  }

  @Override
  public int hashCode() {
    return Objects.hash(customerId, platform);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class UnbindSocialInfoRequest {\n");
    sb.append("    customerId: ").append(toIndentedString(customerId)).append("\n");
    sb.append("    platform: ").append(toIndentedString(platform)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

