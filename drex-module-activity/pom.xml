<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.drex</groupId>
        <artifactId>drex-web</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>drex-module-activity</artifactId>
    <packaging>jar</packaging>

    <name>drex-module-activity</name>
    <url>http://maven.apache.org</url>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.property.path>..</project.property.path>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.drex</groupId>
            <artifactId>core-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.drex</groupId>
            <artifactId>activity-model</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>3.8.1</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.drex</groupId>
            <artifactId>drex-module-common</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
    </dependencies>
    <profiles>
        <profile>
            <id>local</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.openapitools</groupId>
                        <artifactId>openapi-generator-maven-plugin</artifactId>
                        <version>6.6.0</version>
                        <executions>
                            <execution>
                                <id>activity</id>
                                <goals>
                                    <goal>generate</goal>
                                </goals>
                                <configuration>
                                    <inputSpec>${project.basedir}/src/main/resources/api/activity.yaml</inputSpec>
                                    <generatorName>spring</generatorName>
                                    <apiPackage>com.drex.web.activity.generated.api.v1</apiPackage>
                                    <modelPackage>com.drex.web.activity.generated.model.v1</modelPackage>
                                    <generateApiTests>false</generateApiTests>
                                    <generateModelTests>false</generateModelTests>
                                    <addTestCompileSourceRoot>false</addTestCompileSourceRoot>
                                    <output>.</output>
                                    <importMappings>
                                        <importMapping>WebResult=com.drex.web.common.WebResult</importMapping>
                                        <importMapping>Date=java.util.Date</importMapping>
                                        <importMapping>Map=java.util.Map</importMapping>
                                    </importMappings>
                                    <configOptions>
                                        <delegatePattern>true</delegatePattern>
                                        <hideGenerationTimestamp>true</hideGenerationTimestamp>
                                        <useTags>true</useTags>
                                    </configOptions>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>
