type: object
properties:
  id:
    type: string
    description: id
  type:
    type: string
    description: 类型 Article、News、YouTube、DApp、XPost
  name:
    type: string
    description: 名称
  logo:
    type: string
    description: logo链接
  title:
    type: string
    description: 标题
  subTitle:
    type: string
    description: 子标题
  image:
    type: string
    description: 图片
  link:
    type: string
    description: 外部链接
  category:
    type: array
    description: 标签
    items:
      type: string
  tag:
    type: string
    description: 来源icon图标
  date:
    type: integer
    description: 发布时间
    format: int64
  isRecommend:
    type: boolean
    description: 是否推荐置顶
  isReward:
    type: boolean
    description: 是否有奖励