package com.drex.web.core.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * FaucetDripResponseAllOfObj
 */

@JsonTypeName("FaucetDripResponse_allOf_obj")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class FaucetDripResponseAllOfObj {

  private String targetAddress;

  private String callData;

  private String amount;

  private String txHash;

  public FaucetDripResponseAllOfObj targetAddress(String targetAddress) {
    this.targetAddress = targetAddress;
    return this;
  }

  /**
   * 目标地址
   * @return targetAddress
  */
  
  @Schema(name = "targetAddress", description = "目标地址", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("targetAddress")
  public String getTargetAddress() {
    return targetAddress;
  }

  public void setTargetAddress(String targetAddress) {
    this.targetAddress = targetAddress;
  }

  public FaucetDripResponseAllOfObj callData(String callData) {
    this.callData = callData;
    return this;
  }

  /**
   * 调用数据
   * @return callData
  */
  
  @Schema(name = "callData", description = "调用数据", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("callData")
  public String getCallData() {
    return callData;
  }

  public void setCallData(String callData) {
    this.callData = callData;
  }

  public FaucetDripResponseAllOfObj amount(String amount) {
    this.amount = amount;
    return this;
  }

  /**
   * 金额
   * @return amount
  */
  
  @Schema(name = "amount", description = "金额", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("amount")
  public String getAmount() {
    return amount;
  }

  public void setAmount(String amount) {
    this.amount = amount;
  }

  public FaucetDripResponseAllOfObj txHash(String txHash) {
    this.txHash = txHash;
    return this;
  }

  /**
   * 交易哈希
   * @return txHash
  */
  
  @Schema(name = "txHash", description = "交易哈希", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("txHash")
  public String getTxHash() {
    return txHash;
  }

  public void setTxHash(String txHash) {
    this.txHash = txHash;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    FaucetDripResponseAllOfObj faucetDripResponseAllOfObj = (FaucetDripResponseAllOfObj) o;
    return Objects.equals(this.targetAddress, faucetDripResponseAllOfObj.targetAddress) &&
        Objects.equals(this.callData, faucetDripResponseAllOfObj.callData) &&
        Objects.equals(this.amount, faucetDripResponseAllOfObj.amount) &&
        Objects.equals(this.txHash, faucetDripResponseAllOfObj.txHash);
  }

  @Override
  public int hashCode() {
    return Objects.hash(targetAddress, callData, amount, txHash);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class FaucetDripResponseAllOfObj {\n");
    sb.append("    targetAddress: ").append(toIndentedString(targetAddress)).append("\n");
    sb.append("    callData: ").append(toIndentedString(callData)).append("\n");
    sb.append("    amount: ").append(toIndentedString(amount)).append("\n");
    sb.append("    txHash: ").append(toIndentedString(txHash)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

