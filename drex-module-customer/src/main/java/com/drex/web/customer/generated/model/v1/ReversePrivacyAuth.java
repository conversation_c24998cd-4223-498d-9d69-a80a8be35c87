package com.drex.web.customer.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * ReversePrivacyAuth
 */

@JsonTypeName("reversePrivacyAuth")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class ReversePrivacyAuth {

  private String platform;

  private String action;

  public ReversePrivacyAuth platform(String platform) {
    this.platform = platform;
    return this;
  }

  /**
   * 授权平台 X or YouTube
   * @return platform
  */
  
  @Schema(name = "platform", description = "授权平台 X or YouTube", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("platform")
  public String getPlatform() {
    return platform;
  }

  public void setPlatform(String platform) {
    this.platform = platform;
  }

  public ReversePrivacyAuth action(String action) {
    this.action = action;
    return this;
  }

  /**
   * reject 代表拒绝授权
   * @return action
  */
  
  @Schema(name = "action", description = "reject 代表拒绝授权", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("action")
  public String getAction() {
    return action;
  }

  public void setAction(String action) {
    this.action = action;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ReversePrivacyAuth reversePrivacyAuth = (ReversePrivacyAuth) o;
    return Objects.equals(this.platform, reversePrivacyAuth.platform) &&
        Objects.equals(this.action, reversePrivacyAuth.action);
  }

  @Override
  public int hashCode() {
    return Objects.hash(platform, action);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ReversePrivacyAuth {\n");
    sb.append("    platform: ").append(toIndentedString(platform)).append("\n");
    sb.append("    action: ").append(toIndentedString(action)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

