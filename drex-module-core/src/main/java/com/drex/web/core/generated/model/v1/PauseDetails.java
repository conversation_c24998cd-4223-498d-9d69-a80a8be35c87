package com.drex.web.core.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonTypeName;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * PauseDetails
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class PauseDetails implements EventDataDetails {

  /**
   * 播放状态
   */
  public enum NewStateEnum {
    PAUSED("PAUSED");

    private String value;

    NewStateEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static NewStateEnum fromValue(String value) {
      for (NewStateEnum b : NewStateEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  private NewStateEnum newState;

  private BigDecimal currentTime;

  /**
   * Default constructor
   * @deprecated Use {@link PauseDetails#PauseDetails(NewStateEnum, BigDecimal)}
   */
  @Deprecated
  public PauseDetails() {
    super();
  }

  /**
   * Constructor with only required parameters
   */
  public PauseDetails(NewStateEnum newState, BigDecimal currentTime) {
    this.newState = newState;
    this.currentTime = currentTime;
  }

  public PauseDetails newState(NewStateEnum newState) {
    this.newState = newState;
    return this;
  }

  /**
   * 播放状态
   * @return newState
  */
  @NotNull 
  @Schema(name = "newState", example = "PAUSED", description = "播放状态", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("newState")
  public NewStateEnum getNewState() {
    return newState;
  }

  public void setNewState(NewStateEnum newState) {
    this.newState = newState;
  }

  public PauseDetails currentTime(BigDecimal currentTime) {
    this.currentTime = currentTime;
    return this;
  }

  /**
   * 当前播放时间（秒）
   * @return currentTime
  */
  @NotNull @Valid 
  @Schema(name = "currentTime", example = "125.0", description = "当前播放时间（秒）", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("currentTime")
  public BigDecimal getCurrentTime() {
    return currentTime;
  }

  public void setCurrentTime(BigDecimal currentTime) {
    this.currentTime = currentTime;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PauseDetails pauseDetails = (PauseDetails) o;
    return Objects.equals(this.newState, pauseDetails.newState) &&
        Objects.equals(this.currentTime, pauseDetails.currentTime);
  }

  @Override
  public int hashCode() {
    return Objects.hash(newState, currentTime);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PauseDetails {\n");
    sb.append("    newState: ").append(toIndentedString(newState)).append("\n");
    sb.append("    currentTime: ").append(toIndentedString(currentTime)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

