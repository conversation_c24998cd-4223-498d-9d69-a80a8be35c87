get:
  summary: customers/queryBySocialInfo
  description: third platform bindInfo
  operationId: queryBySocialInfo
  security:
    - jwtBearerAuth: []
  tags:
    - Customer
  parameters:
    - name: app_key
      in: header
      description: app_key
      required: true
      example: trex
      schema:
        type: string
    - name: passportId
      in: query
      description: passportId
      required: false
      schema:
        type: string
    - name: platform
      in: query
      description: platform
      required: true
      schema:
        type: string
    - name: socialUserId
      in: query
      description: socialUserId
      required: false
      schema:
        type: string
    - name: socialHandleName
      in: query
      description: socialHandleName
      required: false
      schema:
        type: string
    - name: socialEmail
      in: query
      description: socialEmail
      required: false
      schema:
        type: string
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            $ref: '../components/QueryBySocialInfoResponse.yaml'