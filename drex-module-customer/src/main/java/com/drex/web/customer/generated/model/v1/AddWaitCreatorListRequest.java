package com.drex.web.customer.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.ArrayList;
import java.util.List;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * AddWaitCreatorListRequest
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class AddWaitCreatorListRequest {

  private String name;

  private String contactEmail;

  private String contactTelegram;

  private String contactDiscord;

  private String contactOther;

  private String socialsInstagram;

  private String socialsTwitter;

  private String socialsYoutube;

  private String socialsTiktok;

  private String socialsTelegram;

  private String socialsOther;

  @Valid
  private List<String> contentTypes;

  private String contentTypesOther;

  @Valid
  private List<String> interestReasons;

  private String interestReasonsOther;

  /**
   * Default constructor
   * @deprecated Use {@link AddWaitCreatorListRequest#AddWaitCreatorListRequest(String)}
   */
  @Deprecated
  public AddWaitCreatorListRequest() {
    super();
  }

  /**
   * Constructor with only required parameters
   */
  public AddWaitCreatorListRequest(String name) {
    this.name = name;
  }

  public AddWaitCreatorListRequest name(String name) {
    this.name = name;
    return this;
  }

  /**
   * 你的姓名
   * @return name
  */
  @NotNull 
  @Schema(name = "name", description = "你的姓名", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("name")
  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public AddWaitCreatorListRequest contactEmail(String contactEmail) {
    this.contactEmail = contactEmail;
    return this;
  }

  /**
   * 联系邮箱
   * @return contactEmail
  */
  @javax.validation.constraints.Email
  @Schema(name = "contactEmail", description = "联系邮箱", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("contactEmail")
  public String getContactEmail() {
    return contactEmail;
  }

  public void setContactEmail(String contactEmail) {
    this.contactEmail = contactEmail;
  }

  public AddWaitCreatorListRequest contactTelegram(String contactTelegram) {
    this.contactTelegram = contactTelegram;
    return this;
  }

  /**
   * 联系 Telegram 用户名
   * @return contactTelegram
  */
  
  @Schema(name = "contactTelegram", description = "联系 Telegram 用户名", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("contactTelegram")
  public String getContactTelegram() {
    return contactTelegram;
  }

  public void setContactTelegram(String contactTelegram) {
    this.contactTelegram = contactTelegram;
  }

  public AddWaitCreatorListRequest contactDiscord(String contactDiscord) {
    this.contactDiscord = contactDiscord;
    return this;
  }

  /**
   * 联系 Discord 用户名
   * @return contactDiscord
  */
  
  @Schema(name = "contactDiscord", description = "联系 Discord 用户名", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("contactDiscord")
  public String getContactDiscord() {
    return contactDiscord;
  }

  public void setContactDiscord(String contactDiscord) {
    this.contactDiscord = contactDiscord;
  }

  public AddWaitCreatorListRequest contactOther(String contactOther) {
    this.contactOther = contactOther;
    return this;
  }

  /**
   * 其他联系方式
   * @return contactOther
  */
  
  @Schema(name = "contactOther", description = "其他联系方式", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("contactOther")
  public String getContactOther() {
    return contactOther;
  }

  public void setContactOther(String contactOther) {
    this.contactOther = contactOther;
  }

  public AddWaitCreatorListRequest socialsInstagram(String socialsInstagram) {
    this.socialsInstagram = socialsInstagram;
    return this;
  }

  /**
   * Instagram 账号
   * @return socialsInstagram
  */
  
  @Schema(name = "socialsInstagram", description = "Instagram 账号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("socialsInstagram")
  public String getSocialsInstagram() {
    return socialsInstagram;
  }

  public void setSocialsInstagram(String socialsInstagram) {
    this.socialsInstagram = socialsInstagram;
  }

  public AddWaitCreatorListRequest socialsTwitter(String socialsTwitter) {
    this.socialsTwitter = socialsTwitter;
    return this;
  }

  /**
   * twitter 账号
   * @return socialsTwitter
  */
  
  @Schema(name = "socialsTwitter", description = "twitter 账号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("socialsTwitter")
  public String getSocialsTwitter() {
    return socialsTwitter;
  }

  public void setSocialsTwitter(String socialsTwitter) {
    this.socialsTwitter = socialsTwitter;
  }

  public AddWaitCreatorListRequest socialsYoutube(String socialsYoutube) {
    this.socialsYoutube = socialsYoutube;
    return this;
  }

  /**
   * youtube 账号
   * @return socialsYoutube
  */
  
  @Schema(name = "socialsYoutube", description = "youtube 账号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("socialsYoutube")
  public String getSocialsYoutube() {
    return socialsYoutube;
  }

  public void setSocialsYoutube(String socialsYoutube) {
    this.socialsYoutube = socialsYoutube;
  }

  public AddWaitCreatorListRequest socialsTiktok(String socialsTiktok) {
    this.socialsTiktok = socialsTiktok;
    return this;
  }

  /**
   * Tiktok 账号
   * @return socialsTiktok
  */
  
  @Schema(name = "socialsTiktok", description = "Tiktok 账号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("socialsTiktok")
  public String getSocialsTiktok() {
    return socialsTiktok;
  }

  public void setSocialsTiktok(String socialsTiktok) {
    this.socialsTiktok = socialsTiktok;
  }

  public AddWaitCreatorListRequest socialsTelegram(String socialsTelegram) {
    this.socialsTelegram = socialsTelegram;
    return this;
  }

  /**
   * Telegram 账号
   * @return socialsTelegram
  */
  
  @Schema(name = "socialsTelegram", description = "Telegram 账号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("socialsTelegram")
  public String getSocialsTelegram() {
    return socialsTelegram;
  }

  public void setSocialsTelegram(String socialsTelegram) {
    this.socialsTelegram = socialsTelegram;
  }

  public AddWaitCreatorListRequest socialsOther(String socialsOther) {
    this.socialsOther = socialsOther;
    return this;
  }

  /**
   * 其他账号
   * @return socialsOther
  */
  
  @Schema(name = "socialsOther", description = "其他账号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("socialsOther")
  public String getSocialsOther() {
    return socialsOther;
  }

  public void setSocialsOther(String socialsOther) {
    this.socialsOther = socialsOther;
  }

  public AddWaitCreatorListRequest contentTypes(List<String> contentTypes) {
    this.contentTypes = contentTypes;
    return this;
  }

  public AddWaitCreatorListRequest addContentTypesItem(String contentTypesItem) {
    if (this.contentTypes == null) {
      this.contentTypes = new ArrayList<>();
    }
    this.contentTypes.add(contentTypesItem);
    return this;
  }

  /**
   * 你创作的内容类型，可多选
   * @return contentTypes
  */
  
  @Schema(name = "contentTypes", description = "你创作的内容类型，可多选", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("contentTypes")
  public List<String> getContentTypes() {
    return contentTypes;
  }

  public void setContentTypes(List<String> contentTypes) {
    this.contentTypes = contentTypes;
  }

  public AddWaitCreatorListRequest contentTypesOther(String contentTypesOther) {
    this.contentTypesOther = contentTypesOther;
    return this;
  }

  /**
   * 若内容类型选择 'Others'，请填写具体内容
   * @return contentTypesOther
  */
  
  @Schema(name = "contentTypesOther", description = "若内容类型选择 'Others'，请填写具体内容", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("contentTypesOther")
  public String getContentTypesOther() {
    return contentTypesOther;
  }

  public void setContentTypesOther(String contentTypesOther) {
    this.contentTypesOther = contentTypesOther;
  }

  public AddWaitCreatorListRequest interestReasons(List<String> interestReasons) {
    this.interestReasons = interestReasons;
    return this;
  }

  public AddWaitCreatorListRequest addInterestReasonsItem(String interestReasonsItem) {
    if (this.interestReasons == null) {
      this.interestReasons = new ArrayList<>();
    }
    this.interestReasons.add(interestReasonsItem);
    return this;
  }

  /**
   * 你有兴趣加入 Trex 创作者计划的原因，可多选
   * @return interestReasons
  */
  
  @Schema(name = "interestReasons", description = "你有兴趣加入 Trex 创作者计划的原因，可多选", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("interestReasons")
  public List<String> getInterestReasons() {
    return interestReasons;
  }

  public void setInterestReasons(List<String> interestReasons) {
    this.interestReasons = interestReasons;
  }

  public AddWaitCreatorListRequest interestReasonsOther(String interestReasonsOther) {
    this.interestReasonsOther = interestReasonsOther;
    return this;
  }

  /**
   * 若原因选择 'Others'，请填写具体内容
   * @return interestReasonsOther
  */
  
  @Schema(name = "interestReasonsOther", description = "若原因选择 'Others'，请填写具体内容", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("interestReasonsOther")
  public String getInterestReasonsOther() {
    return interestReasonsOther;
  }

  public void setInterestReasonsOther(String interestReasonsOther) {
    this.interestReasonsOther = interestReasonsOther;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    AddWaitCreatorListRequest addWaitCreatorListRequest = (AddWaitCreatorListRequest) o;
    return Objects.equals(this.name, addWaitCreatorListRequest.name) &&
        Objects.equals(this.contactEmail, addWaitCreatorListRequest.contactEmail) &&
        Objects.equals(this.contactTelegram, addWaitCreatorListRequest.contactTelegram) &&
        Objects.equals(this.contactDiscord, addWaitCreatorListRequest.contactDiscord) &&
        Objects.equals(this.contactOther, addWaitCreatorListRequest.contactOther) &&
        Objects.equals(this.socialsInstagram, addWaitCreatorListRequest.socialsInstagram) &&
        Objects.equals(this.socialsTwitter, addWaitCreatorListRequest.socialsTwitter) &&
        Objects.equals(this.socialsYoutube, addWaitCreatorListRequest.socialsYoutube) &&
        Objects.equals(this.socialsTiktok, addWaitCreatorListRequest.socialsTiktok) &&
        Objects.equals(this.socialsTelegram, addWaitCreatorListRequest.socialsTelegram) &&
        Objects.equals(this.socialsOther, addWaitCreatorListRequest.socialsOther) &&
        Objects.equals(this.contentTypes, addWaitCreatorListRequest.contentTypes) &&
        Objects.equals(this.contentTypesOther, addWaitCreatorListRequest.contentTypesOther) &&
        Objects.equals(this.interestReasons, addWaitCreatorListRequest.interestReasons) &&
        Objects.equals(this.interestReasonsOther, addWaitCreatorListRequest.interestReasonsOther);
  }

  @Override
  public int hashCode() {
    return Objects.hash(name, contactEmail, contactTelegram, contactDiscord, contactOther, socialsInstagram, socialsTwitter, socialsYoutube, socialsTiktok, socialsTelegram, socialsOther, contentTypes, contentTypesOther, interestReasons, interestReasonsOther);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class AddWaitCreatorListRequest {\n");
    sb.append("    name: ").append(toIndentedString(name)).append("\n");
    sb.append("    contactEmail: ").append(toIndentedString(contactEmail)).append("\n");
    sb.append("    contactTelegram: ").append(toIndentedString(contactTelegram)).append("\n");
    sb.append("    contactDiscord: ").append(toIndentedString(contactDiscord)).append("\n");
    sb.append("    contactOther: ").append(toIndentedString(contactOther)).append("\n");
    sb.append("    socialsInstagram: ").append(toIndentedString(socialsInstagram)).append("\n");
    sb.append("    socialsTwitter: ").append(toIndentedString(socialsTwitter)).append("\n");
    sb.append("    socialsYoutube: ").append(toIndentedString(socialsYoutube)).append("\n");
    sb.append("    socialsTiktok: ").append(toIndentedString(socialsTiktok)).append("\n");
    sb.append("    socialsTelegram: ").append(toIndentedString(socialsTelegram)).append("\n");
    sb.append("    socialsOther: ").append(toIndentedString(socialsOther)).append("\n");
    sb.append("    contentTypes: ").append(toIndentedString(contentTypes)).append("\n");
    sb.append("    contentTypesOther: ").append(toIndentedString(contentTypesOther)).append("\n");
    sb.append("    interestReasons: ").append(toIndentedString(interestReasons)).append("\n");
    sb.append("    interestReasonsOther: ").append(toIndentedString(interestReasonsOther)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

