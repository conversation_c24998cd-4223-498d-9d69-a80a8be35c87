package com.drex.web;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.servers.Server;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication(scanBasePackages = {"com.drex", "com.kikitrade.gateway"})
@OpenAPIDefinition(
        info = @Info(title = "trex api", version = "1.0"),
        servers = {
                @Server(url = "https://api.trex.dev.dipbit.xyz", description = "http Server")
        }
)
public class DrexWebApplication {

    public static void main(String[] args) {
        SpringApplication.run(DrexWebApplication.class, args);
    }

}
