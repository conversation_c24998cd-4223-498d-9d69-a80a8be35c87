package com.drex.web.core.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.drex.web.core.generated.model.v1.ExtraRewardInfo;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.ArrayList;
import java.util.List;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * RewardCollect
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class RewardCollect {

  private String rexyRewardLevel;

  private String rexyRewardId;

  private String rexyRewardPoint;

  @Valid
  private List<@Valid ExtraRewardInfo> extraRewards;

  public RewardCollect rexyRewardLevel(String rexyRewardLevel) {
    this.rexyRewardLevel = rexyRewardLevel;
    return this;
  }

  /**
   * Get rexyRewardLevel
   * @return rexyRewardLevel
  */
  
  @Schema(name = "rexyRewardLevel", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("rexyRewardLevel")
  public String getRexyRewardLevel() {
    return rexyRewardLevel;
  }

  public void setRexyRewardLevel(String rexyRewardLevel) {
    this.rexyRewardLevel = rexyRewardLevel;
  }

  public RewardCollect rexyRewardId(String rexyRewardId) {
    this.rexyRewardId = rexyRewardId;
    return this;
  }

  /**
   * Get rexyRewardId
   * @return rexyRewardId
  */
  
  @Schema(name = "rexyRewardId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("rexyRewardId")
  public String getRexyRewardId() {
    return rexyRewardId;
  }

  public void setRexyRewardId(String rexyRewardId) {
    this.rexyRewardId = rexyRewardId;
  }

  public RewardCollect rexyRewardPoint(String rexyRewardPoint) {
    this.rexyRewardPoint = rexyRewardPoint;
    return this;
  }

  /**
   * Get rexyRewardPoint
   * @return rexyRewardPoint
  */
  
  @Schema(name = "rexyRewardPoint", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("rexyRewardPoint")
  public String getRexyRewardPoint() {
    return rexyRewardPoint;
  }

  public void setRexyRewardPoint(String rexyRewardPoint) {
    this.rexyRewardPoint = rexyRewardPoint;
  }

  public RewardCollect extraRewards(List<@Valid ExtraRewardInfo> extraRewards) {
    this.extraRewards = extraRewards;
    return this;
  }

  public RewardCollect addExtraRewardsItem(ExtraRewardInfo extraRewardsItem) {
    if (this.extraRewards == null) {
      this.extraRewards = new ArrayList<>();
    }
    this.extraRewards.add(extraRewardsItem);
    return this;
  }

  /**
   * Get extraRewards
   * @return extraRewards
  */
  @Valid 
  @Schema(name = "extraRewards", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("extraRewards")
  public List<@Valid ExtraRewardInfo> getExtraRewards() {
    return extraRewards;
  }

  public void setExtraRewards(List<@Valid ExtraRewardInfo> extraRewards) {
    this.extraRewards = extraRewards;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RewardCollect rewardCollect = (RewardCollect) o;
    return Objects.equals(this.rexyRewardLevel, rewardCollect.rexyRewardLevel) &&
        Objects.equals(this.rexyRewardId, rewardCollect.rexyRewardId) &&
        Objects.equals(this.rexyRewardPoint, rewardCollect.rexyRewardPoint) &&
        Objects.equals(this.extraRewards, rewardCollect.extraRewards);
  }

  @Override
  public int hashCode() {
    return Objects.hash(rexyRewardLevel, rexyRewardId, rexyRewardPoint, extraRewards);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RewardCollect {\n");
    sb.append("    rexyRewardLevel: ").append(toIndentedString(rexyRewardLevel)).append("\n");
    sb.append("    rexyRewardId: ").append(toIndentedString(rexyRewardId)).append("\n");
    sb.append("    rexyRewardPoint: ").append(toIndentedString(rexyRewardPoint)).append("\n");
    sb.append("    extraRewards: ").append(toIndentedString(extraRewards)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

