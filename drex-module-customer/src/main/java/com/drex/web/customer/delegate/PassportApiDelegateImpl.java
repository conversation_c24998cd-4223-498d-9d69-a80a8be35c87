package com.drex.web.customer.delegate;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.model.OSSObject;
import com.drex.customer.api.RemotePassportService;
import com.drex.customer.api.response.PassportConnectDTO;
import com.drex.customer.api.response.PassportDTO;
import com.drex.web.common.ErrorCode;
import com.drex.web.common.JwtHolder;
import com.drex.web.common.PassportHolder;
import com.drex.web.common.config.WebProperties;
import com.drex.web.common.utils.UploadAliyunOssUtil;
import com.drex.web.customer.delegate.converter.CustomerConverter;
import com.drex.web.customer.delegate.model.anchor.AnchorConstants;
import com.drex.web.customer.generated.api.v1.PassportApiDelegate;
import com.drex.web.customer.generated.model.v1.NftVO;
import com.drex.web.customer.generated.model.v1.PassportConnectResponse;
import com.drex.web.customer.generated.model.v1.PassportInfo;
import com.drex.web.customer.generated.model.v1.PassportResponse;
import com.drex.web.customer.generated.model.v1.SetHandleNameResponse;
import com.kikitrade.framework.common.model.Response;
import com.kikitrade.gateway.client.QuestsApi;
import com.kikitrade.gateway.client.model.WebResultAssets;
import jakarta.annotation.Resource;
import kotlin.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.Duration;
import java.util.*;

@Slf4j
@Service
public class PassportApiDelegateImpl implements PassportApiDelegate {

    private static final int TIMEOUT_MILLISECONDS = 5000;

    @Resource
    private WebProperties webProperties;
    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private UploadAliyunOssUtil uploadAliyunOssUtil;
    @DubboReference
    private RemotePassportService remotePassportService;
    @Resource
    private QuestsApi questsApi;


    /**
     * GET /passport/me : 查询用户passport信息
     * 返回用户的passport基本信息，包括customerId、created时间和handleName
     *
     * @return 成功获取passport信息 (status code 200)
     */
    @Override
    public ResponseEntity<PassportResponse> getPassportInfo() {
        PassportDTO passport = PassportHolder.passport();
        if (passport == null) {
            log.error("getPassportInfo passport is null");
            return ResponseEntity.ok(null);
        }

        PassportResponse response = new PassportResponse();
        response.success();

        PassportInfo passportInfo = new PassportInfo();
        passportInfo.setPassportId(passport.getPassportId());
        passportInfo.setUserName(passport.getUsername());
        passportInfo.setHandleName(passport.getHandleName());
        passportInfo.setCreated(passport.getCreatedAt().getTime());
        passportInfo.setAddresses(passport.getAddress());
        passportInfo.setInviteCode(passport.getReferralCode());
        passportInfo.setEmail(passport.getEmail());
        passportInfo.setAvatar(passport.getAvatar());
        passportInfo.setPassportChainId(passport.getPassportChainId() == null ? 0 : Integer.valueOf(passport.getPassportChainId()));
        passportInfo.setContractAddress(passport.getContractAddress());
        passportInfo.setHoldTitles(Collections.singletonList("Crypto OG"));
        passportInfo.setBadges(getSeriesNfts(JwtHolder.jwt()));
        try{
            WebResultAssets point = questsApi.saas(webProperties.getSaasId()).member().assetsByCid(webProperties.getSaasId(), passport.getPassportId(), "POINT");
            if(point.getObj() != null){
                passportInfo.setPoint(point.getObj().getAvailable().longValue());
            }
        }catch (Exception e){
            log.error("get point error", e);
            passportInfo.setPoint(0L);
        }
        response.setObj(passportInfo);
        return ResponseEntity.ok(response);
    }

    @Override
    public ResponseEntity<PassportConnectResponse> getPassportConnect() {
        PassportDTO passport = PassportHolder.passport();
        if (passport == null) {
            log.error("getPassportConnect passport is null");
            return ResponseEntity.ok(null);
        }
        Response<List<PassportConnectDTO>> passportConnect = remotePassportService.getPassportConnect(passport.getPassportId());
        if (!passportConnect.isSuccess()) {
            return ResponseEntity.ok(null);
        }

        List<PassportConnectDTO> data = passportConnect.getData();
        log.info("getPassportConnect data:{}", data);

        PassportConnectResponse passportConnectResponse = new PassportConnectResponse();
        passportConnectResponse.setSuccess(true);
        passportConnectResponse.setObj(CustomerConverter.toPassportConnectInfo(data));
        return ResponseEntity.ok(passportConnectResponse);
    }

    @Override
    public ResponseEntity<SetHandleNameResponse> setHandleName(String handleName) {
        SetHandleNameResponse setHandleNameResponse = new SetHandleNameResponse();
        PassportDTO passportDTO = PassportHolder.passport();
        if (StringUtils.isNotBlank(passportDTO.getHandleName())) {
            setHandleNameResponse.setSuccess(false);
            setHandleNameResponse.setCode(ErrorCode.REPEAT_OPERATION.getCode());
            setHandleNameResponse.setMessage(ErrorCode.REPEAT_OPERATION.getMessage());
            return ResponseEntity.ok(setHandleNameResponse);
        }

        Pair<Boolean, ErrorCode> booleanErrorCodePair = checkHandleNameValid(handleName);
        if (!booleanErrorCodePair.getFirst()) {
            setHandleNameResponse.setSuccess(false);
            setHandleNameResponse.setCode(booleanErrorCodePair.getSecond().getCode());
            setHandleNameResponse.setMessage(booleanErrorCodePair.getSecond().getMessage());
            return ResponseEntity.ok(setHandleNameResponse);
        }
        Response<Boolean> booleanResponse = remotePassportService.updateHandleName(passportDTO.getPassportId(), handleName);
        if (!booleanResponse.isSuccess()) {
            setHandleNameResponse.setSuccess(false);
            setHandleNameResponse.setMessage("update handleName failed");
            return ResponseEntity.ok(setHandleNameResponse);
        }

        setHandleNameResponse.setSuccess(booleanResponse.getData());
        setHandleNameResponse.setMessage("success");
        return ResponseEntity.ok(setHandleNameResponse);
    }

    private Pair<Boolean, ErrorCode> checkHandleNameValid(String handleName) {
        if (StringUtils.isEmpty(handleName) || handleName.length() < 6 || handleName.length() > 20 || !handleName.matches("^[a-zA-Z0-9]+$")) {
            return new Pair<>(false, ErrorCode.PARAMS_ILLEGAL);
        }
        if (containSensitiveWords(handleName.toLowerCase(Locale.ROOT))) {
            return new Pair<>(false, ErrorCode.SENSITIVE_WORDS);
        }
        Response<PassportDTO> byHandleName = remotePassportService.getByHandleName(handleName);
        if (byHandleName.isSuccess()) {
            if (byHandleName.getData() != null) {
                return new Pair<>(false, ErrorCode.HANDLE_NAME_EXISTS);
            }
        }
        return new Pair<>(true, null);
    }

    private boolean containSensitiveWords(String handleName) {
        try {
            log.info("containSensitiveWords handleName {}", handleName);
            String sensitiveWords = redisTemplate.opsForValue().get("sensitiveWords");
            if (StringUtils.isBlank(sensitiveWords)) {
                sensitiveWords = getSensitiveWords();
                log.info("getSensitiveWords sensitiveWords {}", sensitiveWords);
                redisTemplate.opsForValue().set("sensitiveWords", sensitiveWords, Duration.ofHours(1));
            }
            log.info("sensitiveWords {}", sensitiveWords);
            String[] words = sensitiveWords.split(",");
            for (String word : words) {
                if (handleName.contains(word)) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("getSensitiveWords failed", e);
        }
        return false;
    }

    private String getSensitiveWords() {
        OSSObject object = uploadAliyunOssUtil.getObject("drex/trex/SensitiveWords.txt");
        if (object != null) {
            try (InputStream inputStream = object.getObjectContent();
                 ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
                byte[] readBuffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(readBuffer)) != -1) {
                    byteArrayOutputStream.write(readBuffer, 0, bytesRead);
                }
                byte[] fileBytes = byteArrayOutputStream.toByteArray();

                String sensitiveWords = new String(fileBytes, "UTF-8");
                log.info("origin sensitiveWords {}", sensitiveWords);
                List<String>  words = List.of(sensitiveWords.split("\n"));

                StringBuilder sb = new StringBuilder();
                for (String word : words) {
                    sb.append(word.toLowerCase(Locale.ROOT)).append(",");
                }
                String sensitiveWordsStr = sb.toString();
                if (sensitiveWordsStr.endsWith(",")) {
                    sensitiveWordsStr = sensitiveWordsStr.substring(0, sensitiveWordsStr.length() - 1);
                }
                return sensitiveWordsStr;

            } catch (Exception e) {
                log.error("Error reading sensitive words from Excel", e);
            } finally {
                try {
                    object.close();
                } catch (IOException e) {
                    log.error("Error closing OSS object", e);
                }
            }
        } else {
            log.error("Error getting sensitive words from Excel");
        }
        return "";
    }


    public List<NftVO> getSeriesNfts(String jwt) {
        String url = webProperties.getAnchorHost() + String.format("/v1/badges?strategy=%s&status=%s", "TIME", "claimable");
        HttpRequest request = HttpUtil.createGet(url).addHeaders(buildHeaders(jwt)).timeout(TIMEOUT_MILLISECONDS);
        HttpResponse response = request.execute();
        if (response.getStatus() == HttpStatus.HTTP_OK) {
            log.info("getSeriesNfts from anchor, response:{}", response.body());
            if (StringUtils.isNoneBlank(response.body())) {
                JSONObject bodyObj = JSON.parseObject(response.body());
                if ("0000".equals(bodyObj.getString("code"))) {
                    JSONObject obj = bodyObj.getJSONObject("obj");
                    JSONArray rows = obj.getJSONArray("rows");
                    return rows.toJavaList(NftVO.class);
                }
            }
        }
        log.error("getSeriesNfts failed, response:{}", response);
        return new ArrayList<>();
    }

    private Map<String, String> buildHeaders(String accessToken) {
        Map<String, String> headers = new HashMap<>();
        headers.put(AnchorConstants.HEADER_JWT_TOKEN, accessToken);
        headers.put(AnchorConstants.HEADER_CLIENT_ID, webProperties.getAnchorClientId());
        headers.put(AnchorConstants.HEADER_CHAIN_ID, String.valueOf(webProperties.getChainId()));
        log.info("send anchor api headers:{}", JSONObject.toJSONString(headers));
        return headers;
    }
}
