/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (6.6.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
package com.drex.web.customer.generated.api.v1;

import com.drex.web.customer.generated.model.v1.PassportConnectResponse;
import com.drex.web.customer.generated.model.v1.PassportResponse;
import com.drex.web.customer.generated.model.v1.SetHandleNameResponse;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
@Validated
@Tag(name = "Passport", description = "用户相关接口描述")
public interface PassportApi {

    default PassportApiDelegate getDelegate() {
        return new PassportApiDelegate() {};
    }

    /**
     * GET /passport/connect : 查询用户passportConnect钱包连接信息
     * 返回用户的passportConnect钱包连接信息
     *
     * @return 成功获取passportConnect (status code 200)
     */
    @Operation(
        operationId = "getPassportConnect",
        summary = "查询用户passportConnect钱包连接信息",
        description = "返回用户的passportConnect钱包连接信息",
        tags = { "Passport" },
        responses = {
            @ApiResponse(responseCode = "200", description = "成功获取passportConnect", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = PassportConnectResponse.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/passport/connect",
        produces = { "application/json" }
    )
    default ResponseEntity<PassportConnectResponse> getPassportConnect(
        
    ) {
        return getDelegate().getPassportConnect();
    }


    /**
     * GET /passport/me : 查询用户passport信息
     * 返回用户的passport基本信息，包括customerId、created时间和handleName
     *
     * @return 成功获取passport信息 (status code 200)
     */
    @Operation(
        operationId = "getPassportInfo",
        summary = "查询用户passport信息",
        description = "返回用户的passport基本信息，包括customerId、created时间和handleName",
        tags = { "Passport" },
        responses = {
            @ApiResponse(responseCode = "200", description = "成功获取passport信息", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = PassportResponse.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/passport/me",
        produces = { "application/json" }
    )
    default ResponseEntity<PassportResponse> getPassportInfo(
        
    ) {
        return getDelegate().getPassportInfo();
    }


    /**
     * POST /passport/setHandleName : 保存用户handleName信息
     * 保存用户handleName信息
     *
     * @param handleName handleName (required)
     * @return 保存用户handleName信息 (status code 200)
     */
    @Operation(
        operationId = "setHandleName",
        summary = "保存用户handleName信息",
        description = "保存用户handleName信息",
        tags = { "Passport" },
        responses = {
            @ApiResponse(responseCode = "200", description = "保存用户handleName信息", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = SetHandleNameResponse.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/passport/setHandleName",
        produces = { "application/json" }
    )
    default ResponseEntity<SetHandleNameResponse> setHandleName(
        @NotNull @Parameter(name = "handleName", description = "handleName", required = true, in = ParameterIn.QUERY) @Valid @RequestParam(value = "handleName", required = true) String handleName
    ) {
        return getDelegate().setHandleName(handleName);
    }

}
