package com.drex.web.core.delegate;

import com.drex.core.api.RemoteVideoAntiCheatService;
import com.drex.web.common.PassportHolder;
import com.drex.web.common.context.RequestContextHolder;
import com.drex.web.core.generated.api.v1.VideoApiDelegate;
import com.drex.web.core.generated.model.v1.*;
import com.kikitrade.framework.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class VideoApiDelegateImpl implements VideoApiDelegate {

    @DubboReference
    private RemoteVideoAntiCheatService remoteVideoAntiCheatService;

    @Override
    public ResponseEntity<VideoSessionInitResponse> initVideoSession(VideoSessionInitRequest videoSessionInitRequest) {
        com.drex.core.api.request.VideoSessionInitRequest videoSessionInitRequest1 = new com.drex.core.api.request.VideoSessionInitRequest();
        videoSessionInitRequest1.setVideoId(videoSessionInitRequest.getVideoId());
        videoSessionInitRequest1.setPlatform(videoSessionInitRequest.getPlatform());
        videoSessionInitRequest1.setCustomerId(PassportHolder.passport().getPassportId());
        videoSessionInitRequest1.setClientIp(RequestContextHolder.getClientIp());
        Response<com.drex.core.api.response.VideoSessionInitResponse> response = remoteVideoAntiCheatService.initVideoSession(videoSessionInitRequest1);
        VideoSessionInitResponse webResponse = new VideoSessionInitResponse();
        webResponse.setSuccess(true);
        VideoSessionInit videoSessionInit = new VideoSessionInit();
        if(response.isSuccess() && response.getData() != null) {
            com.drex.core.api.response.VideoSessionInitResponse data = response.getData();
            videoSessionInit.setSessionId(data.getSessionId());
            videoSessionInit.setReportKey(data.getReportKey());
            videoSessionInit.setHasReward(!CollectionUtils.isEmpty(data.getRewardStages()));
            List<VideoSessionInitStageRangesInner> rewardStages = new ArrayList<>();
            data.getRewardStages().forEach(rewardStage -> {
                VideoSessionInitStageRangesInner rewardStage1 = new VideoSessionInitStageRangesInner();
                rewardStage1.setName(rewardStage.getStageName());
                rewardStage1.setRewardAmount(rewardStage.getRewardAmount());
                rewardStage1.setMinRatio(rewardStage.getRequiredWatchMinPercentage());
                rewardStage1.setMaxRatio(rewardStage.getRequiredWatchMaxPercentage());
                rewardStages.add(rewardStage1);
            });
            videoSessionInit.setStageRanges(rewardStages);
            webResponse.setObj(videoSessionInit);
            return ResponseEntity.ok(webResponse);
        }
        videoSessionInit.setHasReward(false);
        webResponse.setObj(videoSessionInit);
        return ResponseEntity.ok(webResponse);
    }

    @Override
    public ResponseEntity<Object> reportEvents(VideoEventsReportRequest videoEventsReportRequest) {
        return VideoApiDelegate.super.reportEvents(videoEventsReportRequest);
    }
}
