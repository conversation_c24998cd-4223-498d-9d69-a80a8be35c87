package com.drex.web.customer.delegate.model.anchor;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BadgeAssetsResult implements Serializable {

    private boolean success;

    private String code;

    private String msgKey;

    private BadgeAssetsDTO obj;

    @Data
    public static class BadgeAssetsDTO {
        private List<BadgeAssets> rows;
    }

    @Data
    public static class BadgeAssets {
        private String customerId;
        private List<NftDTO> nfts;
    }

    @Data
    public static class NftDTO {
        private String address;
        private String series;
        private String seriesName;
        private Integer tier;
        private String logo;
        private String name;
        private String desc;
        private String status;
        private Integer minCond;
        private Integer maxCond;
        private Integer process;
        private Integer reward;
        private long claimTime;
        private String claimableId;
        private boolean newTag;
        private String type;
        private Integer totalSupply;
        private Integer mintedCount;
        private String level;
        private List<Progress> progress;
        private String tokenId;
        private String category;
        private Integer sort;
        private String seriesLogo;
    }

    @Data
    public static class Progress {
        private String name;
        private String type;
        private String condition;
        private String value;
    }
}