package com.drex.web.core.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * Rexys
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class Rexys {

  private String id;

  private String code;

  private String image;

  private String miniImage;

  private String level;

  private Integer rate;

  private Integer limit;

  private Integer status;

  private Boolean has;

  private Boolean selected;

  public Rexys id(String id) {
    this.id = id;
    return this;
  }

  /**
   * 恐龙id
   * @return id
  */
  
  @Schema(name = "id", description = "恐龙id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("id")
  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public Rexys code(String code) {
    this.code = code;
    return this;
  }

  /**
   * 恐龙编号
   * @return code
  */
  
  @Schema(name = "code", description = "恐龙编号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("code")
  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public Rexys image(String image) {
    this.image = image;
    return this;
  }

  /**
   * 图片
   * @return image
  */
  
  @Schema(name = "image", description = "图片", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("image")
  public String getImage() {
    return image;
  }

  public void setImage(String image) {
    this.image = image;
  }

  public Rexys miniImage(String miniImage) {
    this.miniImage = miniImage;
    return this;
  }

  /**
   * 小图
   * @return miniImage
  */
  
  @Schema(name = "miniImage", description = "小图", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("miniImage")
  public String getMiniImage() {
    return miniImage;
  }

  public void setMiniImage(String miniImage) {
    this.miniImage = miniImage;
  }

  public Rexys level(String level) {
    this.level = level;
    return this;
  }

  /**
   * level
   * @return level
  */
  
  @Schema(name = "level", description = "level", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("level")
  public String getLevel() {
    return level;
  }

  public void setLevel(String level) {
    this.level = level;
  }

  public Rexys rate(Integer rate) {
    this.rate = rate;
    return this;
  }

  /**
   * 生产速率
   * @return rate
  */
  
  @Schema(name = "rate", description = "生产速率", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("rate")
  public Integer getRate() {
    return rate;
  }

  public void setRate(Integer rate) {
    this.rate = rate;
  }

  public Rexys limit(Integer limit) {
    this.limit = limit;
    return this;
  }

  /**
   * 篮子上限
   * @return limit
  */
  
  @Schema(name = "limit", description = "篮子上限", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("limit")
  public Integer getLimit() {
    return limit;
  }

  public void setLimit(Integer limit) {
    this.limit = limit;
  }

  public Rexys status(Integer status) {
    this.status = status;
    return this;
  }

  /**
   * 状态, 0:coming soon 1:lock 2:open
   * @return status
  */
  
  @Schema(name = "status", description = "状态, 0:coming soon 1:lock 2:open", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("status")
  public Integer getStatus() {
    return status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }

  public Rexys has(Boolean has) {
    this.has = has;
    return this;
  }

  /**
   * 是否拥有
   * @return has
  */
  
  @Schema(name = "has", description = "是否拥有", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("has")
  public Boolean getHas() {
    return has;
  }

  public void setHas(Boolean has) {
    this.has = has;
  }

  public Rexys selected(Boolean selected) {
    this.selected = selected;
    return this;
  }

  /**
   * 是否选中
   * @return selected
  */
  
  @Schema(name = "selected", description = "是否选中", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("selected")
  public Boolean getSelected() {
    return selected;
  }

  public void setSelected(Boolean selected) {
    this.selected = selected;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Rexys rexys = (Rexys) o;
    return Objects.equals(this.id, rexys.id) &&
        Objects.equals(this.code, rexys.code) &&
        Objects.equals(this.image, rexys.image) &&
        Objects.equals(this.miniImage, rexys.miniImage) &&
        Objects.equals(this.level, rexys.level) &&
        Objects.equals(this.rate, rexys.rate) &&
        Objects.equals(this.limit, rexys.limit) &&
        Objects.equals(this.status, rexys.status) &&
        Objects.equals(this.has, rexys.has) &&
        Objects.equals(this.selected, rexys.selected);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, code, image, miniImage, level, rate, limit, status, has, selected);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Rexys {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    image: ").append(toIndentedString(image)).append("\n");
    sb.append("    miniImage: ").append(toIndentedString(miniImage)).append("\n");
    sb.append("    level: ").append(toIndentedString(level)).append("\n");
    sb.append("    rate: ").append(toIndentedString(rate)).append("\n");
    sb.append("    limit: ").append(toIndentedString(limit)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    has: ").append(toIndentedString(has)).append("\n");
    sb.append("    selected: ").append(toIndentedString(selected)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

