package com.drex.web.activity.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * TaskConfig
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class TaskConfig {

  private String taskId;

  private String title;

  private String desc;

  private String url;

  private String connectUrl;

  private Integer status;

  private Integer btn;

  @Valid
  private Map<String, Object> link = new HashMap<>();

  private String code;

  private BigDecimal reward;

  private String icon;

  public TaskConfig taskId(String taskId) {
    this.taskId = taskId;
    return this;
  }

  /**
   * 任务id，唯一标识
   * @return taskId
  */
  
  @Schema(name = "taskId", description = "任务id，唯一标识", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("taskId")
  public String getTaskId() {
    return taskId;
  }

  public void setTaskId(String taskId) {
    this.taskId = taskId;
  }

  public TaskConfig title(String title) {
    this.title = title;
    return this;
  }

  /**
   * 任务标题
   * @return title
  */
  
  @Schema(name = "title", description = "任务标题", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("title")
  public String getTitle() {
    return title;
  }

  public void setTitle(String title) {
    this.title = title;
  }

  public TaskConfig desc(String desc) {
    this.desc = desc;
    return this;
  }

  /**
   * 任务的描述信息
   * @return desc
  */
  
  @Schema(name = "desc", description = "任务的描述信息", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("desc")
  public String getDesc() {
    return desc;
  }

  public void setDesc(String desc) {
    this.desc = desc;
  }

  public TaskConfig url(String url) {
    this.url = url;
    return this;
  }

  /**
   * 任务详情链接
   * @return url
  */
  
  @Schema(name = "url", description = "任务详情链接", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("url")
  public String getUrl() {
    return url;
  }

  public void setUrl(String url) {
    this.url = url;
  }

  public TaskConfig connectUrl(String connectUrl) {
    this.connectUrl = connectUrl;
    return this;
  }

  /**
   * 授权地址
   * @return connectUrl
  */
  
  @Schema(name = "connectUrl", description = "授权地址", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("connectUrl")
  public String getConnectUrl() {
    return connectUrl;
  }

  public void setConnectUrl(String connectUrl) {
    this.connectUrl = connectUrl;
  }

  public TaskConfig status(Integer status) {
    this.status = status;
    return this;
  }

  /**
   * 完成状态 0-未完成 1-已完成
   * @return status
  */
  
  @Schema(name = "status", description = "完成状态 0-未完成 1-已完成", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("status")
  public Integer getStatus() {
    return status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }

  public TaskConfig btn(Integer btn) {
    this.btn = btn;
    return this;
  }

  /**
   * 0:不显示 1:go 2:verify
   * @return btn
  */
  
  @Schema(name = "btn", description = "0:不显示 1:go 2:verify", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("btn")
  public Integer getBtn() {
    return btn;
  }

  public void setBtn(Integer btn) {
    this.btn = btn;
  }

  public TaskConfig link(Map<String, Object> link) {
    this.link = link;
    return this;
  }

  public TaskConfig putLinkItem(String key, Object linkItem) {
    if (this.link == null) {
      this.link = new HashMap<>();
    }
    this.link.put(key, linkItem);
    return this;
  }

  /**
   * map结构，key表示文案，例如connect now，value表示链接地址
   * @return link
  */
  
  @Schema(name = "link", description = "map结构，key表示文案，例如connect now，value表示链接地址", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("link")
  public Map<String, Object> getLink() {
    return link;
  }

  public void setLink(Map<String, Object> link) {
    this.link = link;
  }

  public TaskConfig code(String code) {
    this.code = code;
    return this;
  }

  /**
   * 任务code
   * @return code
  */
  
  @Schema(name = "code", description = "任务code", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("code")
  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public TaskConfig reward(BigDecimal reward) {
    this.reward = reward;
    return this;
  }

  /**
   * 页面展示奖励金额
   * @return reward
  */
  @Valid 
  @Schema(name = "reward", description = "页面展示奖励金额", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("reward")
  public BigDecimal getReward() {
    return reward;
  }

  public void setReward(BigDecimal reward) {
    this.reward = reward;
  }

  public TaskConfig icon(String icon) {
    this.icon = icon;
    return this;
  }

  /**
   * 任务图标
   * @return icon
  */
  
  @Schema(name = "icon", description = "任务图标", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("icon")
  public String getIcon() {
    return icon;
  }

  public void setIcon(String icon) {
    this.icon = icon;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TaskConfig taskConfig = (TaskConfig) o;
    return Objects.equals(this.taskId, taskConfig.taskId) &&
        Objects.equals(this.title, taskConfig.title) &&
        Objects.equals(this.desc, taskConfig.desc) &&
        Objects.equals(this.url, taskConfig.url) &&
        Objects.equals(this.connectUrl, taskConfig.connectUrl) &&
        Objects.equals(this.status, taskConfig.status) &&
        Objects.equals(this.btn, taskConfig.btn) &&
        Objects.equals(this.link, taskConfig.link) &&
        Objects.equals(this.code, taskConfig.code) &&
        Objects.equals(this.reward, taskConfig.reward) &&
        Objects.equals(this.icon, taskConfig.icon);
  }

  @Override
  public int hashCode() {
    return Objects.hash(taskId, title, desc, url, connectUrl, status, btn, link, code, reward, icon);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TaskConfig {\n");
    sb.append("    taskId: ").append(toIndentedString(taskId)).append("\n");
    sb.append("    title: ").append(toIndentedString(title)).append("\n");
    sb.append("    desc: ").append(toIndentedString(desc)).append("\n");
    sb.append("    url: ").append(toIndentedString(url)).append("\n");
    sb.append("    connectUrl: ").append(toIndentedString(connectUrl)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    btn: ").append(toIndentedString(btn)).append("\n");
    sb.append("    link: ").append(toIndentedString(link)).append("\n");
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    reward: ").append(toIndentedString(reward)).append("\n");
    sb.append("    icon: ").append(toIndentedString(icon)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

