package com.drex.web.common.cache;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Base Cache for SDK Client
 *
 * <AUTHOR>
 * @create 2021/6/8 11:30 上午
 * @modify
 */
@Slf4j
public abstract class BaseCache<K, V> {

    public static final long DEFAULT_EXPIRE_MINUTE = 5;

    protected LoadingCache<K, V> loadingCache;

    public void initCache() {
        CacheBuilder<Object, Object> builder = CacheBuilder.newBuilder();
        loadingCache = builder
                .maximumSize(maxSize())
                .refreshAfterWrite(Duration.ofSeconds(expireSeconds()))
                .build(new CacheLoader<K, V>() {
                    @Override
                    public V load(K k) throws Exception {
                        return loadEntry(k);
                    }
                });
    }

    public Map<K, V> getAll() {
        return loadingCache.asMap();
    }

    public Map<K, V> getAll(Iterable<K> keys){
        try{
            return loadingCache.getAll(keys);
        }catch (Exception ex){
            log.error("getAll from local cache process fail, key={},e={}", keys, ex);
            return null;
        }
    }

    public V get(K k) {
        try {
            return loadingCache.getUnchecked(k);
        } catch (Exception e) {
            log.error("get from local cache process fail, key={},e={}", k, e);
        }
        return null;
    }

    /**
     * Expired seconds
     *
     * @return
     */
    protected long expireSeconds() {
        return TimeUnit.MINUTES.toSeconds(DEFAULT_EXPIRE_MINUTE);
    }

    ;

    /**
     * Max size for cache count
     *
     * @return
     */
    protected long maxSize() {
        return 10000;
    }

    /**
     * Load single currency from central cache
     *
     * @param k
     * @return
     */
    protected V loadEntry(K k) {
        return null;
    }

    public void onUpdate(K k, V v) {
        invalidEntry(k);
    }

    public void onRemoval(K k) {
        invalidEntry(k);
    }

    public void onAllRemoval() {
        loadingCache.invalidateAll();
    }

    protected void invalidEntry(K k) {
        loadingCache.invalidate(k);
    }

}
