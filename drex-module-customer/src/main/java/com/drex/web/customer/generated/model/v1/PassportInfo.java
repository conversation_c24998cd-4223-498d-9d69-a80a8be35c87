package com.drex.web.customer.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.drex.web.customer.generated.model.v1.NftVO;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.ArrayList;
import java.util.List;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * PassportInfo
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class PassportInfo {

  private String passportId;

  private Long created;

  private String userName;

  private String handleName;

  private String addresses;

  private String email;

  private String kycLevel;

  private String chainId;

  private String authProvider;

  private String inviteCode;

  private String avatar;

  @Valid
  private List<String> holdTitles;

  private Integer passportChainId;

  private String contractAddress;

  private Long point;

  @Valid
  private List<@Valid NftVO> badges;

  public PassportInfo passportId(String passportId) {
    this.passportId = passportId;
    return this;
  }

  /**
   * 用户ID
   * @return passportId
  */
  
  @Schema(name = "passportId", description = "用户ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("passportId")
  public String getPassportId() {
    return passportId;
  }

  public void setPassportId(String passportId) {
    this.passportId = passportId;
  }

  public PassportInfo created(Long created) {
    this.created = created;
    return this;
  }

  /**
   * 创建时间
   * @return created
  */
  
  @Schema(name = "created", description = "创建时间", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("created")
  public Long getCreated() {
    return created;
  }

  public void setCreated(Long created) {
    this.created = created;
  }

  public PassportInfo userName(String userName) {
    this.userName = userName;
    return this;
  }

  /**
   * 用户名称
   * @return userName
  */
  
  @Schema(name = "userName", description = "用户名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("userName")
  public String getUserName() {
    return userName;
  }

  public void setUserName(String userName) {
    this.userName = userName;
  }

  public PassportInfo handleName(String handleName) {
    this.handleName = handleName;
    return this;
  }

  /**
   * 用户昵称或标识
   * @return handleName
  */
  
  @Schema(name = "handleName", description = "用户昵称或标识", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("handleName")
  public String getHandleName() {
    return handleName;
  }

  public void setHandleName(String handleName) {
    this.handleName = handleName;
  }

  public PassportInfo addresses(String addresses) {
    this.addresses = addresses;
    return this;
  }

  /**
   * 用户地址
   * @return addresses
  */
  
  @Schema(name = "addresses", description = "用户地址", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("addresses")
  public String getAddresses() {
    return addresses;
  }

  public void setAddresses(String addresses) {
    this.addresses = addresses;
  }

  public PassportInfo email(String email) {
    this.email = email;
    return this;
  }

  /**
   * 邮箱地址
   * @return email
  */
  
  @Schema(name = "email", description = "邮箱地址", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("email")
  public String getEmail() {
    return email;
  }

  public void setEmail(String email) {
    this.email = email;
  }

  public PassportInfo kycLevel(String kycLevel) {
    this.kycLevel = kycLevel;
    return this;
  }

  /**
   * KYC等级
   * @return kycLevel
  */
  
  @Schema(name = "kycLevel", description = "KYC等级", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("kycLevel")
  public String getKycLevel() {
    return kycLevel;
  }

  public void setKycLevel(String kycLevel) {
    this.kycLevel = kycLevel;
  }

  public PassportInfo chainId(String chainId) {
    this.chainId = chainId;
    return this;
  }

  /**
   * 链id
   * @return chainId
  */
  
  @Schema(name = "chainId", description = "链id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("chainId")
  public String getChainId() {
    return chainId;
  }

  public void setChainId(String chainId) {
    this.chainId = chainId;
  }

  public PassportInfo authProvider(String authProvider) {
    this.authProvider = authProvider;
    return this;
  }

  /**
   * 登录方式
   * @return authProvider
  */
  
  @Schema(name = "authProvider", description = "登录方式", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("authProvider")
  public String getAuthProvider() {
    return authProvider;
  }

  public void setAuthProvider(String authProvider) {
    this.authProvider = authProvider;
  }

  public PassportInfo inviteCode(String inviteCode) {
    this.inviteCode = inviteCode;
    return this;
  }

  /**
   * 邀请码
   * @return inviteCode
  */
  
  @Schema(name = "inviteCode", description = "邀请码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("inviteCode")
  public String getInviteCode() {
    return inviteCode;
  }

  public void setInviteCode(String inviteCode) {
    this.inviteCode = inviteCode;
  }

  public PassportInfo avatar(String avatar) {
    this.avatar = avatar;
    return this;
  }

  /**
   * 头像
   * @return avatar
  */
  
  @Schema(name = "avatar", description = "头像", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("avatar")
  public String getAvatar() {
    return avatar;
  }

  public void setAvatar(String avatar) {
    this.avatar = avatar;
  }

  public PassportInfo holdTitles(List<String> holdTitles) {
    this.holdTitles = holdTitles;
    return this;
  }

  public PassportInfo addHoldTitlesItem(String holdTitlesItem) {
    if (this.holdTitles == null) {
      this.holdTitles = new ArrayList<>();
    }
    this.holdTitles.add(holdTitlesItem);
    return this;
  }

  /**
   * 持有的称号
   * @return holdTitles
  */
  
  @Schema(name = "holdTitles", description = "持有的称号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("holdTitles")
  public List<String> getHoldTitles() {
    return holdTitles;
  }

  public void setHoldTitles(List<String> holdTitles) {
    this.holdTitles = holdTitles;
  }

  public PassportInfo passportChainId(Integer passportChainId) {
    this.passportChainId = passportChainId;
    return this;
  }

  /**
   * passportChainId
   * @return passportChainId
  */
  
  @Schema(name = "passportChainId", description = "passportChainId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("passportChainId")
  public Integer getPassportChainId() {
    return passportChainId;
  }

  public void setPassportChainId(Integer passportChainId) {
    this.passportChainId = passportChainId;
  }

  public PassportInfo contractAddress(String contractAddress) {
    this.contractAddress = contractAddress;
    return this;
  }

  /**
   * contractAddress
   * @return contractAddress
  */
  
  @Schema(name = "contractAddress", description = "contractAddress", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("contractAddress")
  public String getContractAddress() {
    return contractAddress;
  }

  public void setContractAddress(String contractAddress) {
    this.contractAddress = contractAddress;
  }

  public PassportInfo point(Long point) {
    this.point = point;
    return this;
  }

  /**
   * point
   * @return point
  */
  
  @Schema(name = "point", description = "point", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("point")
  public Long getPoint() {
    return point;
  }

  public void setPoint(Long point) {
    this.point = point;
  }

  public PassportInfo badges(List<@Valid NftVO> badges) {
    this.badges = badges;
    return this;
  }

  public PassportInfo addBadgesItem(NftVO badgesItem) {
    if (this.badges == null) {
      this.badges = new ArrayList<>();
    }
    this.badges.add(badgesItem);
    return this;
  }

  /**
   * Get badges
   * @return badges
  */
  @Valid 
  @Schema(name = "badges", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("badges")
  public List<@Valid NftVO> getBadges() {
    return badges;
  }

  public void setBadges(List<@Valid NftVO> badges) {
    this.badges = badges;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PassportInfo passportInfo = (PassportInfo) o;
    return Objects.equals(this.passportId, passportInfo.passportId) &&
        Objects.equals(this.created, passportInfo.created) &&
        Objects.equals(this.userName, passportInfo.userName) &&
        Objects.equals(this.handleName, passportInfo.handleName) &&
        Objects.equals(this.addresses, passportInfo.addresses) &&
        Objects.equals(this.email, passportInfo.email) &&
        Objects.equals(this.kycLevel, passportInfo.kycLevel) &&
        Objects.equals(this.chainId, passportInfo.chainId) &&
        Objects.equals(this.authProvider, passportInfo.authProvider) &&
        Objects.equals(this.inviteCode, passportInfo.inviteCode) &&
        Objects.equals(this.avatar, passportInfo.avatar) &&
        Objects.equals(this.holdTitles, passportInfo.holdTitles) &&
        Objects.equals(this.passportChainId, passportInfo.passportChainId) &&
        Objects.equals(this.contractAddress, passportInfo.contractAddress) &&
        Objects.equals(this.point, passportInfo.point) &&
        Objects.equals(this.badges, passportInfo.badges);
  }

  @Override
  public int hashCode() {
    return Objects.hash(passportId, created, userName, handleName, addresses, email, kycLevel, chainId, authProvider, inviteCode, avatar, holdTitles, passportChainId, contractAddress, point, badges);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PassportInfo {\n");
    sb.append("    passportId: ").append(toIndentedString(passportId)).append("\n");
    sb.append("    created: ").append(toIndentedString(created)).append("\n");
    sb.append("    userName: ").append(toIndentedString(userName)).append("\n");
    sb.append("    handleName: ").append(toIndentedString(handleName)).append("\n");
    sb.append("    addresses: ").append(toIndentedString(addresses)).append("\n");
    sb.append("    email: ").append(toIndentedString(email)).append("\n");
    sb.append("    kycLevel: ").append(toIndentedString(kycLevel)).append("\n");
    sb.append("    chainId: ").append(toIndentedString(chainId)).append("\n");
    sb.append("    authProvider: ").append(toIndentedString(authProvider)).append("\n");
    sb.append("    inviteCode: ").append(toIndentedString(inviteCode)).append("\n");
    sb.append("    avatar: ").append(toIndentedString(avatar)).append("\n");
    sb.append("    holdTitles: ").append(toIndentedString(holdTitles)).append("\n");
    sb.append("    passportChainId: ").append(toIndentedString(passportChainId)).append("\n");
    sb.append("    contractAddress: ").append(toIndentedString(contractAddress)).append("\n");
    sb.append("    point: ").append(toIndentedString(point)).append("\n");
    sb.append("    badges: ").append(toIndentedString(badges)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

