post:
  tags:
    - Customer
  summary: 上传文件
  description: 用于上传文件，返回文件名称和文件路径
  operationId: addWaitListUpload
  requestBody:
    required: true
    content:
      multipart/form-data:
        schema:
          type: object
          properties:
            file:
              type: string
              format: binary
              description: 需要上传的文件
  responses:
    '200':
      description: Successful operation
      content:
        application/json:
          schema:
            $ref: '../components/AddDeveloperWaitListUploadResponse.yaml'
