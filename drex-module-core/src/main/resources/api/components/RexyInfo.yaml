type: object
properties:
  customerId:
    type: string
    description: 用户id
  avatar:
    type: string
    description: 头像
  point:
    type: integer
    format: int64
    description: 积分
  rexy:
    type: object
    properties:
      id:
        type: string
        description: 恐龙id
      name:
        type: string
        description: 恐龙名称
      basketPoint:
        type: integer
        description: 购物车积分
      limitPoint:
        type: integer
        description: 篮子积分上限
      rate:
        type: integer
        description: 当前产生速率
      avatar:
        type: string
        description: 头像
