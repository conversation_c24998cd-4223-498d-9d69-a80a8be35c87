package com.drex.web.core.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.drex.web.core.generated.model.v1.RexyRewardItem;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.ArrayList;
import java.util.List;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * RexyReward
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class RexyReward {

  private Integer stage;

  private String code;

  private String point;

  @Valid
  private List<@Valid RexyRewardItem> rewards;

  public RexyReward stage(Integer stage) {
    this.stage = stage;
    return this;
  }

  /**
   * 奖励所属阶段
   * @return stage
  */
  
  @Schema(name = "stage", description = "奖励所属阶段", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("stage")
  public Integer getStage() {
    return stage;
  }

  public void setStage(Integer stage) {
    this.stage = stage;
  }

  public RexyReward code(String code) {
    this.code = code;
    return this;
  }

  /**
   * 恐龙蛋编号，产生奖励后存在
   * @return code
  */
  
  @Schema(name = "code", description = "恐龙蛋编号，产生奖励后存在", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("code")
  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public RexyReward point(String point) {
    this.point = point;
    return this;
  }

  /**
   * Get point
   * @return point
  */
  
  @Schema(name = "point", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("point")
  public String getPoint() {
    return point;
  }

  public void setPoint(String point) {
    this.point = point;
  }

  public RexyReward rewards(List<@Valid RexyRewardItem> rewards) {
    this.rewards = rewards;
    return this;
  }

  public RexyReward addRewardsItem(RexyRewardItem rewardsItem) {
    if (this.rewards == null) {
      this.rewards = new ArrayList<>();
    }
    this.rewards.add(rewardsItem);
    return this;
  }

  /**
   * Get rewards
   * @return rewards
  */
  @Valid 
  @Schema(name = "rewards", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("rewards")
  public List<@Valid RexyRewardItem> getRewards() {
    return rewards;
  }

  public void setRewards(List<@Valid RexyRewardItem> rewards) {
    this.rewards = rewards;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RexyReward rexyReward = (RexyReward) o;
    return Objects.equals(this.stage, rexyReward.stage) &&
        Objects.equals(this.code, rexyReward.code) &&
        Objects.equals(this.point, rexyReward.point) &&
        Objects.equals(this.rewards, rexyReward.rewards);
  }

  @Override
  public int hashCode() {
    return Objects.hash(stage, code, point, rewards);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RexyReward {\n");
    sb.append("    stage: ").append(toIndentedString(stage)).append("\n");
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    point: ").append(toIndentedString(point)).append("\n");
    sb.append("    rewards: ").append(toIndentedString(rewards)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

