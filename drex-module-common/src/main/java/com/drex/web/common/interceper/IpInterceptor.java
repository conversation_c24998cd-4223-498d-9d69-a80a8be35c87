package com.drex.web.common.interceper;

import com.alibaba.fastjson2.JSON;
import com.drex.web.common.context.RequestContext;
import com.drex.web.common.context.RequestContextHolder;
import com.drex.web.common.utils.IpUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/5/20 14:29
 * @description:
 */
@Slf4j
@Component
public class IpInterceptor extends AbstractInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        RequestContext context = new RequestContext();
        context.setClientIp(IpUtils.getClientIp(request));

        // 绑定到ThreadLocal
        RequestContextHolder.bind(context);
        log.debug("Request context initialized: {}", JSON.toJSONString(context));

        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 清除用户信息
        RequestContextHolder.clear();
        log.debug("Request context cleared");
    }
}
