package com.drex.web.activity.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * TaskSocialEventVO
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class TaskSocialEventVO {

  private String rexyRewardLevel;

  private String rexyRewardId;

  private Long rexyRewardPoint;

  public TaskSocialEventVO rexyRewardLevel(String rexyRewardLevel) {
    this.rexyRewardLevel = rexyRewardLevel;
    return this;
  }

  /**
   * 奖励的等级，普通金或者金蛋
   * @return rexyRewardLevel
  */
  
  @Schema(name = "rexyRewardLevel", description = "奖励的等级，普通金或者金蛋", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("rexyRewardLevel")
  public String getRexyRewardLevel() {
    return rexyRewardLevel;
  }

  public void setRexyRewardLevel(String rexyRewardLevel) {
    this.rexyRewardLevel = rexyRewardLevel;
  }

  public TaskSocialEventVO rexyRewardId(String rexyRewardId) {
    this.rexyRewardId = rexyRewardId;
    return this;
  }

  /**
   * 奖励ID，收集蛋的时候需要上送
   * @return rexyRewardId
  */
  
  @Schema(name = "rexyRewardId", description = "奖励ID，收集蛋的时候需要上送", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("rexyRewardId")
  public String getRexyRewardId() {
    return rexyRewardId;
  }

  public void setRexyRewardId(String rexyRewardId) {
    this.rexyRewardId = rexyRewardId;
  }

  public TaskSocialEventVO rexyRewardPoint(Long rexyRewardPoint) {
    this.rexyRewardPoint = rexyRewardPoint;
    return this;
  }

  /**
   * 奖励的积分
   * @return rexyRewardPoint
  */
  
  @Schema(name = "rexyRewardPoint", description = "奖励的积分", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("rexyRewardPoint")
  public Long getRexyRewardPoint() {
    return rexyRewardPoint;
  }

  public void setRexyRewardPoint(Long rexyRewardPoint) {
    this.rexyRewardPoint = rexyRewardPoint;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TaskSocialEventVO taskSocialEventVO = (TaskSocialEventVO) o;
    return Objects.equals(this.rexyRewardLevel, taskSocialEventVO.rexyRewardLevel) &&
        Objects.equals(this.rexyRewardId, taskSocialEventVO.rexyRewardId) &&
        Objects.equals(this.rexyRewardPoint, taskSocialEventVO.rexyRewardPoint);
  }

  @Override
  public int hashCode() {
    return Objects.hash(rexyRewardLevel, rexyRewardId, rexyRewardPoint);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TaskSocialEventVO {\n");
    sb.append("    rexyRewardLevel: ").append(toIndentedString(rexyRewardLevel)).append("\n");
    sb.append("    rexyRewardId: ").append(toIndentedString(rexyRewardId)).append("\n");
    sb.append("    rexyRewardPoint: ").append(toIndentedString(rexyRewardPoint)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

