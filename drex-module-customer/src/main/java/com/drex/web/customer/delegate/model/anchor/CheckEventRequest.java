package com.drex.web.customer.delegate.model.anchor;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class CheckEventRequest implements Serializable {

    private String customerId;

    private String series;

    private List<Progress> progress;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Progress {
        private String name;
        private BigDecimal value;
    }
}