package com.drex.web.core.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * RexyRewardLast
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class RexyRewardLast {

  private String rewardReceivedStatus;

  private String hasReward;

  private String rewardAmount;

  public RexyRewardLast rewardReceivedStatus(String rewardReceivedStatus) {
    this.rewardReceivedStatus = rewardReceivedStatus;
    return this;
  }

  /**
   * 奖励领取状态 0-未领取 1-已领取
   * @return rewardReceivedStatus
  */
  
  @Schema(name = "rewardReceivedStatus", description = "奖励领取状态 0-未领取 1-已领取", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("rewardReceivedStatus")
  public String getRewardReceivedStatus() {
    return rewardReceivedStatus;
  }

  public void setRewardReceivedStatus(String rewardReceivedStatus) {
    this.rewardReceivedStatus = rewardReceivedStatus;
  }

  public RexyRewardLast hasReward(String hasReward) {
    this.hasReward = hasReward;
    return this;
  }

  /**
   * 是否有奖励 0-无 1-有
   * @return hasReward
  */
  
  @Schema(name = "hasReward", description = "是否有奖励 0-无 1-有", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("hasReward")
  public String getHasReward() {
    return hasReward;
  }

  public void setHasReward(String hasReward) {
    this.hasReward = hasReward;
  }

  public RexyRewardLast rewardAmount(String rewardAmount) {
    this.rewardAmount = rewardAmount;
    return this;
  }

  /**
   * 奖励值
   * @return rewardAmount
  */
  
  @Schema(name = "rewardAmount", description = "奖励值", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("rewardAmount")
  public String getRewardAmount() {
    return rewardAmount;
  }

  public void setRewardAmount(String rewardAmount) {
    this.rewardAmount = rewardAmount;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RexyRewardLast rexyRewardLast = (RexyRewardLast) o;
    return Objects.equals(this.rewardReceivedStatus, rexyRewardLast.rewardReceivedStatus) &&
        Objects.equals(this.hasReward, rexyRewardLast.hasReward) &&
        Objects.equals(this.rewardAmount, rexyRewardLast.rewardAmount);
  }

  @Override
  public int hashCode() {
    return Objects.hash(rewardReceivedStatus, hasReward, rewardAmount);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RexyRewardLast {\n");
    sb.append("    rewardReceivedStatus: ").append(toIndentedString(rewardReceivedStatus)).append("\n");
    sb.append("    hasReward: ").append(toIndentedString(hasReward)).append("\n");
    sb.append("    rewardAmount: ").append(toIndentedString(rewardAmount)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

