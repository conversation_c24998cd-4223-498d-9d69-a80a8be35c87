package com.drex.web.activity.converter;

import com.drex.core.api.response.MaizeDTO;
import com.drex.web.activity.generated.model.v1.TaskSocialEventResponse;
import com.drex.web.activity.generated.model.v1.TaskSocialEventVO;

/**
 * <AUTHOR>
 * @date 2025/4/25 14:37
 * @description:
 */
public class ActivityConverter {

    public static TaskSocialEventResponse toTaskSocialEventResponse(MaizeDTO maizeDTO) {
        TaskSocialEventResponse taskSocialEventResponse = new TaskSocialEventResponse();
        TaskSocialEventVO socialEvent = new TaskSocialEventVO();
        if (maizeDTO != null) {
            taskSocialEventResponse.success();
            socialEvent.setRexyRewardId(maizeDTO.getCode());
            socialEvent.setRexyRewardLevel(maizeDTO.getLevel());
            socialEvent.setRexyRewardPoint(maizeDTO.getScore());
        }
        taskSocialEventResponse.setObj(socialEvent);
        return taskSocialEventResponse;
    }
}
