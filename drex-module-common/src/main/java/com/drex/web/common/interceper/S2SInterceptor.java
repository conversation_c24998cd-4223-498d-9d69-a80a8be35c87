package com.drex.web.common.interceper;

import com.drex.web.common.config.WebProperties;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.ModelAndView;

@Slf4j
@Component
public class S2SInterceptor extends AbstractInterceptor {

    private static final String HEADER_NAME = "app_key";
    @Resource
    private WebProperties webProperties;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if ("OPTIONS".equalsIgnoreCase(request.getMethod())) {
            return true;
        }
        String appKey = request.getHeader(HEADER_NAME);
        if(appKey != null && appKey.equals(webProperties.getAppKey())){
            return true;
        }
        response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "app_key error");
        return false;
    }
}
