/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (6.6.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
package com.drex.web.core.generated.api.v1;

import com.drex.web.core.generated.model.v1.NoticeResponse;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
@Validated
@Tag(name = "Notice", description = "the Notice API")
public interface NoticeApi {

    default NoticeApiDelegate getDelegate() {
        return new NoticeApiDelegate() {};
    }

    /**
     * GET /notice : 查询未读消息通知列表
     * 查询未读消息通知列表
     *
     * @param lastFetchNotifyTime 最后一次拉取通知的时间 (optional, default to 0)
     * @return OK (status code 200)
     */
    @Operation(
        operationId = "getNotices",
        summary = "查询未读消息通知列表",
        description = "查询未读消息通知列表",
        tags = { "Notice" },
        responses = {
            @ApiResponse(responseCode = "200", description = "OK", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = NoticeResponse.class))
            })
        },
        security = {
            @SecurityRequirement(name = "jwtBearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/notice",
        produces = { "application/json" }
    )
    default ResponseEntity<NoticeResponse> getNotices(
        @Parameter(name = "lastFetchNotifyTime", description = "最后一次拉取通知的时间", in = ParameterIn.QUERY) @Valid @RequestParam(value = "lastFetchNotifyTime", required = false, defaultValue = "0") Long lastFetchNotifyTime
    ) {
        return getDelegate().getNotices(lastFetchNotifyTime);
    }

}
