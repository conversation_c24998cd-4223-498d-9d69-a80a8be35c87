package com.drex.web.customer.delegate.model.anchor;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class AccessTokenResult implements Serializable {

    @J<PERSON>NField(name = "access_token")
    private String accessToken;

    @JSONField(name = "token_type")
    private String tokenType;

    @JSONField(name = "expires_in")
    private Integer expiresIn;

}