post:
  tags:
    - Customer
  summary: 删除社媒信息
  description: 删除社媒信息
  operationId: unbindSocialInfo
  security:
    - jwtBearerAuth: [ ]
  parameters:
    - name: app_key
      in: header
      description: app_key
      required: true
      example: trex
      schema:
        type: string
  requestBody:
    content:
      application/json:
        schema:
          $ref: '../components/UnbindSocialInfoRequest.yaml'
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            $ref: '../components/UnBindSocialInfoResponse.yaml'
