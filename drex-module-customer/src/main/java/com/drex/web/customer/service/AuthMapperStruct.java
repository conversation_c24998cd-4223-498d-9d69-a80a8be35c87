package com.drex.web.customer.service;

import com.drex.customer.api.response.ThirdAuthDTO;
import com.drex.customer.api.response.ThirdBindingsDTO;
import com.drex.web.customer.generated.model.v1.ThirdAuthorizeInfo;
import com.drex.web.customer.generated.model.v1.ThirdBindingsInfoInner;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/28 19:58
 * @description:
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AuthMapperStruct {

    ThirdAuthorizeInfo toThirdAuthorizeInfo(ThirdAuthDTO thirdAuthDTO);

    List<ThirdBindingsInfoInner> toThirdBindingsInfo(List<ThirdBindingsDTO> thirdBindings);
}
