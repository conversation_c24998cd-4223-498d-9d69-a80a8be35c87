package com.drex.web.customer.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.ArrayList;
import java.util.List;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * AddWaitDeveloperListRequest
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class AddWaitDeveloperListRequest {

  private String projectName;

  @Valid
  private List<String> projectCategory = new ArrayList<>();

  private String projectCategoryOther;

  private String projectDescription;

  private String ecosystemAlignment;

  private String projectStage;

  private String projectWebsite;

  private String projectTwitter;

  private String projectCommunity;

  private String primaryContactName;

  private String contactEmail;

  private String contactTelegram;

  @Valid
  private List<String> supportType = new ArrayList<>();

  private String supportTypeOther;

  private String useOfFunds;

  private String teamBackground;

  private String supportingDocuments;

  private String additionalComments;

  /**
   * Default constructor
   * @deprecated Use {@link AddWaitDeveloperListRequest#AddWaitDeveloperListRequest(String, List<String>, String, String, String, String, String, String, List<String>, String, String)}
   */
  @Deprecated
  public AddWaitDeveloperListRequest() {
    super();
  }

  /**
   * Constructor with only required parameters
   */
  public AddWaitDeveloperListRequest(String projectName, List<String> projectCategory, String projectDescription, String ecosystemAlignment, String projectStage, String projectWebsite, String primaryContactName, String contactEmail, List<String> supportType, String useOfFunds, String teamBackground) {
    this.projectName = projectName;
    this.projectCategory = projectCategory;
    this.projectDescription = projectDescription;
    this.ecosystemAlignment = ecosystemAlignment;
    this.projectStage = projectStage;
    this.projectWebsite = projectWebsite;
    this.primaryContactName = primaryContactName;
    this.contactEmail = contactEmail;
    this.supportType = supportType;
    this.useOfFunds = useOfFunds;
    this.teamBackground = teamBackground;
  }

  public AddWaitDeveloperListRequest projectName(String projectName) {
    this.projectName = projectName;
    return this;
  }

  /**
   * 项目名称
   * @return projectName
  */
  @NotNull 
  @Schema(name = "projectName", description = "项目名称", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("projectName")
  public String getProjectName() {
    return projectName;
  }

  public void setProjectName(String projectName) {
    this.projectName = projectName;
  }

  public AddWaitDeveloperListRequest projectCategory(List<String> projectCategory) {
    this.projectCategory = projectCategory;
    return this;
  }

  public AddWaitDeveloperListRequest addProjectCategoryItem(String projectCategoryItem) {
    if (this.projectCategory == null) {
      this.projectCategory = new ArrayList<>();
    }
    this.projectCategory.add(projectCategoryItem);
    return this;
  }

  /**
   * 项目类别，可多选
   * @return projectCategory
  */
  @NotNull 
  @Schema(name = "projectCategory", description = "项目类别，可多选", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("projectCategory")
  public List<String> getProjectCategory() {
    return projectCategory;
  }

  public void setProjectCategory(List<String> projectCategory) {
    this.projectCategory = projectCategory;
  }

  public AddWaitDeveloperListRequest projectCategoryOther(String projectCategoryOther) {
    this.projectCategoryOther = projectCategoryOther;
    return this;
  }

  /**
   * 若项目类别选择 'Others'，请填写具体内容
   * @return projectCategoryOther
  */
  
  @Schema(name = "projectCategoryOther", description = "若项目类别选择 'Others'，请填写具体内容", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("projectCategoryOther")
  public String getProjectCategoryOther() {
    return projectCategoryOther;
  }

  public void setProjectCategoryOther(String projectCategoryOther) {
    this.projectCategoryOther = projectCategoryOther;
  }

  public AddWaitDeveloperListRequest projectDescription(String projectDescription) {
    this.projectDescription = projectDescription;
    return this;
  }

  /**
   * 简要描述您的项目、使命及其解决的问题
   * @return projectDescription
  */
  @NotNull 
  @Schema(name = "projectDescription", description = "简要描述您的项目、使命及其解决的问题", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("projectDescription")
  public String getProjectDescription() {
    return projectDescription;
  }

  public void setProjectDescription(String projectDescription) {
    this.projectDescription = projectDescription;
  }

  public AddWaitDeveloperListRequest ecosystemAlignment(String ecosystemAlignment) {
    this.ecosystemAlignment = ecosystemAlignment;
    return this;
  }

  /**
   * 解释您的项目如何利用 T-Rex 并为生态系统带来什么价值
   * @return ecosystemAlignment
  */
  @NotNull 
  @Schema(name = "ecosystemAlignment", description = "解释您的项目如何利用 T-Rex 并为生态系统带来什么价值", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("ecosystemAlignment")
  public String getEcosystemAlignment() {
    return ecosystemAlignment;
  }

  public void setEcosystemAlignment(String ecosystemAlignment) {
    this.ecosystemAlignment = ecosystemAlignment;
  }

  public AddWaitDeveloperListRequest projectStage(String projectStage) {
    this.projectStage = projectStage;
    return this;
  }

  /**
   * 项目所处阶段
   * @return projectStage
  */
  @NotNull 
  @Schema(name = "projectStage", description = "项目所处阶段", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("projectStage")
  public String getProjectStage() {
    return projectStage;
  }

  public void setProjectStage(String projectStage) {
    this.projectStage = projectStage;
  }

  public AddWaitDeveloperListRequest projectWebsite(String projectWebsite) {
    this.projectWebsite = projectWebsite;
    return this;
  }

  /**
   * 项目网站 URL
   * @return projectWebsite
  */
  @NotNull 
  @Schema(name = "projectWebsite", description = "项目网站 URL", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("projectWebsite")
  public String getProjectWebsite() {
    return projectWebsite;
  }

  public void setProjectWebsite(String projectWebsite) {
    this.projectWebsite = projectWebsite;
  }

  public AddWaitDeveloperListRequest projectTwitter(String projectTwitter) {
    this.projectTwitter = projectTwitter;
    return this;
  }

  /**
   * 项目 Twitter URL
   * @return projectTwitter
  */
  
  @Schema(name = "projectTwitter", description = "项目 Twitter URL", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("projectTwitter")
  public String getProjectTwitter() {
    return projectTwitter;
  }

  public void setProjectTwitter(String projectTwitter) {
    this.projectTwitter = projectTwitter;
  }

  public AddWaitDeveloperListRequest projectCommunity(String projectCommunity) {
    this.projectCommunity = projectCommunity;
    return this;
  }

  /**
   * 项目社区（Telegram 群组、Discord 等）URL
   * @return projectCommunity
  */
  
  @Schema(name = "projectCommunity", description = "项目社区（Telegram 群组、Discord 等）URL", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("projectCommunity")
  public String getProjectCommunity() {
    return projectCommunity;
  }

  public void setProjectCommunity(String projectCommunity) {
    this.projectCommunity = projectCommunity;
  }

  public AddWaitDeveloperListRequest primaryContactName(String primaryContactName) {
    this.primaryContactName = primaryContactName;
    return this;
  }

  /**
   * 主要联系人姓名
   * @return primaryContactName
  */
  @NotNull 
  @Schema(name = "primaryContactName", description = "主要联系人姓名", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("primaryContactName")
  public String getPrimaryContactName() {
    return primaryContactName;
  }

  public void setPrimaryContactName(String primaryContactName) {
    this.primaryContactName = primaryContactName;
  }

  public AddWaitDeveloperListRequest contactEmail(String contactEmail) {
    this.contactEmail = contactEmail;
    return this;
  }

  /**
   * 联系人邮箱
   * @return contactEmail
  */
  @NotNull @javax.validation.constraints.Email
  @Schema(name = "contactEmail", description = "联系人邮箱", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("contactEmail")
  public String getContactEmail() {
    return contactEmail;
  }

  public void setContactEmail(String contactEmail) {
    this.contactEmail = contactEmail;
  }

  public AddWaitDeveloperListRequest contactTelegram(String contactTelegram) {
    this.contactTelegram = contactTelegram;
    return this;
  }

  /**
   * 联系人 Telegram 用户名（可选）
   * @return contactTelegram
  */
  
  @Schema(name = "contactTelegram", description = "联系人 Telegram 用户名（可选）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("contactTelegram")
  public String getContactTelegram() {
    return contactTelegram;
  }

  public void setContactTelegram(String contactTelegram) {
    this.contactTelegram = contactTelegram;
  }

  public AddWaitDeveloperListRequest supportType(List<String> supportType) {
    this.supportType = supportType;
    return this;
  }

  public AddWaitDeveloperListRequest addSupportTypeItem(String supportTypeItem) {
    if (this.supportType == null) {
      this.supportType = new ArrayList<>();
    }
    this.supportType.add(supportTypeItem);
    return this;
  }

  /**
   * 寻求的支持类型，可多选
   * @return supportType
  */
  @NotNull 
  @Schema(name = "supportType", description = "寻求的支持类型，可多选", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("supportType")
  public List<String> getSupportType() {
    return supportType;
  }

  public void setSupportType(List<String> supportType) {
    this.supportType = supportType;
  }

  public AddWaitDeveloperListRequest supportTypeOther(String supportTypeOther) {
    this.supportTypeOther = supportTypeOther;
    return this;
  }

  /**
   * 若支持类型选择 'Other'，请填写具体内容
   * @return supportTypeOther
  */
  
  @Schema(name = "supportTypeOther", description = "若支持类型选择 'Other'，请填写具体内容", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("supportTypeOther")
  public String getSupportTypeOther() {
    return supportTypeOther;
  }

  public void setSupportTypeOther(String supportTypeOther) {
    this.supportTypeOther = supportTypeOther;
  }

  public AddWaitDeveloperListRequest useOfFunds(String useOfFunds) {
    this.useOfFunds = useOfFunds;
    return this;
  }

  /**
   * 简要概述计划如何使用申请的资金
   * @return useOfFunds
  */
  @NotNull 
  @Schema(name = "useOfFunds", description = "简要概述计划如何使用申请的资金", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("useOfFunds")
  public String getUseOfFunds() {
    return useOfFunds;
  }

  public void setUseOfFunds(String useOfFunds) {
    this.useOfFunds = useOfFunds;
  }

  public AddWaitDeveloperListRequest teamBackground(String teamBackground) {
    this.teamBackground = teamBackground;
    return this;
  }

  /**
   * 介绍您的核心团队及相关经验
   * @return teamBackground
  */
  @NotNull 
  @Schema(name = "teamBackground", description = "介绍您的核心团队及相关经验", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("teamBackground")
  public String getTeamBackground() {
    return teamBackground;
  }

  public void setTeamBackground(String teamBackground) {
    this.teamBackground = teamBackground;
  }

  public AddWaitDeveloperListRequest supportingDocuments(String supportingDocuments) {
    this.supportingDocuments = supportingDocuments;
    return this;
  }

  /**
   * 上传项目推介材料或支持文档（文件上传或 URL）
   * @return supportingDocuments
  */
  
  @Schema(name = "supportingDocuments", description = "上传项目推介材料或支持文档（文件上传或 URL）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("supportingDocuments")
  public String getSupportingDocuments() {
    return supportingDocuments;
  }

  public void setSupportingDocuments(String supportingDocuments) {
    this.supportingDocuments = supportingDocuments;
  }

  public AddWaitDeveloperListRequest additionalComments(String additionalComments) {
    this.additionalComments = additionalComments;
    return this;
  }

  /**
   * 其他需要我们了解的信息（可选）
   * @return additionalComments
  */
  
  @Schema(name = "additionalComments", description = "其他需要我们了解的信息（可选）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("additionalComments")
  public String getAdditionalComments() {
    return additionalComments;
  }

  public void setAdditionalComments(String additionalComments) {
    this.additionalComments = additionalComments;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    AddWaitDeveloperListRequest addWaitDeveloperListRequest = (AddWaitDeveloperListRequest) o;
    return Objects.equals(this.projectName, addWaitDeveloperListRequest.projectName) &&
        Objects.equals(this.projectCategory, addWaitDeveloperListRequest.projectCategory) &&
        Objects.equals(this.projectCategoryOther, addWaitDeveloperListRequest.projectCategoryOther) &&
        Objects.equals(this.projectDescription, addWaitDeveloperListRequest.projectDescription) &&
        Objects.equals(this.ecosystemAlignment, addWaitDeveloperListRequest.ecosystemAlignment) &&
        Objects.equals(this.projectStage, addWaitDeveloperListRequest.projectStage) &&
        Objects.equals(this.projectWebsite, addWaitDeveloperListRequest.projectWebsite) &&
        Objects.equals(this.projectTwitter, addWaitDeveloperListRequest.projectTwitter) &&
        Objects.equals(this.projectCommunity, addWaitDeveloperListRequest.projectCommunity) &&
        Objects.equals(this.primaryContactName, addWaitDeveloperListRequest.primaryContactName) &&
        Objects.equals(this.contactEmail, addWaitDeveloperListRequest.contactEmail) &&
        Objects.equals(this.contactTelegram, addWaitDeveloperListRequest.contactTelegram) &&
        Objects.equals(this.supportType, addWaitDeveloperListRequest.supportType) &&
        Objects.equals(this.supportTypeOther, addWaitDeveloperListRequest.supportTypeOther) &&
        Objects.equals(this.useOfFunds, addWaitDeveloperListRequest.useOfFunds) &&
        Objects.equals(this.teamBackground, addWaitDeveloperListRequest.teamBackground) &&
        Objects.equals(this.supportingDocuments, addWaitDeveloperListRequest.supportingDocuments) &&
        Objects.equals(this.additionalComments, addWaitDeveloperListRequest.additionalComments);
  }

  @Override
  public int hashCode() {
    return Objects.hash(projectName, projectCategory, projectCategoryOther, projectDescription, ecosystemAlignment, projectStage, projectWebsite, projectTwitter, projectCommunity, primaryContactName, contactEmail, contactTelegram, supportType, supportTypeOther, useOfFunds, teamBackground, supportingDocuments, additionalComments);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class AddWaitDeveloperListRequest {\n");
    sb.append("    projectName: ").append(toIndentedString(projectName)).append("\n");
    sb.append("    projectCategory: ").append(toIndentedString(projectCategory)).append("\n");
    sb.append("    projectCategoryOther: ").append(toIndentedString(projectCategoryOther)).append("\n");
    sb.append("    projectDescription: ").append(toIndentedString(projectDescription)).append("\n");
    sb.append("    ecosystemAlignment: ").append(toIndentedString(ecosystemAlignment)).append("\n");
    sb.append("    projectStage: ").append(toIndentedString(projectStage)).append("\n");
    sb.append("    projectWebsite: ").append(toIndentedString(projectWebsite)).append("\n");
    sb.append("    projectTwitter: ").append(toIndentedString(projectTwitter)).append("\n");
    sb.append("    projectCommunity: ").append(toIndentedString(projectCommunity)).append("\n");
    sb.append("    primaryContactName: ").append(toIndentedString(primaryContactName)).append("\n");
    sb.append("    contactEmail: ").append(toIndentedString(contactEmail)).append("\n");
    sb.append("    contactTelegram: ").append(toIndentedString(contactTelegram)).append("\n");
    sb.append("    supportType: ").append(toIndentedString(supportType)).append("\n");
    sb.append("    supportTypeOther: ").append(toIndentedString(supportTypeOther)).append("\n");
    sb.append("    useOfFunds: ").append(toIndentedString(useOfFunds)).append("\n");
    sb.append("    teamBackground: ").append(toIndentedString(teamBackground)).append("\n");
    sb.append("    supportingDocuments: ").append(toIndentedString(supportingDocuments)).append("\n");
    sb.append("    additionalComments: ").append(toIndentedString(additionalComments)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

