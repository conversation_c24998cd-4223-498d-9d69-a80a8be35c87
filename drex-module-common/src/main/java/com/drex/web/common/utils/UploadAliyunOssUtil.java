
package com.drex.web.common.utils;

import com.aliyun.credentials.models.Config;
import com.aliyun.credentials.models.CredentialModel;
import com.aliyun.credentials.utils.AuthUtils;
import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.auth.*;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.PutObjectResult;
import com.aliyuncs.exceptions.ClientException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
public class UploadAliyunOssUtil {

    private OSS ossClient;

    private String endpoint;
    private String bucketName;

    public UploadAliyunOssUtil() {
    }

    public UploadAliyunOssUtil(String endpoint, String bucketName, String roleName) {
        try {
            this.bucketName = bucketName;
            this.endpoint = endpoint;
            String sysRoleName = AuthUtils.getEnvironmentECSMetaData();
            log.info("UploadOssUtil, RoleName={}", sysRoleName);
            InstanceProfileCredentialsProvider instanceProfileCredentialsProvider = CredentialsProviderFactory.newInstanceProfileCredentialsProvider(sysRoleName);
            ossClient = new OSSClientBuilder().build(endpoint, instanceProfileCredentialsProvider, new ClientBuilderConfiguration());
        } catch (ClientException e) {
            log.error("UploadOssUtil init error", e);
        }
    }

    public OSSObject getObject(String path) {
        return ossClient.getObject(bucketName, path);
    }

    public static void main(String[] args) throws ClientException {

        try {
            OSS ossClient2 = null;

            String endpoint = "http://oss-ap-southeast-1.aliyuncs.com";
            String bucketName = "drex-dev-new";

            String sysRoleName = "DrexDrexwebRoleDev";
            log.info("UploadOssUtil, RoleName={}", sysRoleName);
            InstanceProfileCredentialsProvider instanceProfileCredentialsProvider = CredentialsProviderFactory.newInstanceProfileCredentialsProvider(sysRoleName);
            ossClient2 = new OSSClientBuilder().build(endpoint, instanceProfileCredentialsProvider, new ClientBuilderConfiguration());


            List<String> words = new ArrayList<>();
            OSSObject object = ossClient2.getObject(bucketName, "drex/trex/SensitiveWords.xlsx");

            System.out.println(object);

            if (object != null) {
//            try (Workboo workbook = new XSSFWorkbook(object.getObjectContent())) {
//                Sheet sheet = workbook.getSheetAt(0); // Get first sheet
//                for (Row row : sheet) {
//                    Cell cell = row.getCell(0, Row.MissingCellPolicy.RETURN_BLANK_AS_NULL);
//                    if (cell != null) {
//                        String cellValue = cell.getStringCellValue();
//                        if (cellValue != null && !cellValue.trim().isEmpty()) {
//                            words.add(cellValue.trim());
//                        }
//                    }
//                }
//            } catch (Exception e) {
//                log.error("Error reading sensitive words from Excel", e);
//                // Consider whether to rethrow or handle the exception based on your requirements
//            } finally {
//                try {
//                    object.close();
//                } catch (IOException e) {
//                    log.error("Error closing OSS object", e);
//                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    /**
     * Upload file
     *
     * @param key
     * @param file
     */
    public PutObjectResult putObject(String path, String key, File file) {
        return ossClient.putObject(bucketName, absoluteKey(path, key), file);
    }

    public String absoluteKey(String path, String key) {

        if (StringUtils.isEmpty(path)) {
            return key;
        }

        return path + File.separator + key;
    }

    /**
     * Shutdown
     */
    public void shutdown() {

        if (ossClient == null) {
            return;
        }

        ossClient.shutdown();
    }

    public OSS getOssClient() {
        return ossClient;
    }

    public String getEndpoint() {
        return endpoint;
    }

    public String getBucketName() {
        return bucketName;
    }

    public String getLocation(String path, String filePath) {
        if(StringUtils.isBlank(filePath)){
            return null;
        }
        String p = "https://";
        return String.format("https://%s.%s/%s/%s", bucketName, endpoint.substring(p.length() - 1), path, filePath);
    }
}
