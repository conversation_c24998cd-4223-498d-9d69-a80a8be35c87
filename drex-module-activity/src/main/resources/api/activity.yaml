openapi: 3.0.3
info:
  license:
    name: Apache 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0
  description: The Kweb API v1.0
  title: Kweb API
  version: '1.0'
servers:
  - description: kweb server url
    url: https://api.trex.dev.dipbit.xyz/v1
tags:
  - name: Activity
    description: 活动相关接口描述
paths:
  /task/socialEvent:
    $ref: 'paths/task/taskSocialEvent.yaml'

components:
  securitySchemes:
    jwtBearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: |
        使用JWT Bearer令牌进行身份验证。
        在请求头中添加 `Authorization` 字段，格式为 `Bearer <JWT令牌>`。
