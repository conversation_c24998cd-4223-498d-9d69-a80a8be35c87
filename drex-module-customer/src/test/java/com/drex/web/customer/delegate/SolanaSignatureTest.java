package com.drex.web.customer.delegate;

import cn.hutool.core.codec.Base58;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.sol4k.PublicKey;
import org.web3j.utils.Numeric;

import java.nio.charset.StandardCharsets;

/**
 * Solana 签名验证测试
 * 
 * <AUTHOR>
 * @date 2025/7/24
 */
@Slf4j
public class SolanaSignatureTest {

    @Test
    public void testSolanaSignatureVerification() {
        try {
            String message = "Sign this message to bind your Solana wallet: CmzpyupkTC5watBVq8VCUeTtLr1B2yGmUYGxfS78CYCV\n" +
                    "Nonce: QIzkms4juCIGeIIDHlrqOujcFqDw82Ud\n" +
                    "Timestamp: 2025-07-24T10:32:24.188733200Z";
            String publicKeyStr = "CmzpyupkTC5watBVq8VCUeTtLr1B2yGmUYGxfS78CYCV";
            String signature = "0xa1a47adfe41825652f90d3d797d3b7b863741ea4cf9ea2cb3ab2e2296fe849890858fa52fe0b0afc2df6ffbc47db2b92899d1dadb6ff3df2d9ef99140ec7a905";

            String base58Signature = convertHexToBase58Signature(signature);
            byte[] messageBytes = message.getBytes(StandardCharsets.UTF_8);
            PublicKey publicKey = new PublicKey(publicKeyStr);
            byte[] signatureBytes = org.bitcoinj.core.Base58.decode(base58Signature);
            boolean isValid = publicKey.verify(signatureBytes, messageBytes);
            System.out.println(isValid);
        } catch (Exception e) {
            log.error("testSolanaSignatureVerification error: {}", e.getMessage());
        }
    }

    @Test
    public void testBase58Conversion() {
        String hexSignature = "5754a891ad63019eb9669692e6f00d2d8966a843aacb9a0700469344d7b04ceb67b2704d6a34479feaf57d2357be8ba2c708cf9af4969625de12b0dd4edc8403";
        
        // 转换为字节数组
        byte[] signatureBytes = Numeric.hexStringToByteArray(hexSignature);
        
        // 转换为 Base58
        String base58Signature = Base58.encode(signatureBytes);
        
        System.out.println("十六进制: " + hexSignature);
        System.out.println("Base58: " + base58Signature);
        System.out.println("字节长度: " + signatureBytes.length);
        
        // 验证往返转换
        byte[] decodedBytes = Base58.decode(base58Signature);
        String backToHex = Numeric.toHexString(decodedBytes);
        
        System.out.println("往返转换结果: " + backToHex);
        System.out.println("转换是否一致: " + hexSignature.equals(backToHex.substring(2)));
    }

    /**
     * 将十六进制签名转换为 Base58 格式
     */
    private String convertHexToBase58Signature(String hexSignature) {
        try {
            // 移除 0x 前缀
            if (hexSignature.startsWith("0x")) {
                hexSignature = hexSignature.substring(2);
            }
            
            // 将十六进制字符串转换为字节数组
            byte[] signatureBytes = Numeric.hexStringToByteArray(hexSignature);
            
            // 转换为 Base58 编码
            return Base58.encode(signatureBytes);
        } catch (Exception e) {
            log.error("Failed to convert hex signature to Base58: {}", e.getMessage());
            throw new IllegalArgumentException("Invalid hex signature format", e);
        }
    }
}
