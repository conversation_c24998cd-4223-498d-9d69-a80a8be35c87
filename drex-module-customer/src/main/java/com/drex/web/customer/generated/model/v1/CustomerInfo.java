package com.drex.web.customer.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.drex.web.customer.generated.model.v1.CustomerInfoRexy;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * CustomerInfo
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class CustomerInfo {

  private String customerId;

  private String registerAddress;

  private String eoaAddress;

  private String smartAccountAddress;

  private String connectWalletAddress;

  private String email;

  private String username;

  private String kycLevel;

  private String chainId;

  private String authProvider;

  private String inviteCode;

  private String avatar;

  private Long point;

  private CustomerInfoRexy rexy;

  public CustomerInfo customerId(String customerId) {
    this.customerId = customerId;
    return this;
  }

  /**
   * 用户id
   * @return customerId
  */
  
  @Schema(name = "customerId", description = "用户id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("customerId")
  public String getCustomerId() {
    return customerId;
  }

  public void setCustomerId(String customerId) {
    this.customerId = customerId;
  }

  public CustomerInfo registerAddress(String registerAddress) {
    this.registerAddress = registerAddress;
    return this;
  }

  /**
   * 注册地址
   * @return registerAddress
  */
  
  @Schema(name = "registerAddress", description = "注册地址", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("registerAddress")
  public String getRegisterAddress() {
    return registerAddress;
  }

  public void setRegisterAddress(String registerAddress) {
    this.registerAddress = registerAddress;
  }

  public CustomerInfo eoaAddress(String eoaAddress) {
    this.eoaAddress = eoaAddress;
    return this;
  }

  /**
   * eoa钱包地址
   * @return eoaAddress
  */
  
  @Schema(name = "eoaAddress", description = "eoa钱包地址", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("eoaAddress")
  public String getEoaAddress() {
    return eoaAddress;
  }

  public void setEoaAddress(String eoaAddress) {
    this.eoaAddress = eoaAddress;
  }

  public CustomerInfo smartAccountAddress(String smartAccountAddress) {
    this.smartAccountAddress = smartAccountAddress;
    return this;
  }

  /**
   * 智能钱包地址
   * @return smartAccountAddress
  */
  
  @Schema(name = "smartAccountAddress", description = "智能钱包地址", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("smartAccountAddress")
  public String getSmartAccountAddress() {
    return smartAccountAddress;
  }

  public void setSmartAccountAddress(String smartAccountAddress) {
    this.smartAccountAddress = smartAccountAddress;
  }

  public CustomerInfo connectWalletAddress(String connectWalletAddress) {
    this.connectWalletAddress = connectWalletAddress;
    return this;
  }

  /**
   * 连接钱包地址
   * @return connectWalletAddress
  */
  
  @Schema(name = "connectWalletAddress", description = "连接钱包地址", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("connectWalletAddress")
  public String getConnectWalletAddress() {
    return connectWalletAddress;
  }

  public void setConnectWalletAddress(String connectWalletAddress) {
    this.connectWalletAddress = connectWalletAddress;
  }

  public CustomerInfo email(String email) {
    this.email = email;
    return this;
  }

  /**
   * 邮箱地址
   * @return email
  */
  
  @Schema(name = "email", description = "邮箱地址", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("email")
  public String getEmail() {
    return email;
  }

  public void setEmail(String email) {
    this.email = email;
  }

  public CustomerInfo username(String username) {
    this.username = username;
    return this;
  }

  /**
   * 昵称
   * @return username
  */
  
  @Schema(name = "username", description = "昵称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("username")
  public String getUsername() {
    return username;
  }

  public void setUsername(String username) {
    this.username = username;
  }

  public CustomerInfo kycLevel(String kycLevel) {
    this.kycLevel = kycLevel;
    return this;
  }

  /**
   * KYC等级
   * @return kycLevel
  */
  
  @Schema(name = "kycLevel", description = "KYC等级", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("kycLevel")
  public String getKycLevel() {
    return kycLevel;
  }

  public void setKycLevel(String kycLevel) {
    this.kycLevel = kycLevel;
  }

  public CustomerInfo chainId(String chainId) {
    this.chainId = chainId;
    return this;
  }

  /**
   * 链id
   * @return chainId
  */
  
  @Schema(name = "chainId", description = "链id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("chainId")
  public String getChainId() {
    return chainId;
  }

  public void setChainId(String chainId) {
    this.chainId = chainId;
  }

  public CustomerInfo authProvider(String authProvider) {
    this.authProvider = authProvider;
    return this;
  }

  /**
   * 登录方式
   * @return authProvider
  */
  
  @Schema(name = "authProvider", description = "登录方式", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("authProvider")
  public String getAuthProvider() {
    return authProvider;
  }

  public void setAuthProvider(String authProvider) {
    this.authProvider = authProvider;
  }

  public CustomerInfo inviteCode(String inviteCode) {
    this.inviteCode = inviteCode;
    return this;
  }

  /**
   * 邀请码
   * @return inviteCode
  */
  
  @Schema(name = "inviteCode", description = "邀请码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("inviteCode")
  public String getInviteCode() {
    return inviteCode;
  }

  public void setInviteCode(String inviteCode) {
    this.inviteCode = inviteCode;
  }

  public CustomerInfo avatar(String avatar) {
    this.avatar = avatar;
    return this;
  }

  /**
   * 头像
   * @return avatar
  */
  
  @Schema(name = "avatar", description = "头像", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("avatar")
  public String getAvatar() {
    return avatar;
  }

  public void setAvatar(String avatar) {
    this.avatar = avatar;
  }

  public CustomerInfo point(Long point) {
    this.point = point;
    return this;
  }

  /**
   * 积分
   * @return point
  */
  
  @Schema(name = "point", description = "积分", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("point")
  public Long getPoint() {
    return point;
  }

  public void setPoint(Long point) {
    this.point = point;
  }

  public CustomerInfo rexy(CustomerInfoRexy rexy) {
    this.rexy = rexy;
    return this;
  }

  /**
   * Get rexy
   * @return rexy
  */
  @Valid 
  @Schema(name = "rexy", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("rexy")
  public CustomerInfoRexy getRexy() {
    return rexy;
  }

  public void setRexy(CustomerInfoRexy rexy) {
    this.rexy = rexy;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CustomerInfo customerInfo = (CustomerInfo) o;
    return Objects.equals(this.customerId, customerInfo.customerId) &&
        Objects.equals(this.registerAddress, customerInfo.registerAddress) &&
        Objects.equals(this.eoaAddress, customerInfo.eoaAddress) &&
        Objects.equals(this.smartAccountAddress, customerInfo.smartAccountAddress) &&
        Objects.equals(this.connectWalletAddress, customerInfo.connectWalletAddress) &&
        Objects.equals(this.email, customerInfo.email) &&
        Objects.equals(this.username, customerInfo.username) &&
        Objects.equals(this.kycLevel, customerInfo.kycLevel) &&
        Objects.equals(this.chainId, customerInfo.chainId) &&
        Objects.equals(this.authProvider, customerInfo.authProvider) &&
        Objects.equals(this.inviteCode, customerInfo.inviteCode) &&
        Objects.equals(this.avatar, customerInfo.avatar) &&
        Objects.equals(this.point, customerInfo.point) &&
        Objects.equals(this.rexy, customerInfo.rexy);
  }

  @Override
  public int hashCode() {
    return Objects.hash(customerId, registerAddress, eoaAddress, smartAccountAddress, connectWalletAddress, email, username, kycLevel, chainId, authProvider, inviteCode, avatar, point, rexy);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CustomerInfo {\n");
    sb.append("    customerId: ").append(toIndentedString(customerId)).append("\n");
    sb.append("    registerAddress: ").append(toIndentedString(registerAddress)).append("\n");
    sb.append("    eoaAddress: ").append(toIndentedString(eoaAddress)).append("\n");
    sb.append("    smartAccountAddress: ").append(toIndentedString(smartAccountAddress)).append("\n");
    sb.append("    connectWalletAddress: ").append(toIndentedString(connectWalletAddress)).append("\n");
    sb.append("    email: ").append(toIndentedString(email)).append("\n");
    sb.append("    username: ").append(toIndentedString(username)).append("\n");
    sb.append("    kycLevel: ").append(toIndentedString(kycLevel)).append("\n");
    sb.append("    chainId: ").append(toIndentedString(chainId)).append("\n");
    sb.append("    authProvider: ").append(toIndentedString(authProvider)).append("\n");
    sb.append("    inviteCode: ").append(toIndentedString(inviteCode)).append("\n");
    sb.append("    avatar: ").append(toIndentedString(avatar)).append("\n");
    sb.append("    point: ").append(toIndentedString(point)).append("\n");
    sb.append("    rexy: ").append(toIndentedString(rexy)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

