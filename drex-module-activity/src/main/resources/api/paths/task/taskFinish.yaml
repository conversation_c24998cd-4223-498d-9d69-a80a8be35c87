post:
  summary: 社交事件
  deprecated: false
  description: ''
  operationId: taskFinishEvent
  tags:
    - Activity
  parameters:
    - name: app_key
      in: header
      description: app_key
      required: true
      example: trex
      schema:
        type: string
  requestBody:
    content:
      application/json:
        schema:
          $ref: '../../components/task/TaskFinishRequest.yaml'
  responses:
    '200':
      description: Successful operation
      content:
        application/json:
          schema:
            $ref: '../../components/task/TaskFinishResponse.yaml'