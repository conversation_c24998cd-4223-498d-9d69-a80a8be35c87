get:
  tags:
    - Customer
  summary: 获取邀请二维码
  description: 获取邀请二维码
  operationId: inviteQrCode
  security:
    - jwtBearerAuth: [ ]
  parameters:
    - name: sceneCode
      in: query
      description: 场景码，用于区分不同业务场景下的规则
      required: true
      schema:
        type: string
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            $ref: '../components/InviteQrCodeResponse.yaml'
