package com.drex.web.core.converter;

import com.drex.core.api.request.NoticeDTO;
import com.drex.core.api.response.InformationDTO;
import com.drex.core.api.response.RexysDTO;
import com.drex.web.core.generated.model.v1.NewsInfo;
import com.drex.web.core.generated.model.v1.NewsInfoResponse;
import com.drex.web.core.generated.model.v1.Notice;
import com.drex.web.core.generated.model.v1.NoticeResponse;
import com.drex.web.core.generated.model.v1.Rexys;
import com.drex.web.core.generated.model.v1.RexysResponse;
import com.drex.web.core.generated.model.v1.*;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public class CoreConverter {

    public static NewsInfoResponse toNewsInfoResponse(List<InformationDTO> data) {
        NewsInfoResponse newsInfoResponse = new NewsInfoResponse();
        List<NewsInfo> newsInfos = new ArrayList<>();
        if(CollectionUtils.isEmpty(data)){
            newsInfoResponse.setObj(newsInfos);
            newsInfoResponse.success();
            return newsInfoResponse;
        }
        for(InformationDTO informationDTO : data){
            NewsInfo newsInfo = new NewsInfo();
            BeanUtils.copyProperties(informationDTO, newsInfo);
            // 确保category字段正确转换
            if(informationDTO.getCategory() != null) {
                newsInfo.setCategory(informationDTO.getCategory());
            }
            newsInfos.add(newsInfo);
        }
        // 设置响应对象的数据列表
        newsInfoResponse.setObj(newsInfos);
        newsInfoResponse.success();
        return newsInfoResponse;
    }

    public static NoticeResponse toNoticeResponse(List<NoticeDTO> data) {
        NoticeResponse noticeResponse = new NoticeResponse();
        List<Notice> notices = new ArrayList<>();
        if(CollectionUtils.isEmpty(data)){
            noticeResponse.setObj(notices);
            noticeResponse.success();
            return noticeResponse;
        }
        for(NoticeDTO noticeDTO : data){
            Notice notice = new Notice();
            BeanUtils.copyProperties(noticeDTO, notice);
            notices.add(notice);
        }
        // 设置响应对象的数据列表
        noticeResponse.setObj(notices);
        noticeResponse.success();
        return noticeResponse;
    }

    public static List<Rexys> toRexys(List<RexysDTO> data) {
        List<Rexys> rexysList = new ArrayList<>();
        data.forEach(rexysDTO -> {
            Rexys rexys = new Rexys();
            BeanUtils.copyProperties(rexysDTO, rexys);
            rexysList.add(rexys);
        });
        return rexysList;
    }

    public static DiscoveryEventsResponse toDiscoveryEventsResponse(List<InformationDTO> data) {
        DiscoveryEventsResponse newsInfoResponse = new DiscoveryEventsResponse();
        List<DiscoveryEvents> newsInfos = new ArrayList<>();
        if(CollectionUtils.isEmpty(data)){
            newsInfoResponse.setObj(newsInfos);
            newsInfoResponse.success();
            return newsInfoResponse;
        }
        for(InformationDTO informationDTO : data){
            DiscoveryEvents newsInfo = new DiscoveryEvents();
            BeanUtils.copyProperties(informationDTO, newsInfo);
            newsInfos.add(newsInfo);
        }
        // 设置响应对象的数据列表
        newsInfoResponse.setObj(newsInfos);
        newsInfoResponse.success();
        return newsInfoResponse;
    }

    public static DiscoveryBlogResponse toDiscoveryBlogResponse(List<InformationDTO> data) {
        DiscoveryBlogResponse newsInfoResponse = new DiscoveryBlogResponse();
        List<DiscoveryBlog> newsInfos = new ArrayList<>();
        if(CollectionUtils.isEmpty(data)){
            newsInfoResponse.setObj(newsInfos);
            newsInfoResponse.success();
            return newsInfoResponse;
        }
        for(InformationDTO informationDTO : data){
            DiscoveryBlog newsInfo = new DiscoveryBlog();
            BeanUtils.copyProperties(informationDTO, newsInfo);
            newsInfos.add(newsInfo);
        }
        // 设置响应对象的数据列表
        newsInfoResponse.setObj(newsInfos);
        newsInfoResponse.success();
        return newsInfoResponse;
    }
}
