get:
  tags:
    - News
  summary: discovery blog
  description: discovery blog
  operationId: discoveryBlog
  parameters:
    - name: offset
      in: query
      description: 偏移量
      required: false
      schema:
        type: integer
        format: int64
        default: 0
    - name: limit
      in: query
      description: 查询数量
      required: false
      schema:
        type: integer
        format: int64
        default: 10
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            $ref: '../components/DiscoveryBlogResponse.yaml'
