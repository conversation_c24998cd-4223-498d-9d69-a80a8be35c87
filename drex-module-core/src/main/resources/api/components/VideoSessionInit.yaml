type: object
properties:
  sessionId:
    type: string
    description: 视频观看会话ID
  reportKey:
    type: string
    description: 报告key
  hasReward:
    type: boolean
    description: 该视频是否有奖励
  stageRanges:
    type: array
    description: 观看进度阶段区间定义
    items:
      type: object
      properties:
        level:
          type: integer
          description: 阶段级别，例如：1, 2, 3
        name:
          type: string
          description: 阶段名称，例如：第一阶段
        minRatio:
          type: number
          format: double
          description: 最小比例值
        maxRatio:
          type: number
          format: double
          description: 最大比例值
        rewardAmount:
          type: string
          description: 奖励值
required:
  - sessionId
  - hasReward
  - rewardStatus