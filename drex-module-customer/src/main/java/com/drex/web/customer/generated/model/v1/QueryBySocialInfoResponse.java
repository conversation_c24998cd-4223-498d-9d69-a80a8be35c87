package com.drex.web.customer.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.drex.web.common.WebResult;
import com.drex.web.customer.generated.model.v1.QueryBySocialInfo;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * QueryBySocialInfoResponse
 */


@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class QueryBySocialInfoResponse extends WebResult {

  private QueryBySocialInfo obj;

  public QueryBySocialInfoResponse obj(QueryBySocialInfo obj) {
    this.obj = obj;
    return this;
  }

  /**
   * Get obj
   * @return obj
  */
  @Valid 
  @Schema(name = "obj", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("obj")
  public QueryBySocialInfo getObj() {
    return obj;
  }

  public void setObj(QueryBySocialInfo obj) {
    this.obj = obj;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    QueryBySocialInfoResponse queryBySocialInfoResponse = (QueryBySocialInfoResponse) o;
    return Objects.equals(this.obj, queryBySocialInfoResponse.obj) &&
        super.equals(o);
  }

  @Override
  public int hashCode() {
    return Objects.hash(obj, super.hashCode());
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class QueryBySocialInfoResponse {\n");
    sb.append("    ").append(toIndentedString(super.toString())).append("\n");
    sb.append("    obj: ").append(toIndentedString(obj)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

