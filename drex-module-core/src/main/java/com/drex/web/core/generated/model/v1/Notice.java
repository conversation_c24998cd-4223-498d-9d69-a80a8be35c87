package com.drex.web.core.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * Notice
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class Notice {

  private String id;

  private String title;

  private String subTitle;

  private String content;

  private String link;

  public Notice id(String id) {
    this.id = id;
    return this;
  }

  /**
   * 通知 id
   * @return id
  */
  
  @Schema(name = "id", description = "通知 id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("id")
  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public Notice title(String title) {
    this.title = title;
    return this;
  }

  /**
   * 标题
   * @return title
  */
  
  @Schema(name = "title", description = "标题", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("title")
  public String getTitle() {
    return title;
  }

  public void setTitle(String title) {
    this.title = title;
  }

  public Notice subTitle(String subTitle) {
    this.subTitle = subTitle;
    return this;
  }

  /**
   * 副标题
   * @return subTitle
  */
  
  @Schema(name = "subTitle", description = "副标题", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("subTitle")
  public String getSubTitle() {
    return subTitle;
  }

  public void setSubTitle(String subTitle) {
    this.subTitle = subTitle;
  }

  public Notice content(String content) {
    this.content = content;
    return this;
  }

  /**
   * 内容
   * @return content
  */
  
  @Schema(name = "content", description = "内容", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("content")
  public String getContent() {
    return content;
  }

  public void setContent(String content) {
    this.content = content;
  }

  public Notice link(String link) {
    this.link = link;
    return this;
  }

  /**
   * 链接
   * @return link
  */
  
  @Schema(name = "link", description = "链接", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("link")
  public String getLink() {
    return link;
  }

  public void setLink(String link) {
    this.link = link;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Notice notice = (Notice) o;
    return Objects.equals(this.id, notice.id) &&
        Objects.equals(this.title, notice.title) &&
        Objects.equals(this.subTitle, notice.subTitle) &&
        Objects.equals(this.content, notice.content) &&
        Objects.equals(this.link, notice.link);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, title, subTitle, content, link);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Notice {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    title: ").append(toIndentedString(title)).append("\n");
    sb.append("    subTitle: ").append(toIndentedString(subTitle)).append("\n");
    sb.append("    content: ").append(toIndentedString(content)).append("\n");
    sb.append("    link: ").append(toIndentedString(link)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

