type: object
properties:
  passportId:
    type: string
    description: 用户ID
  created:
    type: integer
    format: int64
    description: 创建时间
  userName:
    type: string
    description: 用户名称
  handleName:
    type: string
    description: 用户昵称或标识
  addresses:
    type: string
    description: 用户地址
  email:
    type: string
    description: 邮箱地址
  kycLevel:
    type: string
    description: KYC等级
  chainId:
    type: string
    description: 链id
  authProvider:
    type: string
    description: 登录方式
  inviteCode:
    type: string
    description: 邀请码
  avatar:
    type: string
    description: 头像
  holdTitles:
    type: array
    items:
      type: string
    description: 持有的称号
  passportChainId:
    type: integer
    description: passportChainId
  contractAddress:
    type: string
    description: contractAddress
  point:
    type: integer
    format: int64
    description: point
  badges:
    type: array
    items:
      "$ref": "../components/NftVO.yaml"
