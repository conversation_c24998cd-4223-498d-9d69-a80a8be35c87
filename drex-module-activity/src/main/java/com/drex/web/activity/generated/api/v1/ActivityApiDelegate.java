package com.drex.web.activity.generated.api.v1;

import com.drex.web.activity.generated.model.v1.TaskSocialEventRequest;
import com.drex.web.activity.generated.model.v1.TaskSocialEventResponse;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Generated;

/**
 * A delegate to be called by the {@link ActivityApiController}}.
 * Implement this interface with a {@link org.springframework.stereotype.Service} annotated class.
 */
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public interface ActivityApiDelegate {

    default Optional<NativeWebRequest> getRequest() {
        return Optional.empty();
    }

    /**
     * POST /task/socialEvent : 社交事件
     * 
     *
     * @param taskSocialEventRequest  (optional)
     * @return Successful operation (status code 200)
     * @see ActivityApi#taskSocialEvent
     */
    default ResponseEntity<TaskSocialEventResponse> taskSocialEvent(TaskSocialEventRequest taskSocialEventRequest) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

}
