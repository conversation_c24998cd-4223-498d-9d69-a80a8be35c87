post:
  summary: 做任务
  deprecated: false
  description: ''
  operationId: taskVerify
  security:
    - jwtBearerAuth: [ ]
  tags:
    - Activity
  parameters:
    - name: app_id
      in: header
      description: appId
      required: true
      schema:
        type: string
  requestBody:
    content:
      application/json:
        schema:
          $ref: '../../components/task/TaskVerifyRequest.yaml'
  responses:
    '200':
      description: 成功
      content:
        application/json:
          schema:
            $ref: '../../components/task/TaskVerifyResponse.yaml'