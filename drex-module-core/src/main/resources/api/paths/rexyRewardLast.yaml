get:
  tags:
    - Rexy
  summary: 查询最新恐龙蛋
  description: 查询最新恐龙蛋
  operationId: rexyRewardLast
  security:
    - jwtBearerAuth: [ ]
  parameters:
    - name: platform
      in: query
      description: 平台，可选值：X 或 YouTube
      required: true
      schema:
        type: string
        enum:
          - X
          - YouTube
    - name: content_id
      in: query
      description: 内容id
      required: false
      schema:
        type: string
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            $ref: '../components/RexyRewardLastResponse.yaml'

