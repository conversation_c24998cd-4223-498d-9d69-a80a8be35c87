package com.drex.web.core.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * SeekDetails
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class SeekDetails implements EventDataDetails {

  private BigDecimal currentTime;

  private BigDecimal previousTime;

  /**
   * Default constructor
   * @deprecated Use {@link SeekDetails#SeekDetails(BigDecimal, BigDecimal)}
   */
  @Deprecated
  public SeekDetails() {
    super();
  }

  /**
   * Constructor with only required parameters
   */
  public SeekDetails(BigDecimal currentTime, BigDecimal previousTime) {
    this.currentTime = currentTime;
    this.previousTime = previousTime;
  }

  public SeekDetails currentTime(BigDecimal currentTime) {
    this.currentTime = currentTime;
    return this;
  }

  /**
   * 跳转后播放时间（秒）
   * @return currentTime
  */
  @NotNull @Valid 
  @Schema(name = "currentTime", example = "180.0", description = "跳转后播放时间（秒）", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("currentTime")
  public BigDecimal getCurrentTime() {
    return currentTime;
  }

  public void setCurrentTime(BigDecimal currentTime) {
    this.currentTime = currentTime;
  }

  public SeekDetails previousTime(BigDecimal previousTime) {
    this.previousTime = previousTime;
    return this;
  }

  /**
   * 跳转前播放时间（秒）
   * @return previousTime
  */
  @NotNull @Valid 
  @Schema(name = "previousTime", example = "120.5", description = "跳转前播放时间（秒）", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("previousTime")
  public BigDecimal getPreviousTime() {
    return previousTime;
  }

  public void setPreviousTime(BigDecimal previousTime) {
    this.previousTime = previousTime;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SeekDetails seekDetails = (SeekDetails) o;
    return Objects.equals(this.currentTime, seekDetails.currentTime) &&
        Objects.equals(this.previousTime, seekDetails.previousTime);
  }

  @Override
  public int hashCode() {
    return Objects.hash(currentTime, previousTime);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SeekDetails {\n");
    sb.append("    currentTime: ").append(toIndentedString(currentTime)).append("\n");
    sb.append("    previousTime: ").append(toIndentedString(previousTime)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

