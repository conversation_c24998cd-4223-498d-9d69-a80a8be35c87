/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (6.6.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
package com.drex.web.core.generated.api.v1;

import com.drex.web.core.generated.model.v1.VideoEventsReportRequest;
import com.drex.web.core.generated.model.v1.VideoSessionInitRequest;
import com.drex.web.core.generated.model.v1.VideoSessionInitResponse;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
@Validated
@Tag(name = "Video", description = "视频相关接口描述")
public interface VideoApi {

    default VideoApiDelegate getDelegate() {
        return new VideoApiDelegate() {};
    }

    /**
     * POST /video/session/init : 初始化视频观看会话
     * 用户开始观看视频时创建新的会话，返回会话ID及相关奖励信息
     *
     * @param videoSessionInitRequest  (required)
     * @return 会话创建成功 (status code 200)
     *         or 请求参数错误 (status code 400)
     *         or 用户未授权 (status code 401)
     */
    @Operation(
        operationId = "initVideoSession",
        summary = "初始化视频观看会话",
        description = "用户开始观看视频时创建新的会话，返回会话ID及相关奖励信息",
        tags = { "Video" },
        responses = {
            @ApiResponse(responseCode = "200", description = "会话创建成功", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = VideoSessionInitResponse.class))
            }),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "401", description = "用户未授权")
        },
        security = {
            @SecurityRequirement(name = "jwtBearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/video/session/init",
        produces = { "application/json" },
        consumes = { "application/json" }
    )
    default ResponseEntity<VideoSessionInitResponse> initVideoSession(
        @Parameter(name = "VideoSessionInitRequest", description = "", required = true) @Valid @RequestBody VideoSessionInitRequest videoSessionInitRequest
    ) {
        return getDelegate().initVideoSession(videoSessionInitRequest);
    }


    /**
     * POST /video/events/report : 上报事件数据
     * 上报事件数据
     *
     * @param videoEventsReportRequest  (optional)
     * @return OK (status code 200)
     */
    @Operation(
        operationId = "reportEvents",
        summary = "上报事件数据",
        description = "上报事件数据",
        tags = { "Video" },
        responses = {
            @ApiResponse(responseCode = "200", description = "OK", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = Object.class))
            })
        },
        security = {
            @SecurityRequirement(name = "jwtBearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/video/events/report",
        produces = { "application/json" },
        consumes = { "application/json" }
    )
    default ResponseEntity<Object> reportEvents(
        @Parameter(name = "VideoEventsReportRequest", description = "") @Valid @RequestBody(required = false) VideoEventsReportRequest videoEventsReportRequest
    ) {
        return getDelegate().reportEvents(videoEventsReportRequest);
    }

}
