package com.drex.web.common.cache;

import com.drex.customer.api.RemotePassportService;
import com.drex.customer.api.response.PassportDTO;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class PassportCacheService extends BaseCache<String, PassportDTO>{

    @DubboReference
    private RemotePassportService remotePassportService;

    @PostConstruct
    public void init(){
        initCache();
    }

    public PassportDTO get(String passportId) {
        if (passportId == null) {
            return null;
        }
        return loadingCache.getUnchecked(passportId);
    }

    @Override
    protected PassportDTO loadEntry(String s) {
        PassportDTO response = remotePassportService.getPassportById(s);
        if(response.getHandleName() != null) {
            return response;
        }
        return null;
    }

    @Override
    protected long expireSeconds() {
        return TimeUnit.HOURS.toSeconds(3);
    }
}
