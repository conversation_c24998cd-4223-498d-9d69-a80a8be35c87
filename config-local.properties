###########################kweb application config########################
kweb.env=local
kweb.saas-id=drex-web
debug=true
app.log.path=/Users/<USER>/Dev/New/kweb/logs
zipkin.host=127.0.0.1
server.port=8081
management.server.port=9090
###########################dubbo config###################################
dubbo.name=drex-web
dubbo.protocol.port=20887
dubbo.consumer.group=kktd
dubbo.provider.group=kktd
dubbo.protocol.host=**************
#zookeeper.url=zookeeper://api.dev.dipbit.xyz:2181
zookeeper.url=127.0.0.1:2181
###########################redis config####################################
redis.host=exkikidevo.redis.singapore.rds.aliyuncs.com
redis.port=6379
redis.password=Credit2021Admin
redis.maxTotal=32
redis.dbIndex=7
##################nacos config##############################################
nacos.config.enabled=false
cloud.nacos.config.group=drex-web
cloud.nacos.config.name=drex-web
nacos.config.file-extension=properties

springdoc.api-docs.enabled=true
springdoc.swagger-ui.enabled=true
