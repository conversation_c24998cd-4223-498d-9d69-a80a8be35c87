package com.drex.web;

import lombok.SneakyThrows;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.Configuration;

@Configuration
public class DrexWebAppInitializer extends SpringBootServletInitializer {

    @SneakyThrows
    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
        builder.sources(DrexWebApplication.class);
        return builder;
    }
}


