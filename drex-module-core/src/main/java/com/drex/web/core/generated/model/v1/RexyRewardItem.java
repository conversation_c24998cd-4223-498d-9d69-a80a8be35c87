package com.drex.web.core.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * RexyRewardItem
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class RexyRewardItem {

  private Integer stage;

  private String code;

  private String point;

  private Boolean hidden;

  /**
   * Gets or Sets status
   */
  public enum StatusEnum {
    EXPIRED("EXPIRED"),
    
    CLAIMED("CLAIMED"),
    
    UNCLAIMED("UNCLAIMED");

    private String value;

    StatusEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static StatusEnum fromValue(String value) {
      for (StatusEnum b : StatusEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  private StatusEnum status;

  public RexyRewardItem stage(Integer stage) {
    this.stage = stage;
    return this;
  }

  /**
   * 奖励所属阶段
   * @return stage
  */
  
  @Schema(name = "stage", description = "奖励所属阶段", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("stage")
  public Integer getStage() {
    return stage;
  }

  public void setStage(Integer stage) {
    this.stage = stage;
  }

  public RexyRewardItem code(String code) {
    this.code = code;
    return this;
  }

  /**
   * 恐龙蛋编号，产生奖励后存在
   * @return code
  */
  
  @Schema(name = "code", description = "恐龙蛋编号，产生奖励后存在", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("code")
  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public RexyRewardItem point(String point) {
    this.point = point;
    return this;
  }

  /**
   * Get point
   * @return point
  */
  
  @Schema(name = "point", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("point")
  public String getPoint() {
    return point;
  }

  public void setPoint(String point) {
    this.point = point;
  }

  public RexyRewardItem hidden(Boolean hidden) {
    this.hidden = hidden;
    return this;
  }

  /**
   * 是否隐藏
   * @return hidden
  */
  
  @Schema(name = "hidden", description = "是否隐藏", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("hidden")
  public Boolean getHidden() {
    return hidden;
  }

  public void setHidden(Boolean hidden) {
    this.hidden = hidden;
  }

  public RexyRewardItem status(StatusEnum status) {
    this.status = status;
    return this;
  }

  /**
   * Get status
   * @return status
  */
  
  @Schema(name = "status", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("status")
  public StatusEnum getStatus() {
    return status;
  }

  public void setStatus(StatusEnum status) {
    this.status = status;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RexyRewardItem rexyRewardItem = (RexyRewardItem) o;
    return Objects.equals(this.stage, rexyRewardItem.stage) &&
        Objects.equals(this.code, rexyRewardItem.code) &&
        Objects.equals(this.point, rexyRewardItem.point) &&
        Objects.equals(this.hidden, rexyRewardItem.hidden) &&
        Objects.equals(this.status, rexyRewardItem.status);
  }

  @Override
  public int hashCode() {
    return Objects.hash(stage, code, point, hidden, status);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RexyRewardItem {\n");
    sb.append("    stage: ").append(toIndentedString(stage)).append("\n");
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    point: ").append(toIndentedString(point)).append("\n");
    sb.append("    hidden: ").append(toIndentedString(hidden)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

