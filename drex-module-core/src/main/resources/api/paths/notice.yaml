get:
  tags:
    - Notice
  summary: 查询未读消息通知列表
  description: 查询未读消息通知列表
  operationId: getNotices
  security:
    - jwtBearerAuth: [ ]
  parameters:
    - name: lastFetchNotifyTime
      in: query
      description: 最后一次拉取通知的时间
      required: false
      schema:
        type: integer
        format: int64
        default: 0
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            $ref: '../components/NoticeResponse.yaml'

