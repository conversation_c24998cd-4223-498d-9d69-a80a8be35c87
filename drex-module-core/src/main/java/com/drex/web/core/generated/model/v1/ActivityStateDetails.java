package com.drex.web.core.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonTypeName;
import java.math.BigDecimal;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * ActivityStateDetails
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class ActivityStateDetails implements EventDataDetails {

  /**
   * 用户状态
   */
  public enum StateEnum {
    ACTIVE("ACTIVE"),
    
    IDLE("IDLE"),
    
    LOCKED("LOCKED");

    private String value;

    StateEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static StateEnum fromValue(String value) {
      for (StateEnum b : StateEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  private StateEnum state;

  /**
   * Default constructor
   * @deprecated Use {@link ActivityStateDetails#ActivityStateDetails(StateEnum)}
   */
  @Deprecated
  public ActivityStateDetails() {
    super();
  }

  /**
   * Constructor with only required parameters
   */
  public ActivityStateDetails(StateEnum state) {
    this.state = state;
  }

  public ActivityStateDetails state(StateEnum state) {
    this.state = state;
    return this;
  }

  /**
   * 用户状态
   * @return state
  */
  @NotNull 
  @Schema(name = "state", example = "IDLE", description = "用户状态", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("state")
  public StateEnum getState() {
    return state;
  }

  public void setState(StateEnum state) {
    this.state = state;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ActivityStateDetails activityStateDetails = (ActivityStateDetails) o;
    return Objects.equals(this.state, activityStateDetails.state);
  }

  @Override
  public int hashCode() {
    return Objects.hash(state);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ActivityStateDetails {\n");
    sb.append("    state: ").append(toIndentedString(state)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

