package com.drex.web.customer.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * InviteInfo
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class InviteInfo {

  private String customerId;

  private String address;

  private String inviteCode;

  private Integer inviteCount;

  private Integer inviteClaimedReward;

  private Integer inviteUnClaimedReward;

  private String inviteRule;

  private String inviter;

  public InviteInfo customerId(String customerId) {
    this.customerId = customerId;
    return this;
  }

  /**
   * 用户id
   * @return customerId
  */
  
  @Schema(name = "customerId", description = "用户id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("customerId")
  public String getCustomerId() {
    return customerId;
  }

  public void setCustomerId(String customerId) {
    this.customerId = customerId;
  }

  public InviteInfo address(String address) {
    this.address = address;
    return this;
  }

  /**
   * 用户注册时的钱包地址
   * @return address
  */
  
  @Schema(name = "address", description = "用户注册时的钱包地址", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("address")
  public String getAddress() {
    return address;
  }

  public void setAddress(String address) {
    this.address = address;
  }

  public InviteInfo inviteCode(String inviteCode) {
    this.inviteCode = inviteCode;
    return this;
  }

  /**
   * 邀请码
   * @return inviteCode
  */
  
  @Schema(name = "inviteCode", description = "邀请码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("inviteCode")
  public String getInviteCode() {
    return inviteCode;
  }

  public void setInviteCode(String inviteCode) {
    this.inviteCode = inviteCode;
  }

  public InviteInfo inviteCount(Integer inviteCount) {
    this.inviteCount = inviteCount;
    return this;
  }

  /**
   * 邀请人数
   * @return inviteCount
  */
  
  @Schema(name = "inviteCount", description = "邀请人数", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("inviteCount")
  public Integer getInviteCount() {
    return inviteCount;
  }

  public void setInviteCount(Integer inviteCount) {
    this.inviteCount = inviteCount;
  }

  public InviteInfo inviteClaimedReward(Integer inviteClaimedReward) {
    this.inviteClaimedReward = inviteClaimedReward;
    return this;
  }

  /**
   * 邀请奖励
   * @return inviteClaimedReward
  */
  
  @Schema(name = "inviteClaimedReward", description = "邀请奖励", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("inviteClaimedReward")
  public Integer getInviteClaimedReward() {
    return inviteClaimedReward;
  }

  public void setInviteClaimedReward(Integer inviteClaimedReward) {
    this.inviteClaimedReward = inviteClaimedReward;
  }

  public InviteInfo inviteUnClaimedReward(Integer inviteUnClaimedReward) {
    this.inviteUnClaimedReward = inviteUnClaimedReward;
    return this;
  }

  /**
   * 邀请未领取的奖励
   * @return inviteUnClaimedReward
  */
  
  @Schema(name = "inviteUnClaimedReward", description = "邀请未领取的奖励", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("inviteUnClaimedReward")
  public Integer getInviteUnClaimedReward() {
    return inviteUnClaimedReward;
  }

  public void setInviteUnClaimedReward(Integer inviteUnClaimedReward) {
    this.inviteUnClaimedReward = inviteUnClaimedReward;
  }

  public InviteInfo inviteRule(String inviteRule) {
    this.inviteRule = inviteRule;
    return this;
  }

  /**
   * 邀请规则
   * @return inviteRule
  */
  
  @Schema(name = "inviteRule", description = "邀请规则", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("inviteRule")
  public String getInviteRule() {
    return inviteRule;
  }

  public void setInviteRule(String inviteRule) {
    this.inviteRule = inviteRule;
  }

  public InviteInfo inviter(String inviter) {
    this.inviter = inviter;
    return this;
  }

  /**
   * 邀请人
   * @return inviter
  */
  
  @Schema(name = "inviter", description = "邀请人", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("inviter")
  public String getInviter() {
    return inviter;
  }

  public void setInviter(String inviter) {
    this.inviter = inviter;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InviteInfo inviteInfo = (InviteInfo) o;
    return Objects.equals(this.customerId, inviteInfo.customerId) &&
        Objects.equals(this.address, inviteInfo.address) &&
        Objects.equals(this.inviteCode, inviteInfo.inviteCode) &&
        Objects.equals(this.inviteCount, inviteInfo.inviteCount) &&
        Objects.equals(this.inviteClaimedReward, inviteInfo.inviteClaimedReward) &&
        Objects.equals(this.inviteUnClaimedReward, inviteInfo.inviteUnClaimedReward) &&
        Objects.equals(this.inviteRule, inviteInfo.inviteRule) &&
        Objects.equals(this.inviter, inviteInfo.inviter);
  }

  @Override
  public int hashCode() {
    return Objects.hash(customerId, address, inviteCode, inviteCount, inviteClaimedReward, inviteUnClaimedReward, inviteRule, inviter);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InviteInfo {\n");
    sb.append("    customerId: ").append(toIndentedString(customerId)).append("\n");
    sb.append("    address: ").append(toIndentedString(address)).append("\n");
    sb.append("    inviteCode: ").append(toIndentedString(inviteCode)).append("\n");
    sb.append("    inviteCount: ").append(toIndentedString(inviteCount)).append("\n");
    sb.append("    inviteClaimedReward: ").append(toIndentedString(inviteClaimedReward)).append("\n");
    sb.append("    inviteUnClaimedReward: ").append(toIndentedString(inviteUnClaimedReward)).append("\n");
    sb.append("    inviteRule: ").append(toIndentedString(inviteRule)).append("\n");
    sb.append("    inviter: ").append(toIndentedString(inviter)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

