package com.drex.web.customer.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.drex.web.customer.generated.model.v1.NftProgressVO;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * all nfts in series
 */

@Schema(name = "NftVO", description = "all nfts in series")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class NftVO {

  private Long mintedCount;

  private String seriesLogo;

  private String claimableId;

  private String type;

  private Integer tier;

  private String price;

  private String logo;

  private Integer reward;

  private Boolean newTag;

  private BigDecimal process;

  private String address;

  private String tokenId;

  private String level;

  private String seriesName;

  private Long totalSupply;

  private BigDecimal maxCond;

  private Long claimTime;

  private String token;

  private String series;

  private BigDecimal minCond;

  private String name;

  @Valid
  private List<@Valid NftProgressVO> progress;

  private String category;

  private String desc;

  private String status;

  public NftVO mintedCount(Long mintedCount) {
    this.mintedCount = mintedCount;
    return this;
  }

  /**
   * minted Count
   * @return mintedCount
  */
  
  @Schema(name = "mintedCount", description = "minted Count", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("mintedCount")
  public Long getMintedCount() {
    return mintedCount;
  }

  public void setMintedCount(Long mintedCount) {
    this.mintedCount = mintedCount;
  }

  public NftVO seriesLogo(String seriesLogo) {
    this.seriesLogo = seriesLogo;
    return this;
  }

  /**
   * nft series logo
   * @return seriesLogo
  */
  
  @Schema(name = "seriesLogo", description = "nft series logo", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("seriesLogo")
  public String getSeriesLogo() {
    return seriesLogo;
  }

  public void setSeriesLogo(String seriesLogo) {
    this.seriesLogo = seriesLogo;
  }

  public NftVO claimableId(String claimableId) {
    this.claimableId = claimableId;
    return this;
  }

  /**
   * claimable id
   * @return claimableId
  */
  
  @Schema(name = "claimableId", description = "claimable id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("claimableId")
  public String getClaimableId() {
    return claimableId;
  }

  public void setClaimableId(String claimableId) {
    this.claimableId = claimableId;
  }

  public NftVO type(String type) {
    this.type = type;
    return this;
  }

  /**
   * nft type
   * @return type
  */
  
  @Schema(name = "type", description = "nft type", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("type")
  public String getType() {
    return type;
  }

  public void setType(String type) {
    this.type = type;
  }

  public NftVO tier(Integer tier) {
    this.tier = tier;
    return this;
  }

  /**
   * nft tier
   * @return tier
  */
  
  @Schema(name = "tier", description = "nft tier", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("tier")
  public Integer getTier() {
    return tier;
  }

  public void setTier(Integer tier) {
    this.tier = tier;
  }

  public NftVO price(String price) {
    this.price = price;
    return this;
  }

  /**
   * price
   * @return price
  */
  
  @Schema(name = "price", description = "price", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("price")
  public String getPrice() {
    return price;
  }

  public void setPrice(String price) {
    this.price = price;
  }

  public NftVO logo(String logo) {
    this.logo = logo;
    return this;
  }

  /**
   * nft gateway logo
   * @return logo
  */
  
  @Schema(name = "logo", description = "nft gateway logo", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("logo")
  public String getLogo() {
    return logo;
  }

  public void setLogo(String logo) {
    this.logo = logo;
  }

  public NftVO reward(Integer reward) {
    this.reward = reward;
    return this;
  }

  /**
   * reward
   * @return reward
  */
  
  @Schema(name = "reward", description = "reward", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("reward")
  public Integer getReward() {
    return reward;
  }

  public void setReward(Integer reward) {
    this.reward = reward;
  }

  public NftVO newTag(Boolean newTag) {
    this.newTag = newTag;
    return this;
  }

  /**
   * show new tag
   * @return newTag
  */
  
  @Schema(name = "newTag", description = "show new tag", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("newTag")
  public Boolean getNewTag() {
    return newTag;
  }

  public void setNewTag(Boolean newTag) {
    this.newTag = newTag;
  }

  public NftVO process(BigDecimal process) {
    this.process = process;
    return this;
  }

  /**
   * nft complete process
   * @return process
  */
  @Valid 
  @Schema(name = "process", description = "nft complete process", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("process")
  public BigDecimal getProcess() {
    return process;
  }

  public void setProcess(BigDecimal process) {
    this.process = process;
  }

  public NftVO address(String address) {
    this.address = address;
    return this;
  }

  /**
   * nft contract address
   * @return address
  */
  
  @Schema(name = "address", description = "nft contract address", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("address")
  public String getAddress() {
    return address;
  }

  public void setAddress(String address) {
    this.address = address;
  }

  public NftVO tokenId(String tokenId) {
    this.tokenId = tokenId;
    return this;
  }

  /**
   * nft token id
   * @return tokenId
  */
  
  @Schema(name = "tokenId", description = "nft token id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("tokenId")
  public String getTokenId() {
    return tokenId;
  }

  public void setTokenId(String tokenId) {
    this.tokenId = tokenId;
  }

  public NftVO level(String level) {
    this.level = level;
    return this;
  }

  /**
   * level
   * @return level
  */
  
  @Schema(name = "level", description = "level", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("level")
  public String getLevel() {
    return level;
  }

  public void setLevel(String level) {
    this.level = level;
  }

  public NftVO seriesName(String seriesName) {
    this.seriesName = seriesName;
    return this;
  }

  /**
   * nft series name
   * @return seriesName
  */
  
  @Schema(name = "seriesName", description = "nft series name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("seriesName")
  public String getSeriesName() {
    return seriesName;
  }

  public void setSeriesName(String seriesName) {
    this.seriesName = seriesName;
  }

  public NftVO totalSupply(Long totalSupply) {
    this.totalSupply = totalSupply;
    return this;
  }

  /**
   * total supply
   * @return totalSupply
  */
  
  @Schema(name = "totalSupply", description = "total supply", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("totalSupply")
  public Long getTotalSupply() {
    return totalSupply;
  }

  public void setTotalSupply(Long totalSupply) {
    this.totalSupply = totalSupply;
  }

  public NftVO maxCond(BigDecimal maxCond) {
    this.maxCond = maxCond;
    return this;
  }

  /**
   * nft condition max
   * @return maxCond
  */
  @Valid 
  @Schema(name = "maxCond", description = "nft condition max", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("maxCond")
  public BigDecimal getMaxCond() {
    return maxCond;
  }

  public void setMaxCond(BigDecimal maxCond) {
    this.maxCond = maxCond;
  }

  public NftVO claimTime(Long claimTime) {
    this.claimTime = claimTime;
    return this;
  }

  /**
   * nft claimed time
   * @return claimTime
  */
  
  @Schema(name = "claimTime", description = "nft claimed time", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("claimTime")
  public Long getClaimTime() {
    return claimTime;
  }

  public void setClaimTime(Long claimTime) {
    this.claimTime = claimTime;
  }

  public NftVO token(String token) {
    this.token = token;
    return this;
  }

  /**
   * currency
   * @return token
  */
  
  @Schema(name = "token", description = "currency", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("token")
  public String getToken() {
    return token;
  }

  public void setToken(String token) {
    this.token = token;
  }

  public NftVO series(String series) {
    this.series = series;
    return this;
  }

  /**
   * nft series type
   * @return series
  */
  
  @Schema(name = "series", description = "nft series type", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("series")
  public String getSeries() {
    return series;
  }

  public void setSeries(String series) {
    this.series = series;
  }

  public NftVO minCond(BigDecimal minCond) {
    this.minCond = minCond;
    return this;
  }

  /**
   * nft condition min
   * @return minCond
  */
  @Valid 
  @Schema(name = "minCond", description = "nft condition min", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("minCond")
  public BigDecimal getMinCond() {
    return minCond;
  }

  public void setMinCond(BigDecimal minCond) {
    this.minCond = minCond;
  }

  public NftVO name(String name) {
    this.name = name;
    return this;
  }

  /**
   * nft name
   * @return name
  */
  
  @Schema(name = "name", description = "nft name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("name")
  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public NftVO progress(List<@Valid NftProgressVO> progress) {
    this.progress = progress;
    return this;
  }

  public NftVO addProgressItem(NftProgressVO progressItem) {
    if (this.progress == null) {
      this.progress = new ArrayList<>();
    }
    this.progress.add(progressItem);
    return this;
  }

  /**
   * nft conditions and progress
   * @return progress
  */
  @Valid 
  @Schema(name = "progress", description = "nft conditions and progress", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("progress")
  public List<@Valid NftProgressVO> getProgress() {
    return progress;
  }

  public void setProgress(List<@Valid NftProgressVO> progress) {
    this.progress = progress;
  }

  public NftVO category(String category) {
    this.category = category;
    return this;
  }

  /**
   * category
   * @return category
  */
  
  @Schema(name = "category", description = "category", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("category")
  public String getCategory() {
    return category;
  }

  public void setCategory(String category) {
    this.category = category;
  }

  public NftVO desc(String desc) {
    this.desc = desc;
    return this;
  }

  /**
   * nft desc
   * @return desc
  */
  
  @Schema(name = "desc", description = "nft desc", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("desc")
  public String getDesc() {
    return desc;
  }

  public void setDesc(String desc) {
    this.desc = desc;
  }

  public NftVO status(String status) {
    this.status = status;
    return this;
  }

  /**
   * nft status(pending、claimable、claimed)
   * @return status
  */
  
  @Schema(name = "status", description = "nft status(pending、claimable、claimed)", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("status")
  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    NftVO nftVO = (NftVO) o;
    return Objects.equals(this.mintedCount, nftVO.mintedCount) &&
        Objects.equals(this.seriesLogo, nftVO.seriesLogo) &&
        Objects.equals(this.claimableId, nftVO.claimableId) &&
        Objects.equals(this.type, nftVO.type) &&
        Objects.equals(this.tier, nftVO.tier) &&
        Objects.equals(this.price, nftVO.price) &&
        Objects.equals(this.logo, nftVO.logo) &&
        Objects.equals(this.reward, nftVO.reward) &&
        Objects.equals(this.newTag, nftVO.newTag) &&
        Objects.equals(this.process, nftVO.process) &&
        Objects.equals(this.address, nftVO.address) &&
        Objects.equals(this.tokenId, nftVO.tokenId) &&
        Objects.equals(this.level, nftVO.level) &&
        Objects.equals(this.seriesName, nftVO.seriesName) &&
        Objects.equals(this.totalSupply, nftVO.totalSupply) &&
        Objects.equals(this.maxCond, nftVO.maxCond) &&
        Objects.equals(this.claimTime, nftVO.claimTime) &&
        Objects.equals(this.token, nftVO.token) &&
        Objects.equals(this.series, nftVO.series) &&
        Objects.equals(this.minCond, nftVO.minCond) &&
        Objects.equals(this.name, nftVO.name) &&
        Objects.equals(this.progress, nftVO.progress) &&
        Objects.equals(this.category, nftVO.category) &&
        Objects.equals(this.desc, nftVO.desc) &&
        Objects.equals(this.status, nftVO.status);
  }

  @Override
  public int hashCode() {
    return Objects.hash(mintedCount, seriesLogo, claimableId, type, tier, price, logo, reward, newTag, process, address, tokenId, level, seriesName, totalSupply, maxCond, claimTime, token, series, minCond, name, progress, category, desc, status);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class NftVO {\n");
    sb.append("    mintedCount: ").append(toIndentedString(mintedCount)).append("\n");
    sb.append("    seriesLogo: ").append(toIndentedString(seriesLogo)).append("\n");
    sb.append("    claimableId: ").append(toIndentedString(claimableId)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    tier: ").append(toIndentedString(tier)).append("\n");
    sb.append("    price: ").append(toIndentedString(price)).append("\n");
    sb.append("    logo: ").append(toIndentedString(logo)).append("\n");
    sb.append("    reward: ").append(toIndentedString(reward)).append("\n");
    sb.append("    newTag: ").append(toIndentedString(newTag)).append("\n");
    sb.append("    process: ").append(toIndentedString(process)).append("\n");
    sb.append("    address: ").append(toIndentedString(address)).append("\n");
    sb.append("    tokenId: ").append(toIndentedString(tokenId)).append("\n");
    sb.append("    level: ").append(toIndentedString(level)).append("\n");
    sb.append("    seriesName: ").append(toIndentedString(seriesName)).append("\n");
    sb.append("    totalSupply: ").append(toIndentedString(totalSupply)).append("\n");
    sb.append("    maxCond: ").append(toIndentedString(maxCond)).append("\n");
    sb.append("    claimTime: ").append(toIndentedString(claimTime)).append("\n");
    sb.append("    token: ").append(toIndentedString(token)).append("\n");
    sb.append("    series: ").append(toIndentedString(series)).append("\n");
    sb.append("    minCond: ").append(toIndentedString(minCond)).append("\n");
    sb.append("    name: ").append(toIndentedString(name)).append("\n");
    sb.append("    progress: ").append(toIndentedString(progress)).append("\n");
    sb.append("    category: ").append(toIndentedString(category)).append("\n");
    sb.append("    desc: ").append(toIndentedString(desc)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

