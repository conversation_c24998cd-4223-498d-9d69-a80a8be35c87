package com.drex.web.customer.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * BindSocialInfoRequest
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class BindSocialInfoRequest {

  private String customerId;

  private String platform;

  private String socialId;

  private String socialHandleName;

  private String socialProfileImage;

  private String socialEmail;

  /**
   * Default constructor
   * @deprecated Use {@link BindSocialInfoRequest#BindSocialInfoRequest(String, String, String)}
   */
  @Deprecated
  public BindSocialInfoRequest() {
    super();
  }

  /**
   * Constructor with only required parameters
   */
  public BindSocialInfoRequest(String customerId, String platform, String socialId) {
    this.customerId = customerId;
    this.platform = platform;
    this.socialId = socialId;
  }

  public BindSocialInfoRequest customerId(String customerId) {
    this.customerId = customerId;
    return this;
  }

  /**
   * Get customerId
   * @return customerId
  */
  @NotNull 
  @Schema(name = "customerId", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("customerId")
  public String getCustomerId() {
    return customerId;
  }

  public void setCustomerId(String customerId) {
    this.customerId = customerId;
  }

  public BindSocialInfoRequest platform(String platform) {
    this.platform = platform;
    return this;
  }

  /**
   * 平台
   * @return platform
  */
  @NotNull 
  @Schema(name = "platform", description = "平台", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("platform")
  public String getPlatform() {
    return platform;
  }

  public void setPlatform(String platform) {
    this.platform = platform;
  }

  public BindSocialInfoRequest socialId(String socialId) {
    this.socialId = socialId;
    return this;
  }

  /**
   * Get socialId
   * @return socialId
  */
  @NotNull 
  @Schema(name = "socialId", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("socialId")
  public String getSocialId() {
    return socialId;
  }

  public void setSocialId(String socialId) {
    this.socialId = socialId;
  }

  public BindSocialInfoRequest socialHandleName(String socialHandleName) {
    this.socialHandleName = socialHandleName;
    return this;
  }

  /**
   * Get socialHandleName
   * @return socialHandleName
  */
  
  @Schema(name = "socialHandleName", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("socialHandleName")
  public String getSocialHandleName() {
    return socialHandleName;
  }

  public void setSocialHandleName(String socialHandleName) {
    this.socialHandleName = socialHandleName;
  }

  public BindSocialInfoRequest socialProfileImage(String socialProfileImage) {
    this.socialProfileImage = socialProfileImage;
    return this;
  }

  /**
   * Get socialProfileImage
   * @return socialProfileImage
  */
  
  @Schema(name = "socialProfileImage", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("socialProfileImage")
  public String getSocialProfileImage() {
    return socialProfileImage;
  }

  public void setSocialProfileImage(String socialProfileImage) {
    this.socialProfileImage = socialProfileImage;
  }

  public BindSocialInfoRequest socialEmail(String socialEmail) {
    this.socialEmail = socialEmail;
    return this;
  }

  /**
   * Get socialEmail
   * @return socialEmail
  */
  
  @Schema(name = "socialEmail", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("socialEmail")
  public String getSocialEmail() {
    return socialEmail;
  }

  public void setSocialEmail(String socialEmail) {
    this.socialEmail = socialEmail;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    BindSocialInfoRequest bindSocialInfoRequest = (BindSocialInfoRequest) o;
    return Objects.equals(this.customerId, bindSocialInfoRequest.customerId) &&
        Objects.equals(this.platform, bindSocialInfoRequest.platform) &&
        Objects.equals(this.socialId, bindSocialInfoRequest.socialId) &&
        Objects.equals(this.socialHandleName, bindSocialInfoRequest.socialHandleName) &&
        Objects.equals(this.socialProfileImage, bindSocialInfoRequest.socialProfileImage) &&
        Objects.equals(this.socialEmail, bindSocialInfoRequest.socialEmail);
  }

  @Override
  public int hashCode() {
    return Objects.hash(customerId, platform, socialId, socialHandleName, socialProfileImage, socialEmail);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class BindSocialInfoRequest {\n");
    sb.append("    customerId: ").append(toIndentedString(customerId)).append("\n");
    sb.append("    platform: ").append(toIndentedString(platform)).append("\n");
    sb.append("    socialId: ").append(toIndentedString(socialId)).append("\n");
    sb.append("    socialHandleName: ").append(toIndentedString(socialHandleName)).append("\n");
    sb.append("    socialProfileImage: ").append(toIndentedString(socialProfileImage)).append("\n");
    sb.append("    socialEmail: ").append(toIndentedString(socialEmail)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

