package com.drex.web.core.generated.api.v1;

import com.drex.web.core.generated.model.v1.DiscoveryBlogResponse;
import com.drex.web.core.generated.model.v1.DiscoveryEventsResponse;
import com.drex.web.core.generated.model.v1.NewsInfoResponse;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Generated;

/**
 * A delegate to be called by the {@link NewsApiController}}.
 * Implement this interface with a {@link org.springframework.stereotype.Service} annotated class.
 */
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public interface NewsApiDelegate {

    default Optional<NativeWebRequest> getRequest() {
        return Optional.empty();
    }

    /**
     * GET /news/banner : 查询新闻banner
     * 查询新闻列表banner
     *
     * @return OK (status code 200)
     * @see NewsApi#banner
     */
    default ResponseEntity<NewsInfoResponse> banner() {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /news/dicovery/blog : discovery blog
     * discovery blog
     *
     * @param offset 偏移量 (optional, default to 0)
     * @param limit 查询数量 (optional, default to 10)
     * @return OK (status code 200)
     * @see NewsApi#discoveryBlog
     */
    default ResponseEntity<DiscoveryBlogResponse> discoveryBlog(Long offset,
        Long limit) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /news/discovery/events : discovery events
     * discovery events
     *
     * @param offset 偏移量 (optional, default to 0)
     * @param limit 查询数量 (optional, default to 10)
     * @return OK (status code 200)
     * @see NewsApi#discoveryEvents
     */
    default ResponseEntity<DiscoveryEventsResponse> discoveryEvents(Long offset,
        Long limit) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /news : 查询新闻列表
     * 查询新闻列表
     *
     * @param offset 偏移量 (optional, default to 0)
     * @param limit 查询数量 (optional, default to 10)
     * @return OK (status code 200)
     * @see NewsApi#news
     */
    default ResponseEntity<NewsInfoResponse> news(Long offset,
        Long limit) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

}
