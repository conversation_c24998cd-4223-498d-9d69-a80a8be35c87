package com.drex.web.customer.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * SocialUploadRequest
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class SocialUploadRequest {

  private String url;

  private String body;

  /**
   * Default constructor
   * @deprecated Use {@link SocialUploadRequest#SocialUploadRequest(String, String)}
   */
  @Deprecated
  public SocialUploadRequest() {
    super();
  }

  /**
   * Constructor with only required parameters
   */
  public SocialUploadRequest(String url, String body) {
    this.url = url;
    this.body = body;
  }

  public SocialUploadRequest url(String url) {
    this.url = url;
    return this;
  }

  /**
   * 拦截的url
   * @return url
  */
  @NotNull 
  @Schema(name = "url", description = "拦截的url", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("url")
  public String getUrl() {
    return url;
  }

  public void setUrl(String url) {
    this.url = url;
  }

  public SocialUploadRequest body(String body) {
    this.body = body;
    return this;
  }

  /**
   * url body
   * @return body
  */
  @NotNull 
  @Schema(name = "body", description = "url body", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("body")
  public String getBody() {
    return body;
  }

  public void setBody(String body) {
    this.body = body;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SocialUploadRequest socialUploadRequest = (SocialUploadRequest) o;
    return Objects.equals(this.url, socialUploadRequest.url) &&
        Objects.equals(this.body, socialUploadRequest.body);
  }

  @Override
  public int hashCode() {
    return Objects.hash(url, body);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SocialUploadRequest {\n");
    sb.append("    url: ").append(toIndentedString(url)).append("\n");
    sb.append("    body: ").append(toIndentedString(body)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

