package com.drex.web.customer.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.drex.web.customer.generated.model.v1.BindWalletSocialPlatformEnum;
import com.drex.web.customer.generated.model.v1.WalletProviderEnum;
import com.drex.web.customer.generated.model.v1.WalletTypeEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * BindWalletRequest
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class BindWalletRequest {

  private String address;

  private String signature;

  private WalletTypeEnum walletType;

  private WalletProviderEnum walletProvider;

  private BindWalletSocialPlatformEnum socialPlatform;

  private String accountDetail;

  public BindWalletRequest address(String address) {
    this.address = address;
    return this;
  }

  /**
   * 用户钱包地址
   * @return address
  */
  
  @Schema(name = "address", description = "用户钱包地址", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("address")
  public String getAddress() {
    return address;
  }

  public void setAddress(String address) {
    this.address = address;
  }

  public BindWalletRequest signature(String signature) {
    this.signature = signature;
    return this;
  }

  /**
   * 用户钱包签名
   * @return signature
  */
  
  @Schema(name = "signature", description = "用户钱包签名", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("signature")
  public String getSignature() {
    return signature;
  }

  public void setSignature(String signature) {
    this.signature = signature;
  }

  public BindWalletRequest walletType(WalletTypeEnum walletType) {
    this.walletType = walletType;
    return this;
  }

  /**
   * Get walletType
   * @return walletType
  */
  @Valid 
  @Schema(name = "walletType", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("walletType")
  public WalletTypeEnum getWalletType() {
    return walletType;
  }

  public void setWalletType(WalletTypeEnum walletType) {
    this.walletType = walletType;
  }

  public BindWalletRequest walletProvider(WalletProviderEnum walletProvider) {
    this.walletProvider = walletProvider;
    return this;
  }

  /**
   * Get walletProvider
   * @return walletProvider
  */
  @Valid 
  @Schema(name = "walletProvider", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("walletProvider")
  public WalletProviderEnum getWalletProvider() {
    return walletProvider;
  }

  public void setWalletProvider(WalletProviderEnum walletProvider) {
    this.walletProvider = walletProvider;
  }

  public BindWalletRequest socialPlatform(BindWalletSocialPlatformEnum socialPlatform) {
    this.socialPlatform = socialPlatform;
    return this;
  }

  /**
   * Get socialPlatform
   * @return socialPlatform
  */
  @Valid 
  @Schema(name = "socialPlatform", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("socialPlatform")
  public BindWalletSocialPlatformEnum getSocialPlatform() {
    return socialPlatform;
  }

  public void setSocialPlatform(BindWalletSocialPlatformEnum socialPlatform) {
    this.socialPlatform = socialPlatform;
  }

  public BindWalletRequest accountDetail(String accountDetail) {
    this.accountDetail = accountDetail;
    return this;
  }

  /**
   * 社媒账号详情
   * @return accountDetail
  */
  
  @Schema(name = "accountDetail", description = "社媒账号详情", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("accountDetail")
  public String getAccountDetail() {
    return accountDetail;
  }

  public void setAccountDetail(String accountDetail) {
    this.accountDetail = accountDetail;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    BindWalletRequest bindWalletRequest = (BindWalletRequest) o;
    return Objects.equals(this.address, bindWalletRequest.address) &&
        Objects.equals(this.signature, bindWalletRequest.signature) &&
        Objects.equals(this.walletType, bindWalletRequest.walletType) &&
        Objects.equals(this.walletProvider, bindWalletRequest.walletProvider) &&
        Objects.equals(this.socialPlatform, bindWalletRequest.socialPlatform) &&
        Objects.equals(this.accountDetail, bindWalletRequest.accountDetail);
  }

  @Override
  public int hashCode() {
    return Objects.hash(address, signature, walletType, walletProvider, socialPlatform, accountDetail);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class BindWalletRequest {\n");
    sb.append("    address: ").append(toIndentedString(address)).append("\n");
    sb.append("    signature: ").append(toIndentedString(signature)).append("\n");
    sb.append("    walletType: ").append(toIndentedString(walletType)).append("\n");
    sb.append("    walletProvider: ").append(toIndentedString(walletProvider)).append("\n");
    sb.append("    socialPlatform: ").append(toIndentedString(socialPlatform)).append("\n");
    sb.append("    accountDetail: ").append(toIndentedString(accountDetail)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

