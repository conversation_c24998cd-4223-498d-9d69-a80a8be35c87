###########################kweb application config########################
#spring.profiles.active=local
kweb.env=@kweb.env@
kweb.saas-id=@kweb.saas-id@
app.log.path=@app.log.path@
zookeeper.url=@zookeeper.url@
server.port=@server.port@
###########################dubbo config###################################
dubbo.reference.check=false
dubbo.consumer.check=false
dubbo.registry.check=false
dubbo.application.name=@dubbo.name@
dubbo.name=@dubbo.name@
dubbo.application.id=@dubbo.name@
dubbo.application.version=1.0.0
dubbo.protocol.server=netty
dubbo.protocol.name=dubbo
dubbo.protocol.port=@dubbo.protocol.port@
dubbo.protocol.threadpool=fixed
dubbo.protocol.threads=50
dubbo.protocol.queues=1000
dubbo.registry.address=zookeeper://@zookeeper.url@
dubbo.consumer.group=@dubbo.consumer.group@
dubbo.provider.group=@dubbo.provider.group@
#TODO REMOVE
#dubbo.protocol.host=@dubbo.protocol.host@
dubbo.provider.timeout=10000
dubbo.consumer.timeout=10000
dubbo.provider.retries=1
dubbo.consumer.retries=1
dubbo.consumer.filter=tracing
dubbo.provider.filter=tracing

spring.zipkin.baseUrl=http://api.dipbit.xyz:9411
spring.sleuth.sampler.probability=1.0
kiki.sleuth.enable-web=true
##################nacos config##############################################
spring.cloud.nacos.config.enabled=@nacos.config.enabled@
spring.cloud.nacos.config.group=@nacos.config.group@
spring.cloud.nacos.config.name=@nacos.config.name@
spring.cloud.nacos.config.file-extension=@nacos.config.file-extension@
