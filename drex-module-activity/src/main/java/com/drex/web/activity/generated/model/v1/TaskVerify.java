package com.drex.web.activity.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * TaskVerify
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class TaskVerify {

  private String point;

  public TaskVerify point(String point) {
    this.point = point;
    return this;
  }

  /**
   * 任务积分
   * @return point
  */
  
  @Schema(name = "point", description = "任务积分", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("point")
  public String getPoint() {
    return point;
  }

  public void setPoint(String point) {
    this.point = point;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TaskVerify taskVerify = (TaskVerify) o;
    return Objects.equals(this.point, taskVerify.point);
  }

  @Override
  public int hashCode() {
    return Objects.hash(point);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TaskVerify {\n");
    sb.append("    point: ").append(toIndentedString(point)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

