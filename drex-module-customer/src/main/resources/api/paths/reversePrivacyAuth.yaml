post:
  tags:
    - Customer
  summary: 更新用户授权隐私状态
  description: 更新用户授权隐私状态
  operationId: reversePrivacyAuth
  requestBody:
    content:
      application/json:
        schema:
          $ref: '../components/reversePrivacyAuth.yaml'
  responses:
    '200':
      description: 更新用户授权隐私状态的结果
      content:
        application/json:
          schema:
            $ref: '../components/ReservePrivacyAuthResponse.yaml'
  security:
    - bearerAuth: [ ]