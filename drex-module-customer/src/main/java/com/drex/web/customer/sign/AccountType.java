package com.drex.web.customer.sign;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Account type")
public enum AccountType {
    EOA("eoa account"),
    SMART_ACCOUNT("smart account");
    private final String description;

    AccountType(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}
