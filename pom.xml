<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.kikitrade</groupId>
        <artifactId>kiki-framework</artifactId>
        <version>3.0.0-SNAPSHOT</version>
    </parent>
    <groupId>com.drex</groupId>
    <artifactId>drex-web</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>drex-web</name>
    <description>Demo project for Spring Boot</description>
    <properties>
        <java.version>17</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <project.property.path>.</project.property.path>
        <springdoc.openapi.webmvc.version>2.0.2</springdoc.openapi.webmvc.version>
    </properties>

    <modules>
        <module>drex-web-start</module>
        <module>drex-module-customer</module>
        <module>drex-module-common</module>
        <module>drex-module-core</module>
        <module>drex-module-activity</module>
    </modules>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>3.18.1</version>
            </dependency>
            <dependency>
                <groupId>com.drex</groupId>
                <artifactId>drex-web-start</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.drex</groupId>
                <artifactId>drex-module-customer</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.drex</groupId>
                <artifactId>drex-module-activity</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.drex</groupId>
                <artifactId>drex-module-core</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.drex</groupId>
                <artifactId>drex-module-common</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.drex</groupId>
                <artifactId>drex-module-core</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>${springdoc.openapi.webmvc.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-api</artifactId>
                <version>${springdoc.openapi.webmvc.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kiki-observability-metrics-spring-boot-starter</artifactId>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>local</id>
            <properties>
                <project.profile.id>local</project.profile.id>
            </properties>
            <build>
                <filters>
                    <filter>${project.property.path}/config-local.properties</filter>
                </filters>
            </build>
            <repositories>
                <repository>
                    <id>snapshot</id>
                    <!--<url>http://nexus.marathon.l4lb.thisdcos.directory:8081/repository/maven-snapshots/</url>-->
                    <url>https://nexus-drex-dev.dipbit.xyz/repository/maven-snapshots/</url>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>

                <repository>
                    <id>public</id>
                    <!--<url>http://*************:8081/nexus/groups/public/</url>-->
                    <url>https://nexus-drex-dev.dipbit.xyz/repository/maven-public/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>

                <repository>
                    <id>releases</id>
                    <url>https://nexus-drex-dev.dipbit.xyz/repository/maven-releases/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                </repository>

                <repository>
                    <id>maven-public</id>
                    <url>https://nexus-drex-dev.dipbit.xyz/repository/maven-public/</url>
                </repository>

                <repository>
                    <id>spring-jcenter-cache</id>
                    <!--<url>http://repo.spring.io/jcenter-cache/</url>-->
                    <url>https://nexus-drex-dev.dipbit.xyz/repository/spring-jcenter-cache</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>

                <repository>
                    <id>repo.maven.apache.org</id>
                    <!--<url>https://dl.bintray.com/spark-packages/maven/</url>-->
                    <url>https://nexus-drex-dev.dipbit.xyz/repository/repo.maven.apache.org</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
            </repositories>
            <distributionManagement>
                <repository>
                    <id>releases</id>
                    <name>Local Nexus Repository</name>
                    <url>https://nexus-drex-dev.dipbit.xyz/repository/maven-releases/</url>
                </repository>
                <snapshotRepository>
                    <id>snapshot</id>
                    <name>Local Nexus Repository</name>
                    <url>https://nexus-drex-dev.dipbit.xyz/repository/maven-snapshots/</url>
                </snapshotRepository>
            </distributionManagement>
            <pluginRepositories>
                <pluginRepository>
                    <id>maven-public</id>
                    <url>https://nexus-drex-dev.dipbit.xyz/repository/maven-public/</url>
                </pluginRepository>
                <pluginRepository>
                    <id>repo.maven.apache.org</id>
                    <url>https://nexus-drex-dev.dipbit.xyz/repository/repo.maven.apache.org</url>
                </pluginRepository>
            </pluginRepositories>
        </profile>
    </profiles>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.yaml</include>
                    <include>**/*.json</include>
                    <include>**/*.txt</include>
                    <include>**/*.png</include>
                    <include>log4j2.xml</include>
                    <include>dubbo.json</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*/*.xml</include>
                    <include>**/*.tld</include>
                    <include>**/*.ftl</include>
                    <include>**/*.vm</include>
                    <include>META-INF/**</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/*.tld</include>
                </includes>
                <filtering>false</filtering>
            </resource>
        </resources>
    </build>
</project>
