package com.drex.web.customer.generated.api.v1;

import com.drex.web.customer.generated.model.v1.AddDeveloperWaitListUploadResponse;
import com.drex.web.customer.generated.model.v1.AddWaitCreatorListRequest;
import com.drex.web.customer.generated.model.v1.AddWaitCreatorListResponse;
import com.drex.web.customer.generated.model.v1.AddWaitDeveloperListRequest;
import com.drex.web.customer.generated.model.v1.AddWaitDeveloperListResponse;
import com.drex.web.customer.generated.model.v1.AddWaitListRequest;
import com.drex.web.customer.generated.model.v1.AddWaitListResponse;
import com.drex.web.customer.generated.model.v1.BindSocialInfoRequest;
import com.drex.web.customer.generated.model.v1.BindSocialInfoResponse;
import com.drex.web.customer.generated.model.v1.InviteBindRequest;
import com.drex.web.customer.generated.model.v1.InviteBindResponse;
import com.drex.web.customer.generated.model.v1.InviteInfoResponse;
import com.drex.web.customer.generated.model.v1.PrivacyAuthResponse;
import com.drex.web.customer.generated.model.v1.QueryBySocialInfoResponse;
import com.drex.web.customer.generated.model.v1.ReservePrivacyAuthResponse;
import com.drex.web.customer.generated.model.v1.ReversePrivacyAuth;
import com.drex.web.customer.generated.model.v1.SocialUploadRequest;
import com.drex.web.customer.generated.model.v1.SocialUploadResponse;
import com.drex.web.customer.generated.model.v1.UnBindSocialInfoResponse;
import com.drex.web.customer.generated.model.v1.UnbindSocialInfoRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Generated;

/**
 * A delegate to be called by the {@link CustomerApiController}}.
 * Implement this interface with a {@link org.springframework.stereotype.Service} annotated class.
 */
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public interface CustomerApiDelegate {

    default Optional<NativeWebRequest> getRequest() {
        return Optional.empty();
    }

    /**
     * POST /customers/addCreateWaitList : /customers/addWaitCreatorList
     * join the wait creator List
     *
     * @param addWaitCreatorListRequest  (optional)
     * @return Successful operation (status code 200)
     * @see CustomerApi#addWaitCreatorList
     */
    default ResponseEntity<AddWaitCreatorListResponse> addWaitCreatorList(AddWaitCreatorListRequest addWaitCreatorListRequest) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /customers/addDeveloperWaitList : /customers/addWaitDeveloperList
     * join the wait developer List
     *
     * @param addWaitDeveloperListRequest  (optional)
     * @return Successful operation (status code 200)
     * @see CustomerApi#addWaitDeveloperList
     */
    default ResponseEntity<AddWaitDeveloperListResponse> addWaitDeveloperList(AddWaitDeveloperListRequest addWaitDeveloperListRequest) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /customers/addWaitList : /customers/addWaitList
     * join the waitList from officialWebsite
     *
     * @param addWaitListRequest  (optional)
     * @return Successful operation (status code 200)
     * @see CustomerApi#addWaitList
     */
    default ResponseEntity<AddWaitListResponse> addWaitList(AddWaitListRequest addWaitListRequest) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /customers/addDeveloperWaitList/upload : 上传文件
     * 用于上传文件，返回文件名称和文件路径
     *
     * @param file 需要上传的文件 (optional)
     * @return Successful operation (status code 200)
     * @see CustomerApi#addWaitListUpload
     */
    default ResponseEntity<AddDeveloperWaitListUploadResponse> addWaitListUpload(MultipartFile file) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /customers/invites/bind : 邀请绑定
     * 邀请绑定
     *
     * @param inviteBindRequest  (optional)
     * @return OK (status code 200)
     * @see CustomerApi#bindInvite
     */
    default ResponseEntity<InviteBindResponse> bindInvite(InviteBindRequest inviteBindRequest) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /s2s/customers/bindSocialInfo : 保存社媒信息
     * 保存社媒信息
     *
     * @param appKey app_key (required)
     * @param bindSocialInfoRequest  (optional)
     * @return OK (status code 200)
     * @see CustomerApi#bindSocialInfo
     */
    default ResponseEntity<BindSocialInfoResponse> bindSocialInfo(String appKey,
        BindSocialInfoRequest bindSocialInfoRequest) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /customers/invites/me : 查询当前用户邀请信息
     * 查询当前用户邀请信息
     *
     * @return OK (status code 200)
     * @see CustomerApi#invite
     */
    default ResponseEntity<InviteInfoResponse> invite() {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /s2s/customers/invites/me : 查询当前用户邀请信息
     * 查询当前用户邀请信息
     *
     * @param cid  (required)
     * @param appKey  (required)
     * @return OK (status code 200)
     * @see CustomerApi#inviteByCid
     */
    default ResponseEntity<InviteInfoResponse> inviteByCid(String cid,
        String appKey) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /customers/social/upload : /customers/platform/social/upload
     * Upload a social media post
     *
     * @param socialUploadRequest  (optional)
     * @return Successful operation (status code 200)
     * @see CustomerApi#platformSocialUpload
     */
    default ResponseEntity<SocialUploadResponse> platformSocialUpload(SocialUploadRequest socialUploadRequest) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /customers/privacy/auth : 查询用户是否同意授权隐私协议
     * 查询用户是否同意授权隐私协议
     *
     * @return 查询用户是否同意授权隐私协议 (status code 200)
     * @see CustomerApi#privacyAuth
     */
    default ResponseEntity<PrivacyAuthResponse> privacyAuth() {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /s2s/customers/queryBySocialInfo : customers/queryBySocialInfo
     * third platform bindInfo
     *
     * @param appKey app_key (required)
     * @param platform platform (required)
     * @param passportId passportId (optional)
     * @param socialUserId socialUserId (optional)
     * @param socialHandleName socialHandleName (optional)
     * @param socialEmail socialEmail (optional)
     * @return OK (status code 200)
     * @see CustomerApi#queryBySocialInfo
     */
    default ResponseEntity<QueryBySocialInfoResponse> queryBySocialInfo(String appKey,
        String platform,
        String passportId,
        String socialUserId,
        String socialHandleName,
        String socialEmail) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /customers/privacy/reverseAuth : 更新用户授权隐私状态
     * 更新用户授权隐私状态
     *
     * @param reversePrivacyAuth  (optional)
     * @return 更新用户授权隐私状态的结果 (status code 200)
     * @see CustomerApi#reversePrivacyAuth
     */
    default ResponseEntity<ReservePrivacyAuthResponse> reversePrivacyAuth(ReversePrivacyAuth reversePrivacyAuth) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /s2s/customers/unbindSocialInfo : 删除社媒信息
     * 删除社媒信息
     *
     * @param appKey app_key (required)
     * @param unbindSocialInfoRequest  (optional)
     * @return OK (status code 200)
     * @see CustomerApi#unbindSocialInfo
     */
    default ResponseEntity<UnBindSocialInfoResponse> unbindSocialInfo(String appKey,
        UnbindSocialInfoRequest unbindSocialInfoRequest) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

}
