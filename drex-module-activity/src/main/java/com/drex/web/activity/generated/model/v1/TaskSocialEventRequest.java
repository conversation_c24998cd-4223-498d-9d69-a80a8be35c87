package com.drex.web.activity.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * TaskSocialEventRequest
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class TaskSocialEventRequest {

  /**
   * 平台，例如X
   */
  public enum PlatformEnum {
    X("X");

    private String value;

    PlatformEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static PlatformEnum fromValue(String value) {
      for (PlatformEnum b : PlatformEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  private PlatformEnum platform;

  /**
   * 行为，例如replay
   */
  public enum TypeEnum {
    REPLAY("replay");

    private String value;

    TypeEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static TypeEnum fromValue(String value) {
      for (TypeEnum b : TypeEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  private TypeEnum type;

  private String replyContent;

  private String signature;

  private String socialContentId;

  private Long time;

  private String deviceFinger;

  /**
   * Default constructor
   * @deprecated Use {@link TaskSocialEventRequest#TaskSocialEventRequest(PlatformEnum, TypeEnum, Long)}
   */
  @Deprecated
  public TaskSocialEventRequest() {
    super();
  }

  /**
   * Constructor with only required parameters
   */
  public TaskSocialEventRequest(PlatformEnum platform, TypeEnum type, Long time) {
    this.platform = platform;
    this.type = type;
    this.time = time;
  }

  public TaskSocialEventRequest platform(PlatformEnum platform) {
    this.platform = platform;
    return this;
  }

  /**
   * 平台，例如X
   * @return platform
  */
  @NotNull 
  @Schema(name = "platform", description = "平台，例如X", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("platform")
  public PlatformEnum getPlatform() {
    return platform;
  }

  public void setPlatform(PlatformEnum platform) {
    this.platform = platform;
  }

  public TaskSocialEventRequest type(TypeEnum type) {
    this.type = type;
    return this;
  }

  /**
   * 行为，例如replay
   * @return type
  */
  @NotNull 
  @Schema(name = "type", description = "行为，例如replay", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("type")
  public TypeEnum getType() {
    return type;
  }

  public void setType(TypeEnum type) {
    this.type = type;
  }

  public TaskSocialEventRequest replyContent(String replyContent) {
    this.replyContent = replyContent;
    return this;
  }

  /**
   * 回复内容
   * @return replyContent
  */
  
  @Schema(name = "replyContent", description = "回复内容", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("replyContent")
  public String getReplyContent() {
    return replyContent;
  }

  public void setReplyContent(String replyContent) {
    this.replyContent = replyContent;
  }

  public TaskSocialEventRequest signature(String signature) {
    this.signature = signature;
    return this;
  }

  /**
   * Get signature
   * @return signature
  */
  
  @Schema(name = "signature", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("signature")
  public String getSignature() {
    return signature;
  }

  public void setSignature(String signature) {
    this.signature = signature;
  }

  public TaskSocialEventRequest socialContentId(String socialContentId) {
    this.socialContentId = socialContentId;
    return this;
  }

  /**
   * 社交内容ID
   * @return socialContentId
  */
  
  @Schema(name = "socialContentId", description = "社交内容ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("socialContentId")
  public String getSocialContentId() {
    return socialContentId;
  }

  public void setSocialContentId(String socialContentId) {
    this.socialContentId = socialContentId;
  }

  public TaskSocialEventRequest time(Long time) {
    this.time = time;
    return this;
  }

  /**
   * Get time
   * @return time
  */
  @NotNull 
  @Schema(name = "time", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("time")
  public Long getTime() {
    return time;
  }

  public void setTime(Long time) {
    this.time = time;
  }

  public TaskSocialEventRequest deviceFinger(String deviceFinger) {
    this.deviceFinger = deviceFinger;
    return this;
  }

  /**
   * 设备指纹
   * @return deviceFinger
  */
  
  @Schema(name = "deviceFinger", description = "设备指纹", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("deviceFinger")
  public String getDeviceFinger() {
    return deviceFinger;
  }

  public void setDeviceFinger(String deviceFinger) {
    this.deviceFinger = deviceFinger;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TaskSocialEventRequest taskSocialEventRequest = (TaskSocialEventRequest) o;
    return Objects.equals(this.platform, taskSocialEventRequest.platform) &&
        Objects.equals(this.type, taskSocialEventRequest.type) &&
        Objects.equals(this.replyContent, taskSocialEventRequest.replyContent) &&
        Objects.equals(this.signature, taskSocialEventRequest.signature) &&
        Objects.equals(this.socialContentId, taskSocialEventRequest.socialContentId) &&
        Objects.equals(this.time, taskSocialEventRequest.time) &&
        Objects.equals(this.deviceFinger, taskSocialEventRequest.deviceFinger);
  }

  @Override
  public int hashCode() {
    return Objects.hash(platform, type, replyContent, signature, socialContentId, time, deviceFinger);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TaskSocialEventRequest {\n");
    sb.append("    platform: ").append(toIndentedString(platform)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    replyContent: ").append(toIndentedString(replyContent)).append("\n");
    sb.append("    signature: ").append(toIndentedString(signature)).append("\n");
    sb.append("    socialContentId: ").append(toIndentedString(socialContentId)).append("\n");
    sb.append("    time: ").append(toIndentedString(time)).append("\n");
    sb.append("    deviceFinger: ").append(toIndentedString(deviceFinger)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

