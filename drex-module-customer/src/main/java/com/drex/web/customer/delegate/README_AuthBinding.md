# 第三方平台绑定授权URL处理

## 功能概述

在 `AuthApiDelegateImpl.thirdBindings()` 方法中实现了自动为未绑定的第三方平台设置授权URL的功能。

## 核心逻辑

### 1. 数据流程

```
1. 获取用户的第三方平台绑定列表 (bindingsDTOList)
2. 调用任务服务获取授权URL列表 (tasksAuthUrls)
3. 遍历绑定列表，为status=0的平台匹配授权URL
4. 按platform名称不区分大小写进行匹配
5. 将匹配到的authUrl设置到binding的connectUrl属性
```

### 2. 关键方法

#### `processBindingsWithAuthUrls()`
- **功能**: 处理绑定列表，为未绑定的平台设置授权URL
- **参数**: 
  - `bindingsDTOList`: 第三方平台绑定列表
  - `tasksAuthUrls`: 任务授权URL数据
- **逻辑**: 
  - 验证输入参数
  - 遍历绑定列表
  - 只处理status为"0"的未绑定平台
  - 调用查找方法获取匹配的授权URL

#### `findAuthUrlForPlatform()`
- **功能**: 在授权URL列表中查找指定平台的授权URL
- **参数**:
  - `platform`: 平台名称
  - `authUrlList`: 授权URL列表
- **返回**: 匹配的授权URL，未找到返回null
- **特点**: 支持不区分大小写的平台名称匹配

## 数据结构

### ThirdBindingsDTO
```java
{
    "platform": "X",           // 平台名称
    "status": "0",             // 绑定状态：0-未绑定，1-已绑定
    "connectUrl": "",          // 授权地址（需要设置的字段）
    "socialHandleName": "",    // 社交账号名称
    "socialProfileImage": ""   // 社交账号头像
}
```

### WebResultAuthUrlsVO
```java
{
    "obj": [
        {
            "platform": "X",                                    // 平台名称
            "authUrl": "https://api.twitter.com/oauth/authorize" // 授权URL
        },
        {
            "platform": "YouTube",
            "authUrl": "https://accounts.google.com/oauth/authorize"
        }
    ]
}
```

## 使用示例

### 输入数据示例

**绑定列表 (bindingsDTOList)**:
```json
[
    {
        "platform": "X",
        "status": "0",
        "connectUrl": "",
        "socialHandleName": "",
        "socialProfileImage": ""
    },
    {
        "platform": "YouTube", 
        "status": "0",
        "connectUrl": "",
        "socialHandleName": "",
        "socialProfileImage": ""
    },
    {
        "platform": "Discord",
        "status": "1",
        "connectUrl": "https://discord.com/oauth2/authorize",
        "socialHandleName": "user123",
        "socialProfileImage": "https://cdn.discord.com/avatar.png"
    }
]
```

**授权URL列表 (tasksAuthUrls)**:
```json
{
    "obj": [
        {
            "platform": "X",
            "authUrl": "https://api.twitter.com/oauth/authorize"
        },
        {
            "platform": "youtube",  // 注意：不区分大小写
            "authUrl": "https://accounts.google.com/oauth/authorize"
        },
        {
            "platform": "Telegram",
            "authUrl": "https://oauth.telegram.org/auth"
        }
    ]
}
```

### 处理结果

处理后的绑定列表：
```json
[
    {
        "platform": "X",
        "status": "0",
        "connectUrl": "https://api.twitter.com/oauth/authorize",  // ✅ 已设置
        "socialHandleName": "",
        "socialProfileImage": ""
    },
    {
        "platform": "YouTube",
        "status": "0", 
        "connectUrl": "https://accounts.google.com/oauth/authorize", // ✅ 已设置（不区分大小写匹配）
        "socialHandleName": "",
        "socialProfileImage": ""
    },
    {
        "platform": "Discord",
        "status": "1",
        "connectUrl": "https://discord.com/oauth2/authorize", // ⏭️ 已绑定，保持不变
        "socialHandleName": "user123",
        "socialProfileImage": "https://cdn.discord.com/avatar.png"
    }
]
```

## 日志输出

### 正常处理日志
```
INFO  - 开始处理绑定列表，bindingsDTOList size: 3, tasksAuthUrls size: 3
DEBUG - 处理未绑定的平台: X
INFO  - 为平台 X 设置connectUrl: https://api.twitter.com/oauth/authorize
DEBUG - 处理未绑定的平台: YouTube
INFO  - 为平台 YouTube 设置connectUrl: https://accounts.google.com/oauth/authorize
DEBUG - 平台 Discord 已绑定，status: 1，跳过处理
```

### 异常情况日志
```
WARN  - 绑定列表为空，跳过处理
WARN  - 任务授权URL列表为空，跳过处理
WARN  - 未找到平台 Facebook 的授权URL
ERROR - 获取任务授权URL失败: Connection timeout
```

## 特性

1. **健壮性**: 完善的空值检查和异常处理
2. **灵活性**: 支持不区分大小写的平台名称匹配
3. **性能**: 找到匹配项后立即跳出循环
4. **可维护性**: 代码结构清晰，职责分离
5. **可测试性**: 提供了完整的单元测试

## 注意事项

1. 只处理 `status` 为 `"0"` 的未绑定平台
2. 平台名称匹配不区分大小写
3. 如果找不到匹配的授权URL，会记录警告日志但不会抛出异常
4. 已绑定的平台（status="1"）不会被修改
5. 异常情况下会记录错误日志，但不会影响整体流程
