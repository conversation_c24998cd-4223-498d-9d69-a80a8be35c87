/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (6.6.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
package com.drex.web.core.generated.api.v1;

import com.drex.web.core.generated.model.v1.DiscoveryBlogResponse;
import com.drex.web.core.generated.model.v1.DiscoveryEventsResponse;
import com.drex.web.core.generated.model.v1.NewsInfoResponse;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
@Validated
@Tag(name = "News", description = "新闻相关接口描述")
public interface NewsApi {

    default NewsApiDelegate getDelegate() {
        return new NewsApiDelegate() {};
    }

    /**
     * GET /news/banner : 查询新闻banner
     * 查询新闻列表banner
     *
     * @return OK (status code 200)
     */
    @Operation(
        operationId = "banner",
        summary = "查询新闻banner",
        description = "查询新闻列表banner",
        tags = { "News" },
        responses = {
            @ApiResponse(responseCode = "200", description = "OK", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = NewsInfoResponse.class))
            })
        },
        security = {
            @SecurityRequirement(name = "jwtBearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/news/banner",
        produces = { "application/json" }
    )
    default ResponseEntity<NewsInfoResponse> banner(
        
    ) {
        return getDelegate().banner();
    }


    /**
     * GET /news/dicovery/blog : discovery blog
     * discovery blog
     *
     * @param offset 偏移量 (optional, default to 0)
     * @param limit 查询数量 (optional, default to 10)
     * @return OK (status code 200)
     */
    @Operation(
        operationId = "discoveryBlog",
        summary = "discovery blog",
        description = "discovery blog",
        tags = { "News" },
        responses = {
            @ApiResponse(responseCode = "200", description = "OK", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = DiscoveryBlogResponse.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/news/dicovery/blog",
        produces = { "application/json" }
    )
    default ResponseEntity<DiscoveryBlogResponse> discoveryBlog(
        @Parameter(name = "offset", description = "偏移量", in = ParameterIn.QUERY) @Valid @RequestParam(value = "offset", required = false, defaultValue = "0") Long offset,
        @Parameter(name = "limit", description = "查询数量", in = ParameterIn.QUERY) @Valid @RequestParam(value = "limit", required = false, defaultValue = "10") Long limit
    ) {
        return getDelegate().discoveryBlog(offset, limit);
    }


    /**
     * GET /news/discovery/events : discovery events
     * discovery events
     *
     * @param offset 偏移量 (optional, default to 0)
     * @param limit 查询数量 (optional, default to 10)
     * @return OK (status code 200)
     */
    @Operation(
        operationId = "discoveryEvents",
        summary = "discovery events",
        description = "discovery events",
        tags = { "News" },
        responses = {
            @ApiResponse(responseCode = "200", description = "OK", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = DiscoveryEventsResponse.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/news/discovery/events",
        produces = { "application/json" }
    )
    default ResponseEntity<DiscoveryEventsResponse> discoveryEvents(
        @Parameter(name = "offset", description = "偏移量", in = ParameterIn.QUERY) @Valid @RequestParam(value = "offset", required = false, defaultValue = "0") Long offset,
        @Parameter(name = "limit", description = "查询数量", in = ParameterIn.QUERY) @Valid @RequestParam(value = "limit", required = false, defaultValue = "10") Long limit
    ) {
        return getDelegate().discoveryEvents(offset, limit);
    }


    /**
     * GET /news : 查询新闻列表
     * 查询新闻列表
     *
     * @param offset 偏移量 (optional, default to 0)
     * @param limit 查询数量 (optional, default to 10)
     * @return OK (status code 200)
     */
    @Operation(
        operationId = "news",
        summary = "查询新闻列表",
        description = "查询新闻列表",
        tags = { "News" },
        responses = {
            @ApiResponse(responseCode = "200", description = "OK", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = NewsInfoResponse.class))
            })
        },
        security = {
            @SecurityRequirement(name = "jwtBearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/news",
        produces = { "application/json" }
    )
    default ResponseEntity<NewsInfoResponse> news(
        @Parameter(name = "offset", description = "偏移量", in = ParameterIn.QUERY) @Valid @RequestParam(value = "offset", required = false, defaultValue = "0") Long offset,
        @Parameter(name = "limit", description = "查询数量", in = ParameterIn.QUERY) @Valid @RequestParam(value = "limit", required = false, defaultValue = "10") Long limit
    ) {
        return getDelegate().news(offset, limit);
    }

}
