package com.drex.web.customer.delegate;

import java.io.File;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

import com.drex.core.api.request.SocialConstant;
import com.drex.customer.api.request.CustomerBindQuery;
import com.drex.customer.api.response.CustomerBindDTO;
import com.drex.customer.api.response.PassportDTO;
import com.drex.web.common.ErrorCode;
import com.drex.web.common.PassportHolder;
import com.drex.web.common.config.WebProperties;
import com.drex.web.customer.generated.model.v1.*;
import com.kikitrade.gateway.client.QuestsApi;
import com.kikitrade.gateway.client.invoker.ApiException;
import com.kikitrade.gateway.client.model.WebResultAssetsSummary;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson2.JSON;
import com.aliyun.oss.model.PutObjectResult;
import com.drex.asset.api.model.constant.AssetType;
import com.drex.asset.api.model.request.AssetQueryRequest;
import com.drex.asset.api.service.RemoteAssetService;
import com.drex.core.api.RemoteRexyBasketService;
import com.drex.core.api.common.RexyConstant;
import com.drex.core.api.request.RexyBasketsRequest;
import com.drex.core.api.request.SocialConstant;
import com.drex.core.api.response.CustomerRexyBasketsDTO;
import com.drex.customer.api.RemoteCustomerBindService;
import com.drex.customer.api.RemoteCustomerService;
import com.drex.customer.api.response.CustomerBindDTO;
import com.drex.customer.api.response.PassportDTO;
import com.drex.web.common.Constant;
import com.drex.web.common.PassportHolder;
import com.drex.web.common.utils.FileUtil;
import com.drex.web.common.utils.UploadAliyunOssUtil;
import com.drex.web.customer.generated.api.v1.CustomerApiDelegate;
import com.drex.web.customer.generated.model.v1.*;
import com.drex.web.customer.service.AuthService;
import com.drex.web.customer.service.CustomerMapperStruct;
import com.kikitrade.framework.common.model.Response;
import com.kikitrade.framework.common.util.BeanUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

@Slf4j
@Service
public class CustomerApiDelegateImpl implements CustomerApiDelegate {

    @Resource
    private CustomerMapperStruct customerMapperStruct;
    @DubboReference
    private RemoteAssetService remoteAssetService;
    @DubboReference
    private RemoteRexyBasketService remoteRexyBasketService;
    @DubboReference
    private RemoteCustomerService remoteCustomerService;
    @Resource
    private UploadAliyunOssUtil uploadAliyunOssUtil;
    @Resource
    public AuthService authService;
    @DubboReference
    private RemoteCustomerBindService remoteCustomerBindService;
    @Resource
    private RedisTemplate<String,String> redisTemplate;
    @Resource
    private WebProperties webProperties;
    @Resource
    private QuestsApi questsApi;

    private static final String EMAIL_REGEX =
            "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$";
    private static final Pattern EMAIL_PATTERN = Pattern.compile(EMAIL_REGEX);
    private static final String PRIVACY_AUTH_REJECT_KEY = "customer:privacyAuth:reject:";

    @Override
    public ResponseEntity<AddWaitListResponse> addWaitList(AddWaitListRequest addWaitListRequest) {
        AddWaitListResponse addWaitListResponse = new AddWaitListResponse();
        addWaitListResponse.setSuccess(true);

        log.info("addWaitListRequest: {}", addWaitListRequest);
        if (addWaitListRequest == null
                || StringUtils.isBlank(addWaitListRequest.getName())
                || StringUtils.isBlank(addWaitListRequest.getEmail())) {
            addWaitListResponse.setSuccess(false);
            addWaitListResponse.setMessage("invalid request, name, project or email is empty");
            return ResponseEntity.badRequest().body(addWaitListResponse);
        }
        String email = addWaitListRequest.getEmail();
        if (!isValidEmail(email)) {
            addWaitListResponse.setSuccess(false);
            addWaitListResponse.setMessage("Invalid email");
            return ResponseEntity.badRequest().body(addWaitListResponse);
        }

        com.drex.customer.api.request.AddWaitListRequest request = new com.drex.customer.api.request.AddWaitListRequest();
        request.setEmail(addWaitListRequest.getEmail());
        request.setProject(addWaitListRequest.getProject());
        request.setEvmAddress(addWaitListRequest.getEvmAddress());
        request.setName(addWaitListRequest.getName());

        Response<Boolean> response = remoteCustomerService.addWaitList(request);
        if (response.isSuccess()) {
            return ResponseEntity.ok(addWaitListResponse);
        } else {
            addWaitListResponse.setSuccess(false);
            return ResponseEntity.badRequest().body(addWaitListResponse);
        }
    }

    @Override
    public ResponseEntity<InviteInfoResponse> invite() {
        InviteInfo inviteInfo = new InviteInfo();
        Response<Long> inviteCountResponse = remoteCustomerService.countByReferrerId(PassportHolder.passport().getPassportId());
        Response<String> referrer = remoteCustomerService.getReferrerByCustomerId(PassportHolder.passport().getPassportId());
        inviteInfo.setInviteCount(inviteCountResponse.isSuccess() ? inviteCountResponse.getData().intValue() : 0);
        inviteInfo.setCustomerId(PassportHolder.passport().getPassportId());
        inviteInfo.setInviteCode(PassportHolder.passport().getReferralCode());
        inviteInfo.setAddress(PassportHolder.passport().getAddress());
        inviteInfo.setInviteRule(webProperties.getInviteRule());
        inviteInfo.setInviter(referrer.isSuccess() ? referrer.getData() : "");
        try {
            WebResultAssetsSummary assetsSummary = questsApi.saas(webProperties.getSaasId()).member().assetsSummary(webProperties.getSaasId(), PassportHolder.passport().getPassportId(), List.of("invited_register"));
            inviteInfo.setInviteClaimedReward(assetsSummary.getObj() == null ? 0 : assetsSummary.getObj().intValue());
        } catch (ApiException e) {
            log.error("获取邀请奖励失败", e);
            inviteInfo.setInviteClaimedReward(0);
        }
        InviteInfoResponse response = new InviteInfoResponse();
        response.success();
        response.setObj(inviteInfo);
        return ResponseEntity.ok(response);
    }

    /**
     * POST /customers/invites/bind : 邀请绑定
     * 邀请绑定
     *
     * @param inviteBindRequest (optional)
     * @return OK (status code 200)
     */
    @Override
    public ResponseEntity<InviteBindResponse> bindInvite(InviteBindRequest inviteBindRequest) {
        log.info("bindInvite:{}", inviteBindRequest);
        InviteBindResponse inviteBindResponse = new InviteBindResponse();
        if(StringUtils.isBlank(inviteBindRequest.getInviteCode())){
            inviteBindResponse.success();
            return ResponseEntity.ok(inviteBindResponse);
        }
        Response<Boolean> response = remoteCustomerService.bindInviteCode(PassportHolder.passport().getPassportId(), inviteBindRequest.getInviteCode());
        if(response.isSuccess()){
            inviteBindResponse.success();
            return ResponseEntity.ok(inviteBindResponse);
        }
        inviteBindResponse.fail(response);
        return ResponseEntity.badRequest().body(inviteBindResponse);
    }

    @Override
    public ResponseEntity<AddWaitCreatorListResponse> addWaitCreatorList(AddWaitCreatorListRequest addWaitCreatorListRequest) {
        AddWaitCreatorListResponse addWaitListResponse = new AddWaitCreatorListResponse();
        addWaitListResponse.setSuccess(true);
        com.drex.customer.api.request.AddWaitCreatorListRequest request = BeanUtil.copyProperties(addWaitCreatorListRequest, new com.drex.customer.api.request.AddWaitCreatorListRequest());
        request.setContentTypes(JSON.toJSONString(addWaitCreatorListRequest.getContentTypes()));
        request.setInterestReasons(JSON.toJSONString(addWaitCreatorListRequest.getInterestReasons()));
        Response<Boolean> response = remoteCustomerService.addWaitCreatorList(request);
        if (response.isSuccess()) {
            return ResponseEntity.ok(addWaitListResponse);
        } else {
            addWaitListResponse.setSuccess(false);
            return ResponseEntity.badRequest().body(addWaitListResponse);
        }
    }

    @Override
    public ResponseEntity<AddWaitDeveloperListResponse> addWaitDeveloperList(AddWaitDeveloperListRequest addWaitDeveloperListRequest) {
        AddWaitDeveloperListResponse addWaitListResponse = new AddWaitDeveloperListResponse();
        addWaitListResponse.setSuccess(true);
        com.drex.customer.api.request.AddWaitDeveloperListRequest request = BeanUtil.copyProperties(addWaitDeveloperListRequest, new com.drex.customer.api.request.AddWaitDeveloperListRequest());
        request.setProjectCategory(JSON.toJSONString(addWaitDeveloperListRequest.getProjectCategory()));
        request.setSupportType(JSON.toJSONString(addWaitDeveloperListRequest.getSupportType()));
        Response<Boolean> response = remoteCustomerService.addWaitDeveloperList(request);
        if (response.isSuccess()) {
            return ResponseEntity.ok(addWaitListResponse);
        } else {
            addWaitListResponse.setSuccess(false);
            return ResponseEntity.badRequest().body(addWaitListResponse);
        }
    }

    /**
     * POST /customers/addDeveloperWaitList/upload : 上传文件
     * 用于上传文件，返回文件名称和文件路径
     *
     * @param file 需要上传的文件 (optional)
     * @return Successful operation (status code 200)
     */
    @Override
    public ResponseEntity<AddDeveloperWaitListUploadResponse> addWaitListUpload(MultipartFile file) {
        File f = FileUtil.toFile(file);
        PutObjectResult result = uploadAliyunOssUtil.putObject("drex/core/wait", f.getName(), f);
        log.info("uploadImage result: {}", result);
        String path = uploadAliyunOssUtil.getLocation("drex/core/wait", f.getName());

        AddDeveloperWaitListUploadResponseAllOfObj obj = new AddDeveloperWaitListUploadResponseAllOfObj();
        obj.setFileName(f.getName());
        obj.setFilePath(path);

        AddDeveloperWaitListUploadResponse response = new AddDeveloperWaitListUploadResponse();
        response.success();
        response.setObj(obj);
        return ResponseEntity.ok(response);
    }

    public static boolean isValidEmail(String email) {
        if (email == null) {
            return false;
        }
        return EMAIL_PATTERN.matcher(email).matches();
    }

    /**
     * POST /customers/social/upload : /customers/platform/social/upload
     * Upload a social media post
     *
     * @param socialUploadRequest (optional)
     * @return Successful operation (status code 200)
     */
    @Override
    public ResponseEntity<SocialUploadResponse> platformSocialUpload(SocialUploadRequest socialUploadRequest) {
        PassportDTO passport = PassportHolder.passport();
        if(passport != null){
            log.info("customerId:{}, platformSocialUpload:{}", passport.getPassportId(), socialUploadRequest);
        }
        SocialUploadResponse socialUploadResponse = new SocialUploadResponse();
        socialUploadResponse.success();
        return ResponseEntity.ok(socialUploadResponse);
    }

    @Override
    public ResponseEntity<PrivacyAuthResponse> privacyAuth() {
        PassportDTO passport = PassportHolder.passport();
        PrivacyAuthResponse privacyAuthResponse = new PrivacyAuthResponse();
        PrivacyAuthVO privacyAuthVO = new PrivacyAuthVO();

        CustomerBindDTO customerBindX = remoteCustomerBindService.findByCustomerId(passport.getPassportId(), SocialConstant.PlatformEnum.X.name());
        CustomerBindDTO customerBindYoutube = remoteCustomerBindService.findByCustomerId(passport.getPassportId(), SocialConstant.PlatformEnum.YouTube.name());
        if (Objects.isNull(customerBindX) && Objects.isNull(customerBindYoutube)) {
            // auth数据都不存在,代表着用户从未点击过同意
            privacyAuthVO.setPrivacyAuthX(false);
            privacyAuthVO.setPrivacyAuthYoutube(false);
            // PRIVACY_AUTH_REJECT_KEY + passport.getPassportId() 缓存中该用户今日未拒绝过 则弹出标识为true
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            String todayStr = sdf.format(new Date());
            String rejectCache = redisTemplate.opsForValue().get(PRIVACY_AUTH_REJECT_KEY + passport.getPassportId());
            if (Objects.isNull(rejectCache) || !todayStr.equals(rejectCache)) {
                privacyAuthVO.setPopAuthPrompt(true);
            }
        }
        if (Objects.nonNull(customerBindX) && customerBindX.getPrivacyAuth()) {
            privacyAuthVO.setPrivacyAuthX(true);
        }
        if (Objects.nonNull(customerBindYoutube) && customerBindYoutube.getPrivacyAuth()) {
            privacyAuthVO.setPrivacyAuthYoutube(true);
        }
        privacyAuthResponse.setObj(privacyAuthVO);
        privacyAuthResponse.success();
        return ResponseEntity.ok(privacyAuthResponse);
    }

    @Override
    public ResponseEntity<ReservePrivacyAuthResponse> reversePrivacyAuth(ReversePrivacyAuth reversePrivacyAuth) {
        PassportDTO passport = PassportHolder.passport();
        ReservePrivacyAuthResponse reservePrivacyAuthResponse = new ReservePrivacyAuthResponse();
        log.info("customerId:{}, reservePrivacyAuth platform:{}, action:{}", passport.getPassportId(), reversePrivacyAuth.getPlatform(), reversePrivacyAuth.getAction());
        if ("reject".equalsIgnoreCase(reversePrivacyAuth.getAction())) {
            // 用户点击拒绝时传 reject，后端缓存用户id + 拒绝日期
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            redisTemplate.opsForValue().set(PRIVACY_AUTH_REJECT_KEY + passport.getPassportId(), sdf.format(new Date()), 24 * 60 * 60, TimeUnit.SECONDS);
            log.info("customerId:{}, reservePrivacyAuth reject success", passport.getPassportId());
            reservePrivacyAuthResponse.setObj(true);
            reservePrivacyAuthResponse.success();
            return ResponseEntity.ok(reservePrivacyAuthResponse);
        } else if (StringUtils.isBlank(reversePrivacyAuth.getAction())) {
            // 一旦参数type为空，代表用户同时打开或单独打开同意，就清除缓存
            redisTemplate.delete(PRIVACY_AUTH_REJECT_KEY + passport.getPassportId());
            reservePrivacyAuthResponse.setObj(true);
            reservePrivacyAuthResponse.success();
            log.info("customerId:{}, reservePrivacyAuth accept, clear privacyAuthRejectCache success", passport.getPassportId());
        }

        try {
            boolean b = remoteCustomerBindService.reservePrivacyAuth(passport.getPassportId(), reversePrivacyAuth.getPlatform());
            if (b) {
                reservePrivacyAuthResponse.setObj(true);
                reservePrivacyAuthResponse.success();
            }
            return ResponseEntity.ok(reservePrivacyAuthResponse);
        } catch (Exception e) {
            log.error("reservePrivacyAuth error", e);
            return ResponseEntity.badRequest().body(reservePrivacyAuthResponse);
        }
    }

    @Override
    public ResponseEntity<BindSocialInfoResponse> bindSocialInfo(String appKey, BindSocialInfoRequest bindSocialInfoRequest) {
        log.info("bindSocialInfoRequest:{}", JSON.toJSONString(bindSocialInfoRequest));
        BindSocialInfoResponse bindSocialInfoResponse = new BindSocialInfoResponse();
        com.drex.customer.api.request.BindSocialInfoRequest bindSocialInfoRequest1 = new com.drex.customer.api.request.BindSocialInfoRequest();
        BeanUtil.copyProperties(bindSocialInfoRequest, bindSocialInfoRequest1);
        //平台映射
        bindSocialInfoRequest1.setPlatform(Objects.requireNonNull(Constant.SocialPlatform.fromPlatformName(bindSocialInfoRequest.getPlatform())).name());

        boolean res = remoteCustomerBindService.bindSocial(bindSocialInfoRequest1);
        if (res) {
            bindSocialInfoResponse.success();
        }
        return ResponseEntity.ok(bindSocialInfoResponse);
    }

    @Override
    public ResponseEntity<InviteInfoResponse> inviteByCid(String cid, String appKey) {
        InviteInfo inviteInfo = new InviteInfo();
        Response<String> referrer = remoteCustomerService.getReferrerByCustomerId(cid);
        inviteInfo.setInviter(referrer.isSuccess() ? referrer.getData() : "");
        InviteInfoResponse response = new InviteInfoResponse();
        response.success();
        response.setObj(inviteInfo);
        return ResponseEntity.ok(response);
    }

    @Override
    public ResponseEntity<QueryBySocialInfoResponse> queryBySocialInfo(String appKey, String platform, String passportId, String socialUserId, String socialHandleName, String socialEmail) {
        QueryBySocialInfoResponse queryBySocialInfoResponse = new QueryBySocialInfoResponse();

        // 验证必要参数
        if (StringUtils.isBlank(platform)) {
            queryBySocialInfoResponse.fail(ErrorCode.PARAMS_ILLEGAL.getCode(), "Platform cannot be empty");
            return ResponseEntity.badRequest().body(queryBySocialInfoResponse);
        }

        QueryBySocialInfo queryBySocialInfo = new QueryBySocialInfo();
        CustomerBindQuery customerBindQuery = CustomerBindQuery.builder()
                .passportId(passportId)
                .socialPlatform(platform)
                .socialUserId(socialUserId)
                .socialUserName(socialHandleName)
                .socialEmail(socialEmail)
                .build();

        try {
            CustomerBindDTO customerBindDTO = remoteCustomerBindService.queryBySocialInfo(customerBindQuery);
            if (customerBindDTO != null) {
                queryBySocialInfo.setPassportId(customerBindDTO.getCustomerId());
                queryBySocialInfo.setSocialUserId(customerBindDTO.getSocialUserId());
                queryBySocialInfo.setSocialHandleName(customerBindDTO.getSocialHandleName());
                queryBySocialInfo.setSocialEmail(customerBindDTO.getSocialEmail());
                queryBySocialInfo.setPlatform(customerBindDTO.getSocialPlatform());
                queryBySocialInfoResponse.setObj(queryBySocialInfo);
            }
            queryBySocialInfoResponse.success();
            return ResponseEntity.ok(queryBySocialInfoResponse);
        } catch (Exception e) {
            log.error("Error querying social info: {}", e.getMessage(), e);
            queryBySocialInfoResponse.fail(ErrorCode.PARAMS_ILLEGAL.getCode(), "Platform cannot be empty");
            return ResponseEntity.badRequest().body(queryBySocialInfoResponse);
        }
    }

    @Override
    public ResponseEntity<UnBindSocialInfoResponse> unbindSocialInfo(String appKey, UnbindSocialInfoRequest unbindSocialInfoRequest) {
        log.info("unbindSocialInfoRequest:{}", JSON.toJSONString(unbindSocialInfoRequest));
        UnBindSocialInfoResponse unBindSocialInfoResponse = new UnBindSocialInfoResponse();
        String platform = Objects.requireNonNull(Constant.SocialPlatform.fromPlatformName(unbindSocialInfoRequest.getPlatform())).name();
        boolean res = remoteCustomerBindService.unbindSocial(unbindSocialInfoRequest.getCustomerId(), platform);
        log.info("unbindSocialInfo res:{}", res);
        if (res) {
            unBindSocialInfoResponse.success();
        }
        return ResponseEntity.ok(unBindSocialInfoResponse);
    }
}
