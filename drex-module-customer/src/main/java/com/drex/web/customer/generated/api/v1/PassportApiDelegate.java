package com.drex.web.customer.generated.api.v1;

import com.drex.web.customer.generated.model.v1.PassportConnectResponse;
import com.drex.web.customer.generated.model.v1.PassportResponse;
import com.drex.web.customer.generated.model.v1.SetHandleNameResponse;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Generated;

/**
 * A delegate to be called by the {@link PassportApiController}}.
 * Implement this interface with a {@link org.springframework.stereotype.Service} annotated class.
 */
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public interface PassportApiDelegate {

    default Optional<NativeWebRequest> getRequest() {
        return Optional.empty();
    }

    /**
     * GET /passport/connect : 查询用户passportConnect钱包连接信息
     * 返回用户的passportConnect钱包连接信息
     *
     * @return 成功获取passportConnect (status code 200)
     * @see PassportApi#getPassportConnect
     */
    default ResponseEntity<PassportConnectResponse> getPassportConnect() {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /passport/me : 查询用户passport信息
     * 返回用户的passport基本信息，包括customerId、created时间和handleName
     *
     * @return 成功获取passport信息 (status code 200)
     * @see PassportApi#getPassportInfo
     */
    default ResponseEntity<PassportResponse> getPassportInfo() {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /passport/setHandleName : 保存用户handleName信息
     * 保存用户handleName信息
     *
     * @param handleName handleName (required)
     * @return 保存用户handleName信息 (status code 200)
     * @see PassportApi#setHandleName
     */
    default ResponseEntity<SetHandleNameResponse> setHandleName(String handleName) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

}
