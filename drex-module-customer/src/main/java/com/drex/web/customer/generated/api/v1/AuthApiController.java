package com.drex.web.customer.generated.api.v1;

import com.drex.web.customer.generated.model.v1.AccessTokenResponse;
import com.drex.web.customer.generated.model.v1.BindWalletRequest;
import com.drex.web.customer.generated.model.v1.BindWalletResponse;
import com.drex.web.customer.generated.model.v1.ChallengeResponse;
import com.drex.web.customer.generated.model.v1.LoginRequest;
import com.drex.web.customer.generated.model.v1.LogoutResponse;
import com.drex.web.customer.generated.model.v1.ThirdAuthorizeRequest;
import com.drex.web.customer.generated.model.v1.ThirdAuthorizeResponse;
import com.drex.web.customer.generated.model.v1.ThirdBindingsResponse;
import com.drex.web.customer.generated.model.v1.WalletTypeEnum;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.CookieValue;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.*;
import javax.validation.Valid;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
@Controller
@RequestMapping("${openapi.kweb.base-path:/v1}")
public class AuthApiController implements AuthApi {

    private final AuthApiDelegate delegate;

    public AuthApiController(@Autowired(required = false) AuthApiDelegate delegate) {
        this.delegate = Optional.ofNullable(delegate).orElse(new AuthApiDelegate() {});
    }

    @Override
    public AuthApiDelegate getDelegate() {
        return delegate;
    }

}
