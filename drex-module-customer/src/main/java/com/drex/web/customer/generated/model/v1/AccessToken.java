package com.drex.web.customer.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * AccessToken
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class AccessToken {

  private String accessToken;

  private String refreshToken;

  private Boolean isNewUser;

  private Boolean isNewPassport;

  private Boolean isRedeemedSuccessfully;

  public AccessToken accessToken(String accessToken) {
    this.accessToken = accessToken;
    return this;
  }

  /**
   * 访问token
   * @return accessToken
  */
  
  @Schema(name = "accessToken", description = "访问token", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("accessToken")
  public String getAccessToken() {
    return accessToken;
  }

  public void setAccessToken(String accessToken) {
    this.accessToken = accessToken;
  }

  public AccessToken refreshToken(String refreshToken) {
    this.refreshToken = refreshToken;
    return this;
  }

  /**
   * 刷新token
   * @return refreshToken
  */
  
  @Schema(name = "refreshToken", description = "刷新token", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("refreshToken")
  public String getRefreshToken() {
    return refreshToken;
  }

  public void setRefreshToken(String refreshToken) {
    this.refreshToken = refreshToken;
  }

  public AccessToken isNewUser(Boolean isNewUser) {
    this.isNewUser = isNewUser;
    return this;
  }

  /**
   * 是否新用户
   * @return isNewUser
  */
  
  @Schema(name = "isNewUser", description = "是否新用户", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("isNewUser")
  public Boolean getIsNewUser() {
    return isNewUser;
  }

  public void setIsNewUser(Boolean isNewUser) {
    this.isNewUser = isNewUser;
  }

  public AccessToken isNewPassport(Boolean isNewPassport) {
    this.isNewPassport = isNewPassport;
    return this;
  }

  /**
   * 是否新通行证
   * @return isNewPassport
  */
  
  @Schema(name = "isNewPassport", description = "是否新通行证", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("isNewPassport")
  public Boolean getIsNewPassport() {
    return isNewPassport;
  }

  public void setIsNewPassport(Boolean isNewPassport) {
    this.isNewPassport = isNewPassport;
  }

  public AccessToken isRedeemedSuccessfully(Boolean isRedeemedSuccessfully) {
    this.isRedeemedSuccessfully = isRedeemedSuccessfully;
    return this;
  }

  /**
   * 是否兑换成功
   * @return isRedeemedSuccessfully
  */
  
  @Schema(name = "isRedeemedSuccessfully", description = "是否兑换成功", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("isRedeemedSuccessfully")
  public Boolean getIsRedeemedSuccessfully() {
    return isRedeemedSuccessfully;
  }

  public void setIsRedeemedSuccessfully(Boolean isRedeemedSuccessfully) {
    this.isRedeemedSuccessfully = isRedeemedSuccessfully;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    AccessToken accessToken = (AccessToken) o;
    return Objects.equals(this.accessToken, accessToken.accessToken) &&
        Objects.equals(this.refreshToken, accessToken.refreshToken) &&
        Objects.equals(this.isNewUser, accessToken.isNewUser) &&
        Objects.equals(this.isNewPassport, accessToken.isNewPassport) &&
        Objects.equals(this.isRedeemedSuccessfully, accessToken.isRedeemedSuccessfully);
  }

  @Override
  public int hashCode() {
    return Objects.hash(accessToken, refreshToken, isNewUser, isNewPassport, isRedeemedSuccessfully);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class AccessToken {\n");
    sb.append("    accessToken: ").append(toIndentedString(accessToken)).append("\n");
    sb.append("    refreshToken: ").append(toIndentedString(refreshToken)).append("\n");
    sb.append("    isNewUser: ").append(toIndentedString(isNewUser)).append("\n");
    sb.append("    isNewPassport: ").append(toIndentedString(isNewPassport)).append("\n");
    sb.append("    isRedeemedSuccessfully: ").append(toIndentedString(isRedeemedSuccessfully)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

