package com.drex.web.common;

import com.drex.customer.api.response.PassportDTO;

/**
 * <AUTHOR>
 * @Description
 * @date 2021/07/30 18:00
 */
public class PassportHolder {

    private static final ThreadLocal<PassportDTO> holder = new ThreadLocal<>();

    public static void bind(PassportDTO context) {
        if (null == context) {
            return;
        }
        holder.set(context);
    }

    public static PassportDTO passport() {
        return holder.get();
    }

    public static void clear() {
        holder.remove();
    }
}
