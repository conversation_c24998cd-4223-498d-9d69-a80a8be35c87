package com.drex.web.core.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.ArrayList;
import java.util.List;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * NewsInfo
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class NewsInfo {

  private String id;

  private String type;

  private String name;

  private String logo;

  private String title;

  private String subTitle;

  private String image;

  private String link;

  @Valid
  private List<String> category;

  private String tag;

  private Long date;

  private Boolean isRecommend;

  private Boolean isReward;

  public NewsInfo id(String id) {
    this.id = id;
    return this;
  }

  /**
   * id
   * @return id
  */
  
  @Schema(name = "id", description = "id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("id")
  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public NewsInfo type(String type) {
    this.type = type;
    return this;
  }

  /**
   * 类型 Article、News、YouTube、DApp、XPost
   * @return type
  */
  
  @Schema(name = "type", description = "类型 Article、News、YouTube、DApp、XPost", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("type")
  public String getType() {
    return type;
  }

  public void setType(String type) {
    this.type = type;
  }

  public NewsInfo name(String name) {
    this.name = name;
    return this;
  }

  /**
   * 名称
   * @return name
  */
  
  @Schema(name = "name", description = "名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("name")
  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public NewsInfo logo(String logo) {
    this.logo = logo;
    return this;
  }

  /**
   * logo链接
   * @return logo
  */
  
  @Schema(name = "logo", description = "logo链接", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("logo")
  public String getLogo() {
    return logo;
  }

  public void setLogo(String logo) {
    this.logo = logo;
  }

  public NewsInfo title(String title) {
    this.title = title;
    return this;
  }

  /**
   * 标题
   * @return title
  */
  
  @Schema(name = "title", description = "标题", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("title")
  public String getTitle() {
    return title;
  }

  public void setTitle(String title) {
    this.title = title;
  }

  public NewsInfo subTitle(String subTitle) {
    this.subTitle = subTitle;
    return this;
  }

  /**
   * 子标题
   * @return subTitle
  */
  
  @Schema(name = "subTitle", description = "子标题", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("subTitle")
  public String getSubTitle() {
    return subTitle;
  }

  public void setSubTitle(String subTitle) {
    this.subTitle = subTitle;
  }

  public NewsInfo image(String image) {
    this.image = image;
    return this;
  }

  /**
   * 图片
   * @return image
  */
  
  @Schema(name = "image", description = "图片", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("image")
  public String getImage() {
    return image;
  }

  public void setImage(String image) {
    this.image = image;
  }

  public NewsInfo link(String link) {
    this.link = link;
    return this;
  }

  /**
   * 外部链接
   * @return link
  */
  
  @Schema(name = "link", description = "外部链接", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("link")
  public String getLink() {
    return link;
  }

  public void setLink(String link) {
    this.link = link;
  }

  public NewsInfo category(List<String> category) {
    this.category = category;
    return this;
  }

  public NewsInfo addCategoryItem(String categoryItem) {
    if (this.category == null) {
      this.category = new ArrayList<>();
    }
    this.category.add(categoryItem);
    return this;
  }

  /**
   * 标签
   * @return category
  */
  
  @Schema(name = "category", description = "标签", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("category")
  public List<String> getCategory() {
    return category;
  }

  public void setCategory(List<String> category) {
    this.category = category;
  }

  public NewsInfo tag(String tag) {
    this.tag = tag;
    return this;
  }

  /**
   * 来源icon图标
   * @return tag
  */
  
  @Schema(name = "tag", description = "来源icon图标", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("tag")
  public String getTag() {
    return tag;
  }

  public void setTag(String tag) {
    this.tag = tag;
  }

  public NewsInfo date(Long date) {
    this.date = date;
    return this;
  }

  /**
   * 发布时间
   * @return date
  */
  
  @Schema(name = "date", description = "发布时间", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("date")
  public Long getDate() {
    return date;
  }

  public void setDate(Long date) {
    this.date = date;
  }

  public NewsInfo isRecommend(Boolean isRecommend) {
    this.isRecommend = isRecommend;
    return this;
  }

  /**
   * 是否推荐置顶
   * @return isRecommend
  */
  
  @Schema(name = "isRecommend", description = "是否推荐置顶", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("isRecommend")
  public Boolean getIsRecommend() {
    return isRecommend;
  }

  public void setIsRecommend(Boolean isRecommend) {
    this.isRecommend = isRecommend;
  }

  public NewsInfo isReward(Boolean isReward) {
    this.isReward = isReward;
    return this;
  }

  /**
   * 是否有奖励
   * @return isReward
  */
  
  @Schema(name = "isReward", description = "是否有奖励", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("isReward")
  public Boolean getIsReward() {
    return isReward;
  }

  public void setIsReward(Boolean isReward) {
    this.isReward = isReward;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    NewsInfo newsInfo = (NewsInfo) o;
    return Objects.equals(this.id, newsInfo.id) &&
        Objects.equals(this.type, newsInfo.type) &&
        Objects.equals(this.name, newsInfo.name) &&
        Objects.equals(this.logo, newsInfo.logo) &&
        Objects.equals(this.title, newsInfo.title) &&
        Objects.equals(this.subTitle, newsInfo.subTitle) &&
        Objects.equals(this.image, newsInfo.image) &&
        Objects.equals(this.link, newsInfo.link) &&
        Objects.equals(this.category, newsInfo.category) &&
        Objects.equals(this.tag, newsInfo.tag) &&
        Objects.equals(this.date, newsInfo.date) &&
        Objects.equals(this.isRecommend, newsInfo.isRecommend) &&
        Objects.equals(this.isReward, newsInfo.isReward);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, type, name, logo, title, subTitle, image, link, category, tag, date, isRecommend, isReward);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class NewsInfo {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    name: ").append(toIndentedString(name)).append("\n");
    sb.append("    logo: ").append(toIndentedString(logo)).append("\n");
    sb.append("    title: ").append(toIndentedString(title)).append("\n");
    sb.append("    subTitle: ").append(toIndentedString(subTitle)).append("\n");
    sb.append("    image: ").append(toIndentedString(image)).append("\n");
    sb.append("    link: ").append(toIndentedString(link)).append("\n");
    sb.append("    category: ").append(toIndentedString(category)).append("\n");
    sb.append("    tag: ").append(toIndentedString(tag)).append("\n");
    sb.append("    date: ").append(toIndentedString(date)).append("\n");
    sb.append("    isRecommend: ").append(toIndentedString(isRecommend)).append("\n");
    sb.append("    isReward: ").append(toIndentedString(isReward)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

