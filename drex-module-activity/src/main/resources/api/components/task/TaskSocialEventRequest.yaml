type: object
properties:
  platform:
    type: string
    description: 平台，例如X
    enum:
      - X
  type:
    type: string
    description: 行为，例如replay
    enum:
      - replay
  replyContent:
    type: string
    description: 回复内容
  signature:
    type: string
  socialContentId:
    type: string
    description: 社交内容ID
  time:
    type: integer
    format: int64
  deviceFinger:
    type: string
    description: 设备指纹
required:
  - type
  - platform
  - time