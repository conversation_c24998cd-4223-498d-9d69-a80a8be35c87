package com.drex.web.core.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * DiscoveryEvents
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class DiscoveryEvents {

  private String id;

  private String title;

  private String summary;

  private String image;

  private String link;

  private String organizer;

  private String location;

  private Long activityStartTime;

  private Long activityEndTime;

  private String remainingTime;

  public DiscoveryEvents id(String id) {
    this.id = id;
    return this;
  }

  /**
   * id
   * @return id
  */
  
  @Schema(name = "id", description = "id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("id")
  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public DiscoveryEvents title(String title) {
    this.title = title;
    return this;
  }

  /**
   * 标题
   * @return title
  */
  
  @Schema(name = "title", description = "标题", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("title")
  public String getTitle() {
    return title;
  }

  public void setTitle(String title) {
    this.title = title;
  }

  public DiscoveryEvents summary(String summary) {
    this.summary = summary;
    return this;
  }

  /**
   * 摘要
   * @return summary
  */
  
  @Schema(name = "summary", description = "摘要", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("summary")
  public String getSummary() {
    return summary;
  }

  public void setSummary(String summary) {
    this.summary = summary;
  }

  public DiscoveryEvents image(String image) {
    this.image = image;
    return this;
  }

  /**
   * 图片
   * @return image
  */
  
  @Schema(name = "image", description = "图片", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("image")
  public String getImage() {
    return image;
  }

  public void setImage(String image) {
    this.image = image;
  }

  public DiscoveryEvents link(String link) {
    this.link = link;
    return this;
  }

  /**
   * 外部链接
   * @return link
  */
  
  @Schema(name = "link", description = "外部链接", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("link")
  public String getLink() {
    return link;
  }

  public void setLink(String link) {
    this.link = link;
  }

  public DiscoveryEvents organizer(String organizer) {
    this.organizer = organizer;
    return this;
  }

  /**
   * 组织者
   * @return organizer
  */
  
  @Schema(name = "organizer", description = "组织者", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("organizer")
  public String getOrganizer() {
    return organizer;
  }

  public void setOrganizer(String organizer) {
    this.organizer = organizer;
  }

  public DiscoveryEvents location(String location) {
    this.location = location;
    return this;
  }

  /**
   * 地点
   * @return location
  */
  
  @Schema(name = "location", description = "地点", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("location")
  public String getLocation() {
    return location;
  }

  public void setLocation(String location) {
    this.location = location;
  }

  public DiscoveryEvents activityStartTime(Long activityStartTime) {
    this.activityStartTime = activityStartTime;
    return this;
  }

  /**
   * 开始时间
   * @return activityStartTime
  */
  
  @Schema(name = "activityStartTime", description = "开始时间", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("activityStartTime")
  public Long getActivityStartTime() {
    return activityStartTime;
  }

  public void setActivityStartTime(Long activityStartTime) {
    this.activityStartTime = activityStartTime;
  }

  public DiscoveryEvents activityEndTime(Long activityEndTime) {
    this.activityEndTime = activityEndTime;
    return this;
  }

  /**
   * 结束时间
   * @return activityEndTime
  */
  
  @Schema(name = "activityEndTime", description = "结束时间", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("activityEndTime")
  public Long getActivityEndTime() {
    return activityEndTime;
  }

  public void setActivityEndTime(Long activityEndTime) {
    this.activityEndTime = activityEndTime;
  }

  public DiscoveryEvents remainingTime(String remainingTime) {
    this.remainingTime = remainingTime;
    return this;
  }

  /**
   * 剩余时间
   * @return remainingTime
  */
  
  @Schema(name = "remainingTime", description = "剩余时间", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("remainingTime")
  public String getRemainingTime() {
    return remainingTime;
  }

  public void setRemainingTime(String remainingTime) {
    this.remainingTime = remainingTime;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DiscoveryEvents discoveryEvents = (DiscoveryEvents) o;
    return Objects.equals(this.id, discoveryEvents.id) &&
        Objects.equals(this.title, discoveryEvents.title) &&
        Objects.equals(this.summary, discoveryEvents.summary) &&
        Objects.equals(this.image, discoveryEvents.image) &&
        Objects.equals(this.link, discoveryEvents.link) &&
        Objects.equals(this.organizer, discoveryEvents.organizer) &&
        Objects.equals(this.location, discoveryEvents.location) &&
        Objects.equals(this.activityStartTime, discoveryEvents.activityStartTime) &&
        Objects.equals(this.activityEndTime, discoveryEvents.activityEndTime) &&
        Objects.equals(this.remainingTime, discoveryEvents.remainingTime);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, title, summary, image, link, organizer, location, activityStartTime, activityEndTime, remainingTime);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DiscoveryEvents {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    title: ").append(toIndentedString(title)).append("\n");
    sb.append("    summary: ").append(toIndentedString(summary)).append("\n");
    sb.append("    image: ").append(toIndentedString(image)).append("\n");
    sb.append("    link: ").append(toIndentedString(link)).append("\n");
    sb.append("    organizer: ").append(toIndentedString(organizer)).append("\n");
    sb.append("    location: ").append(toIndentedString(location)).append("\n");
    sb.append("    activityStartTime: ").append(toIndentedString(activityStartTime)).append("\n");
    sb.append("    activityEndTime: ").append(toIndentedString(activityEndTime)).append("\n");
    sb.append("    remainingTime: ").append(toIndentedString(remainingTime)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

