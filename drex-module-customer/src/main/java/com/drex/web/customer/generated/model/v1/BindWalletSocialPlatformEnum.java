package com.drex.web.customer.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonValue;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * bind wallet social platform enum
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public enum BindWalletSocialPlatformEnum {
  
  GOOGLE("GOOGLE"),
  
  APPLE("APPLE"),
  
  FACEBOOK("FACEBOOK"),
  
  DISCORD("DISCORD"),
  
  <PERSON>IN<PERSON>("LINE"),
  
  X("X"),
  
  COINBASE("COINBASE"),
  
  FARCASTER("FARCASTER"),
  
  TELEGRAM("TELEGRAM"),
  
  GITHUB("GITHUB"),
  
  TWITCH("TWITCH"),
  
  STEAM("STEAM"),
  
  GUEST("GUEST"),
  
  BACKEND("BACKEND"),
  
  EMAIL("EMAIL"),
  
  PHONE("PHONE"),
  
  PASSKEY("PASSKEY"),
  
  WALLET("WALLET");

  private String value;

  BindWalletSocialPlatformEnum(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return value;
  }

  @Override
  public String toString() {
    return String.valueOf(value);
  }

  @JsonCreator
  public static BindWalletSocialPlatformEnum fromValue(String value) {
    for (BindWalletSocialPlatformEnum b : BindWalletSocialPlatformEnum.values()) {
      if (b.value.equals(value)) {
        return b;
      }
    }
    throw new IllegalArgumentException("Unexpected value '" + value + "'");
  }
}

