package com.drex.web.core.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * RexyClaimRequest
 */

@JsonTypeName("rexyClaim_request")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class RexyClaimRequest {

  private String transactionHash;

  private String callData;

  public RexyClaimRequest transactionHash(String transactionHash) {
    this.transactionHash = transactionHash;
    return this;
  }

  /**
   * 交易哈希
   * @return transactionHash
  */
  
  @Schema(name = "transactionHash", description = "交易哈希", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("transactionHash")
  public String getTransactionHash() {
    return transactionHash;
  }

  public void setTransactionHash(String transactionHash) {
    this.transactionHash = transactionHash;
  }

  public RexyClaimRequest callData(String callData) {
    this.callData = callData;
    return this;
  }

  /**
   * 调用数据
   * @return callData
  */
  
  @Schema(name = "callData", description = "调用数据", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("callData")
  public String getCallData() {
    return callData;
  }

  public void setCallData(String callData) {
    this.callData = callData;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RexyClaimRequest rexyClaimRequest = (RexyClaimRequest) o;
    return Objects.equals(this.transactionHash, rexyClaimRequest.transactionHash) &&
        Objects.equals(this.callData, rexyClaimRequest.callData);
  }

  @Override
  public int hashCode() {
    return Objects.hash(transactionHash, callData);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RexyClaimRequest {\n");
    sb.append("    transactionHash: ").append(toIndentedString(transactionHash)).append("\n");
    sb.append("    callData: ").append(toIndentedString(callData)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

