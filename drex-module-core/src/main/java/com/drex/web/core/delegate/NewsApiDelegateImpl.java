package com.drex.web.core.delegate;

import com.drex.core.api.RemoteInformationService;
import com.drex.core.api.response.InformationDTO;
import com.drex.web.common.PassportHolder;
import com.drex.web.core.converter.CoreConverter;
import com.drex.web.core.generated.api.v1.NewsApiDelegate;
import com.drex.web.core.generated.model.v1.NewsInfoResponse;
import com.drex.web.core.generated.model.v1.DiscoveryBlogResponse;
import com.drex.web.core.generated.model.v1.DiscoveryEventsResponse;
import com.kikitrade.framework.common.model.Response;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class NewsApiDelegateImpl implements NewsApiDelegate {

    private static final String EXTENSION_HOME = "extension_home";
    private static final String DISCOVERY_BLOG = "discovery_blog";
    private static final String DISCOVERY_EVENTS = "discovery_events";

    @DubboReference
    private RemoteInformationService remoteInformationService;

    @Override
    public ResponseEntity<NewsInfoResponse> banner() {
        String passportId = PassportHolder.passport().getPassportId();
        NewsInfoResponse newsInfoResponse = new NewsInfoResponse();
        Response<List<InformationDTO>> informationDTOListResponse = remoteInformationService.getByRecommend(passportId, true, EXTENSION_HOME);
        if (informationDTOListResponse.isSuccess()) {
            List<InformationDTO> data = informationDTOListResponse.getData();

            newsInfoResponse = CoreConverter.toNewsInfoResponse(data);
            return ResponseEntity.ok(newsInfoResponse);
        } else {
            newsInfoResponse.fail(informationDTOListResponse.getCode(), informationDTOListResponse.getMessage());
            return ResponseEntity.badRequest().body(newsInfoResponse);
        }
    }

    @Override
    public ResponseEntity<NewsInfoResponse> news(Long offset, Long limit) {
        String passportId = PassportHolder.passport().getPassportId();
        NewsInfoResponse newsInfoResponse = new NewsInfoResponse();
        Response<List<InformationDTO>> newsInfoList = remoteInformationService.listAll(passportId, offset.intValue(), limit.intValue(), EXTENSION_HOME);
        if (newsInfoList.isSuccess()) {
            List<InformationDTO> data = newsInfoList.getData();
            newsInfoResponse = CoreConverter.toNewsInfoResponse(data);
            return ResponseEntity.ok(newsInfoResponse);
        } else {
            newsInfoResponse.fail(newsInfoList.getCode(), newsInfoList.getMessage());
            return ResponseEntity.badRequest().body(newsInfoResponse);
        }
    }

    /**
     * GET /news/discovery/events : discovery events
     * discovery events
     *
     * @param offset 偏移量 (optional, default to 0)
     * @param limit  查询数量 (optional, default to 10)
     * @return OK (status code 200)
     */
    @Override
    public ResponseEntity<DiscoveryEventsResponse> discoveryEvents(Long offset, Long limit) {
        DiscoveryEventsResponse discoveryEventsResponse = new DiscoveryEventsResponse();
        Response<List<InformationDTO>> newsInfoList = remoteInformationService.listAll(null, offset.intValue(), limit.intValue(), DISCOVERY_EVENTS);
        if (newsInfoList.isSuccess()) {
            List<InformationDTO> data = newsInfoList.getData();
            discoveryEventsResponse = CoreConverter.toDiscoveryEventsResponse(data);
            return ResponseEntity.ok(discoveryEventsResponse);
        } else {
            discoveryEventsResponse.fail(newsInfoList.getCode(), newsInfoList.getMessage());
            return ResponseEntity.badRequest().body(discoveryEventsResponse);
        }
    }

    /**
     * GET /news/dicovery/blog : discovery blog
     * discovery blog
     *
     * @param offset 偏移量 (optional, default to 0)
     * @param limit  查询数量 (optional, default to 10)
     * @return OK (status code 200)
     */
    @Override
    public ResponseEntity<DiscoveryBlogResponse> discoveryBlog(Long offset, Long limit) {
        DiscoveryBlogResponse discoveryBlogResponse = new DiscoveryBlogResponse();
        Response<List<InformationDTO>> newsInfoList = remoteInformationService.listAll(null, offset.intValue(), limit.intValue(), DISCOVERY_BLOG);
        if (newsInfoList.isSuccess()) {
            List<InformationDTO> data = newsInfoList.getData();
            discoveryBlogResponse = CoreConverter.toDiscoveryBlogResponse(data);
            return ResponseEntity.ok(discoveryBlogResponse);
        } else {
            discoveryBlogResponse.fail(newsInfoList.getCode(), newsInfoList.getMessage());
            return ResponseEntity.badRequest().body(discoveryBlogResponse);
        }
    }
}
