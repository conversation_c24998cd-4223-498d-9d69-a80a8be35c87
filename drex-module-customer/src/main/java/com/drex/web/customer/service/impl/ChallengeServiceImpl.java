package com.drex.web.customer.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.drex.web.common.CacheKey;
import com.drex.web.common.Constant;
import com.drex.web.common.config.WebProperties;
import com.drex.web.common.utils.JwtUtils;
import com.drex.web.common.utils.Web3Utils;
import com.drex.web.customer.enums.WalletType;
import com.drex.web.customer.generated.model.v1.Challenge;
import com.drex.web.customer.service.ChallengeService;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.time.Instant;
import java.util.Date;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class ChallengeServiceImpl implements ChallengeService {

    @Resource
    private RedisTemplate<String,String> redisTemplate;
    @Resource
    private WebProperties webProperties;

    private static final long expireTime = TimeUnit.MINUTES.toSeconds(10);

    @Override
    public Challenge generateChallenge(String address, String type, String walletType) {
        String cacheKey = CacheKey.Auth.CHALLENGE.getKey() + type + ":" + walletType + ":" + address;
        String signatureMessage;

        if(Constant.ChallengeType.bind_wallet.name().equals(type)){
            if(WalletType.SOLANA.name().equals(walletType)) {
                // SOL钱包绑定签名消息
                signatureMessage = generateSolanaSignatureMessage(address);
            } else {
                // EVM钱包绑定签名消息
                signatureMessage = Web3Utils.getSignatureMessage(Web3Utils.signatureTemplateForBind,
                    new String[]{address, RandomUtil.randomString(32), Instant.now().toString()});
            }
        } else {
            if(WalletType.SOLANA.name().equals(walletType)) {
                // SOL钱包登录签名消息
                signatureMessage = generateSolanaLoginMessage(address);
            } else {
                // EVM钱包登录签名消息
                signatureMessage = Web3Utils.getSignatureMessage(
                    address, "", String.valueOf(webProperties.getChainId()),
                    new SecureRandom().ints(66, 0, 16).mapToObj(Integer::toHexString).reduce("", String::concat),
                    Instant.now().toString(),
                    Instant.now().plusSeconds(expireTime).toString(),
                    Instant.now().minusSeconds(expireTime).toString()
                );
            }
        }

        redisTemplate.opsForValue().set(cacheKey, signatureMessage, expireTime, TimeUnit.SECONDS);
        return new Challenge().text(signatureMessage);
    }

    public String getChallenge(String address, String type, String walletType) {
        String cacheKey = CacheKey.Auth.CHALLENGE.getKey() + type + ":" + walletType + ":" + address;
        return redisTemplate.opsForValue().get(cacheKey);
    }

    @Override
    public void deleteChallenge(String address, String type, String walletType) {
        String cacheKey = CacheKey.Auth.CHALLENGE.getKey() + type + ":" + walletType + ":" + address;
        //redisTemplate.delete(cacheKey);
    }

    private String generateSolanaSignatureMessage(String address) {
        return "Sign this message to bind your Solana wallet: " + address +
               "\nNonce: " + RandomUtil.randomString(32) +
               "\nTimestamp: " + Instant.now().toString();
    }

    private String generateSolanaLoginMessage(String address) {
        return "Sign this message to login with your Solana wallet: " + address +
               "\nNonce: " + new SecureRandom().ints(32, 0, 16).mapToObj(Integer::toHexString).reduce("", String::concat) +
               "\nTimestamp: " + Instant.now().toString();
    }
}
