post:
  summary: 初始化视频观看会话
  description: 用户开始观看视频时创建新的会话，返回会话ID及相关奖励信息
  operationId: initVideoSession
  tags:
    - Video
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../components/VideoSessionInitRequest.yaml'
  responses:
    '200':
      description: 会话创建成功
      content:
        application/json:
          schema:
            $ref: '../components/VideoSessionInitResponse.yaml'
    '400':
      description: 请求参数错误
    '401':
      description: 用户未授权
  security:
    - jwtBearerAuth: []