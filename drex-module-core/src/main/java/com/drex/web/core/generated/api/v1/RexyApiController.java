package com.drex.web.core.generated.api.v1;

import com.drex.web.core.generated.model.v1.ClaimBuildResponse;
import com.drex.web.core.generated.model.v1.ClaimResponse;
import com.drex.web.core.generated.model.v1.RewardCollectResponse;
import com.drex.web.core.generated.model.v1.RexyClaimRequest;
import com.drex.web.core.generated.model.v1.RexyInfoResponse;
import com.drex.web.core.generated.model.v1.RexyRewardCollectRequest;
import com.drex.web.core.generated.model.v1.RexyRewardLastResponse;
import com.drex.web.core.generated.model.v1.RexysResponse;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.CookieValue;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.*;
import javax.validation.Valid;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
@Controller
@RequestMapping("${openapi.kweb.base-path:/v1}")
public class RexyApiController implements RexyApi {

    private final RexyApiDelegate delegate;

    public RexyApiController(@Autowired(required = false) RexyApiDelegate delegate) {
        this.delegate = Optional.ofNullable(delegate).orElse(new RexyApiDelegate() {});
    }

    @Override
    public RexyApiDelegate getDelegate() {
        return delegate;
    }

}
