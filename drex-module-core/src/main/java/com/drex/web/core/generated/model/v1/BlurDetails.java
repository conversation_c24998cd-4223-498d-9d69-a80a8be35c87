package com.drex.web.core.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.math.BigDecimal;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * BlurDetails
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class BlurDetails implements EventDataDetails {

  private Boolean tabActive;

  private Boolean windowFocused;

  /**
   * Default constructor
   * @deprecated Use {@link BlurDetails#BlurDetails(<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)}
   */
  @Deprecated
  public BlurDetails() {
    super();
  }

  /**
   * Constructor with only required parameters
   */
  public BlurDetails(Boolean tabActive, Boolean windowFocused) {
    this.tabActive = tabActive;
    this.windowFocused = windowFocused;
  }

  public BlurDetails tabActive(Boolean tabActive) {
    this.tabActive = tabActive;
    return this;
  }

  /**
   * 标签是否活跃
   * @return tabActive
  */
  @NotNull 
  @Schema(name = "tabActive", example = "false", description = "标签是否活跃", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("tabActive")
  public Boolean getTabActive() {
    return tabActive;
  }

  public void setTabActive(Boolean tabActive) {
    this.tabActive = tabActive;
  }

  public BlurDetails windowFocused(Boolean windowFocused) {
    this.windowFocused = windowFocused;
    return this;
  }

  /**
   * 窗口是否聚焦
   * @return windowFocused
  */
  @NotNull 
  @Schema(name = "windowFocused", example = "false", description = "窗口是否聚焦", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("windowFocused")
  public Boolean getWindowFocused() {
    return windowFocused;
  }

  public void setWindowFocused(Boolean windowFocused) {
    this.windowFocused = windowFocused;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    BlurDetails blurDetails = (BlurDetails) o;
    return Objects.equals(this.tabActive, blurDetails.tabActive) &&
        Objects.equals(this.windowFocused, blurDetails.windowFocused);
  }

  @Override
  public int hashCode() {
    return Objects.hash(tabActive, windowFocused);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class BlurDetails {\n");
    sb.append("    tabActive: ").append(toIndentedString(tabActive)).append("\n");
    sb.append("    windowFocused: ").append(toIndentedString(windowFocused)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

