/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (6.6.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
package com.drex.web.customer.generated.api.v1;

import com.drex.web.customer.generated.model.v1.AddDeveloperWaitListUploadResponse;
import com.drex.web.customer.generated.model.v1.AddWaitCreatorListRequest;
import com.drex.web.customer.generated.model.v1.AddWaitCreatorListResponse;
import com.drex.web.customer.generated.model.v1.AddWaitDeveloperListRequest;
import com.drex.web.customer.generated.model.v1.AddWaitDeveloperListResponse;
import com.drex.web.customer.generated.model.v1.AddWaitListRequest;
import com.drex.web.customer.generated.model.v1.AddWaitListResponse;
import com.drex.web.customer.generated.model.v1.BindSocialInfoRequest;
import com.drex.web.customer.generated.model.v1.BindSocialInfoResponse;
import com.drex.web.customer.generated.model.v1.InviteBindRequest;
import com.drex.web.customer.generated.model.v1.InviteBindResponse;
import com.drex.web.customer.generated.model.v1.InviteInfoResponse;
import com.drex.web.customer.generated.model.v1.PrivacyAuthResponse;
import com.drex.web.customer.generated.model.v1.QueryBySocialInfoResponse;
import com.drex.web.customer.generated.model.v1.ReservePrivacyAuthResponse;
import com.drex.web.customer.generated.model.v1.ReversePrivacyAuth;
import com.drex.web.customer.generated.model.v1.SocialUploadRequest;
import com.drex.web.customer.generated.model.v1.SocialUploadResponse;
import com.drex.web.customer.generated.model.v1.UnBindSocialInfoResponse;
import com.drex.web.customer.generated.model.v1.UnbindSocialInfoRequest;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
@Validated
@Tag(name = "Customer", description = "用户相关接口描述")
public interface CustomerApi {

    default CustomerApiDelegate getDelegate() {
        return new CustomerApiDelegate() {};
    }

    /**
     * POST /customers/addCreateWaitList : /customers/addWaitCreatorList
     * join the wait creator List
     *
     * @param addWaitCreatorListRequest  (optional)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "addWaitCreatorList",
        summary = "/customers/addWaitCreatorList",
        description = "join the wait creator List",
        tags = { "Customer" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = AddWaitCreatorListResponse.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/customers/addCreateWaitList",
        produces = { "application/json" },
        consumes = { "application/json" }
    )
    default ResponseEntity<AddWaitCreatorListResponse> addWaitCreatorList(
        @Parameter(name = "AddWaitCreatorListRequest", description = "") @Valid @RequestBody(required = false) AddWaitCreatorListRequest addWaitCreatorListRequest
    ) {
        return getDelegate().addWaitCreatorList(addWaitCreatorListRequest);
    }


    /**
     * POST /customers/addDeveloperWaitList : /customers/addWaitDeveloperList
     * join the wait developer List
     *
     * @param addWaitDeveloperListRequest  (optional)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "addWaitDeveloperList",
        summary = "/customers/addWaitDeveloperList",
        description = "join the wait developer List",
        tags = { "Customer" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = AddWaitDeveloperListResponse.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/customers/addDeveloperWaitList",
        produces = { "application/json" },
        consumes = { "application/json" }
    )
    default ResponseEntity<AddWaitDeveloperListResponse> addWaitDeveloperList(
        @Parameter(name = "AddWaitDeveloperListRequest", description = "") @Valid @RequestBody(required = false) AddWaitDeveloperListRequest addWaitDeveloperListRequest
    ) {
        return getDelegate().addWaitDeveloperList(addWaitDeveloperListRequest);
    }


    /**
     * POST /customers/addWaitList : /customers/addWaitList
     * join the waitList from officialWebsite
     *
     * @param addWaitListRequest  (optional)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "addWaitList",
        summary = "/customers/addWaitList",
        description = "join the waitList from officialWebsite",
        tags = { "Customer" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = AddWaitListResponse.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/customers/addWaitList",
        produces = { "application/json" },
        consumes = { "application/json" }
    )
    default ResponseEntity<AddWaitListResponse> addWaitList(
        @Parameter(name = "AddWaitListRequest", description = "") @Valid @RequestBody(required = false) AddWaitListRequest addWaitListRequest
    ) {
        return getDelegate().addWaitList(addWaitListRequest);
    }


    /**
     * POST /customers/addDeveloperWaitList/upload : 上传文件
     * 用于上传文件，返回文件名称和文件路径
     *
     * @param file 需要上传的文件 (optional)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "addWaitListUpload",
        summary = "上传文件",
        description = "用于上传文件，返回文件名称和文件路径",
        tags = { "Customer" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = AddDeveloperWaitListUploadResponse.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/customers/addDeveloperWaitList/upload",
        produces = { "application/json" },
        consumes = { "multipart/form-data" }
    )
    default ResponseEntity<AddDeveloperWaitListUploadResponse> addWaitListUpload(
        @Parameter(name = "file", description = "需要上传的文件") @RequestPart(value = "file", required = false) MultipartFile file
    ) {
        return getDelegate().addWaitListUpload(file);
    }


    /**
     * POST /customers/invites/bind : 邀请绑定
     * 邀请绑定
     *
     * @param inviteBindRequest  (optional)
     * @return OK (status code 200)
     */
    @Operation(
        operationId = "bindInvite",
        summary = "邀请绑定",
        description = "邀请绑定",
        tags = { "Customer" },
        responses = {
            @ApiResponse(responseCode = "200", description = "OK", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = InviteBindResponse.class))
            })
        },
        security = {
            @SecurityRequirement(name = "jwtBearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/customers/invites/bind",
        produces = { "application/json" },
        consumes = { "application/json" }
    )
    default ResponseEntity<InviteBindResponse> bindInvite(
        @Parameter(name = "InviteBindRequest", description = "") @Valid @RequestBody(required = false) InviteBindRequest inviteBindRequest
    ) {
        return getDelegate().bindInvite(inviteBindRequest);
    }


    /**
     * POST /s2s/customers/bindSocialInfo : 保存社媒信息
     * 保存社媒信息
     *
     * @param appKey app_key (required)
     * @param bindSocialInfoRequest  (optional)
     * @return OK (status code 200)
     */
    @Operation(
        operationId = "bindSocialInfo",
        summary = "保存社媒信息",
        description = "保存社媒信息",
        tags = { "Customer" },
        responses = {
            @ApiResponse(responseCode = "200", description = "OK", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = BindSocialInfoResponse.class))
            })
        },
        security = {
            @SecurityRequirement(name = "jwtBearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/s2s/customers/bindSocialInfo",
        produces = { "application/json" },
        consumes = { "application/json" }
    )
    default ResponseEntity<BindSocialInfoResponse> bindSocialInfo(
        @NotNull @Parameter(name = "app_key", description = "app_key", required = true, in = ParameterIn.HEADER) @RequestHeader(value = "app_key", required = true) String appKey,
        @Parameter(name = "BindSocialInfoRequest", description = "") @Valid @RequestBody(required = false) BindSocialInfoRequest bindSocialInfoRequest
    ) {
        return getDelegate().bindSocialInfo(appKey, bindSocialInfoRequest);
    }


    /**
     * GET /customers/invites/me : 查询当前用户邀请信息
     * 查询当前用户邀请信息
     *
     * @return OK (status code 200)
     */
    @Operation(
        operationId = "invite",
        summary = "查询当前用户邀请信息",
        description = "查询当前用户邀请信息",
        tags = { "Customer" },
        responses = {
            @ApiResponse(responseCode = "200", description = "OK", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = InviteInfoResponse.class))
            })
        },
        security = {
            @SecurityRequirement(name = "jwtBearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/customers/invites/me",
        produces = { "application/json" }
    )
    default ResponseEntity<InviteInfoResponse> invite(
        
    ) {
        return getDelegate().invite();
    }


    /**
     * GET /s2s/customers/invites/me : 查询当前用户邀请信息
     * 查询当前用户邀请信息
     *
     * @param cid  (required)
     * @param appKey  (required)
     * @return OK (status code 200)
     */
    @Operation(
        operationId = "inviteByCid",
        summary = "查询当前用户邀请信息",
        description = "查询当前用户邀请信息",
        tags = { "Customer" },
        responses = {
            @ApiResponse(responseCode = "200", description = "OK", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = InviteInfoResponse.class))
            })
        },
        security = {
            @SecurityRequirement(name = "jwtBearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/s2s/customers/invites/me",
        produces = { "application/json" }
    )
    default ResponseEntity<InviteInfoResponse> inviteByCid(
        @NotNull @Parameter(name = "cid", description = "", required = true, in = ParameterIn.QUERY) @Valid @RequestParam(value = "cid", required = true) String cid,
        @NotNull @Parameter(name = "app_key", description = "", required = true, in = ParameterIn.HEADER) @RequestHeader(value = "app_key", required = true) String appKey
    ) {
        return getDelegate().inviteByCid(cid, appKey);
    }


    /**
     * POST /customers/social/upload : /customers/platform/social/upload
     * Upload a social media post
     *
     * @param socialUploadRequest  (optional)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "platformSocialUpload",
        summary = "/customers/platform/social/upload",
        description = "Upload a social media post",
        tags = { "Customer" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = SocialUploadResponse.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/customers/social/upload",
        produces = { "application/json" },
        consumes = { "application/json" }
    )
    default ResponseEntity<SocialUploadResponse> platformSocialUpload(
        @Parameter(name = "SocialUploadRequest", description = "") @Valid @RequestBody(required = false) SocialUploadRequest socialUploadRequest
    ) {
        return getDelegate().platformSocialUpload(socialUploadRequest);
    }


    /**
     * GET /customers/privacy/auth : 查询用户是否同意授权隐私协议
     * 查询用户是否同意授权隐私协议
     *
     * @return 查询用户是否同意授权隐私协议 (status code 200)
     */
    @Operation(
        operationId = "privacyAuth",
        summary = "查询用户是否同意授权隐私协议",
        description = "查询用户是否同意授权隐私协议",
        tags = { "Customer" },
        responses = {
            @ApiResponse(responseCode = "200", description = "查询用户是否同意授权隐私协议", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = PrivacyAuthResponse.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/customers/privacy/auth",
        produces = { "application/json" }
    )
    default ResponseEntity<PrivacyAuthResponse> privacyAuth(
        
    ) {
        return getDelegate().privacyAuth();
    }


    /**
     * GET /s2s/customers/queryBySocialInfo : customers/queryBySocialInfo
     * third platform bindInfo
     *
     * @param appKey app_key (required)
     * @param platform platform (required)
     * @param passportId passportId (optional)
     * @param socialUserId socialUserId (optional)
     * @param socialHandleName socialHandleName (optional)
     * @param socialEmail socialEmail (optional)
     * @return OK (status code 200)
     */
    @Operation(
        operationId = "queryBySocialInfo",
        summary = "customers/queryBySocialInfo",
        description = "third platform bindInfo",
        tags = { "Customer" },
        responses = {
            @ApiResponse(responseCode = "200", description = "OK", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = QueryBySocialInfoResponse.class))
            })
        },
        security = {
            @SecurityRequirement(name = "jwtBearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/s2s/customers/queryBySocialInfo",
        produces = { "application/json" }
    )
    default ResponseEntity<QueryBySocialInfoResponse> queryBySocialInfo(
        @NotNull @Parameter(name = "app_key", description = "app_key", required = true, in = ParameterIn.HEADER) @RequestHeader(value = "app_key", required = true) String appKey,
        @NotNull @Parameter(name = "platform", description = "platform", required = true, in = ParameterIn.QUERY) @Valid @RequestParam(value = "platform", required = true) String platform,
        @Parameter(name = "passportId", description = "passportId", in = ParameterIn.QUERY) @Valid @RequestParam(value = "passportId", required = false) String passportId,
        @Parameter(name = "socialUserId", description = "socialUserId", in = ParameterIn.QUERY) @Valid @RequestParam(value = "socialUserId", required = false) String socialUserId,
        @Parameter(name = "socialHandleName", description = "socialHandleName", in = ParameterIn.QUERY) @Valid @RequestParam(value = "socialHandleName", required = false) String socialHandleName,
        @Parameter(name = "socialEmail", description = "socialEmail", in = ParameterIn.QUERY) @Valid @RequestParam(value = "socialEmail", required = false) String socialEmail
    ) {
        return getDelegate().queryBySocialInfo(appKey, platform, passportId, socialUserId, socialHandleName, socialEmail);
    }


    /**
     * POST /customers/privacy/reverseAuth : 更新用户授权隐私状态
     * 更新用户授权隐私状态
     *
     * @param reversePrivacyAuth  (optional)
     * @return 更新用户授权隐私状态的结果 (status code 200)
     */
    @Operation(
        operationId = "reversePrivacyAuth",
        summary = "更新用户授权隐私状态",
        description = "更新用户授权隐私状态",
        tags = { "Customer" },
        responses = {
            @ApiResponse(responseCode = "200", description = "更新用户授权隐私状态的结果", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = ReservePrivacyAuthResponse.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/customers/privacy/reverseAuth",
        produces = { "application/json" },
        consumes = { "application/json" }
    )
    default ResponseEntity<ReservePrivacyAuthResponse> reversePrivacyAuth(
        @Parameter(name = "ReversePrivacyAuth", description = "") @Valid @RequestBody(required = false) ReversePrivacyAuth reversePrivacyAuth
    ) {
        return getDelegate().reversePrivacyAuth(reversePrivacyAuth);
    }


    /**
     * POST /s2s/customers/unbindSocialInfo : 删除社媒信息
     * 删除社媒信息
     *
     * @param appKey app_key (required)
     * @param unbindSocialInfoRequest  (optional)
     * @return OK (status code 200)
     */
    @Operation(
        operationId = "unbindSocialInfo",
        summary = "删除社媒信息",
        description = "删除社媒信息",
        tags = { "Customer" },
        responses = {
            @ApiResponse(responseCode = "200", description = "OK", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = UnBindSocialInfoResponse.class))
            })
        },
        security = {
            @SecurityRequirement(name = "jwtBearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/s2s/customers/unbindSocialInfo",
        produces = { "application/json" },
        consumes = { "application/json" }
    )
    default ResponseEntity<UnBindSocialInfoResponse> unbindSocialInfo(
        @NotNull @Parameter(name = "app_key", description = "app_key", required = true, in = ParameterIn.HEADER) @RequestHeader(value = "app_key", required = true) String appKey,
        @Parameter(name = "UnbindSocialInfoRequest", description = "") @Valid @RequestBody(required = false) UnbindSocialInfoRequest unbindSocialInfoRequest
    ) {
        return getDelegate().unbindSocialInfo(appKey, unbindSocialInfoRequest);
    }

}
