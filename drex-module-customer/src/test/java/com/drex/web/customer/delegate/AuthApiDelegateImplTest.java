package com.drex.web.customer.delegate;

import com.drex.customer.api.response.ThirdBindingsDTO;
import com.kikitrade.gateway.client.model.WebResultAuthUrlsVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AuthApiDelegateImpl 测试类
 * 主要测试绑定列表处理逻辑
 * 
 * <AUTHOR>
 * @date 2025/7/23
 */
@ExtendWith(MockitoExtension.class)
public class AuthApiDelegateImplTest {

    @InjectMocks
    private AuthApiDelegateImpl authApiDelegate;

    private List<ThirdBindingsDTO> bindingsDTOList;
    private WebResultAuthUrlsVO tasksAuthUrls;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        bindingsDTOList = new ArrayList<>();
        
        // 创建未绑定的平台
        ThirdBindingsDTO binding1 = new ThirdBindingsDTO();
        binding1.setPlatform("X");
        binding1.setStatus("0"); // 未绑定
        binding1.setConnectUrl(""); // 初始为空
        bindingsDTOList.add(binding1);
        
        ThirdBindingsDTO binding2 = new ThirdBindingsDTO();
        binding2.setPlatform("YouTube");
        binding2.setStatus("0"); // 未绑定
        binding2.setConnectUrl(""); // 初始为空
        bindingsDTOList.add(binding2);
        
        // 创建已绑定的平台
        ThirdBindingsDTO binding3 = new ThirdBindingsDTO();
        binding3.setPlatform("Discord");
        binding3.setStatus("1"); // 已绑定
        binding3.setConnectUrl("https://discord.com/oauth2/authorize");
        bindingsDTOList.add(binding3);
        
        // 准备授权URL数据
        tasksAuthUrls = new WebResultAuthUrlsVO();
        List<Object> authUrlList = new ArrayList<>();
        
        // X平台授权URL
        Map<String, Object> xAuthUrl = new HashMap<>();
        xAuthUrl.put("platform", "X");
        xAuthUrl.put("authUrl", "https://api.twitter.com/oauth/authorize");
        authUrlList.add(xAuthUrl);
        
        // YouTube平台授权URL
        Map<String, Object> youtubeAuthUrl = new HashMap<>();
        youtubeAuthUrl.put("platform", "youtube"); // 测试不区分大小写
        youtubeAuthUrl.put("authUrl", "https://accounts.google.com/oauth/authorize");
        authUrlList.add(youtubeAuthUrl);
        
        // 其他平台授权URL
        Map<String, Object> telegramAuthUrl = new HashMap<>();
        telegramAuthUrl.put("platform", "Telegram");
        telegramAuthUrl.put("authUrl", "https://oauth.telegram.org/auth");
        authUrlList.add(telegramAuthUrl);
        
//        tasksAuthUrls.setObj(authUrlList);
    }

    @Test
    void testProcessBindingsWithAuthUrls() throws Exception {
        // 使用反射调用私有方法
        Method method = AuthApiDelegateImpl.class.getDeclaredMethod(
            "processBindingsWithAuthUrls", 
            List.class, 
            WebResultAuthUrlsVO.class
        );
        method.setAccessible(true);
        
        // 执行方法
        method.invoke(authApiDelegate, bindingsDTOList, tasksAuthUrls);
        
        // 验证结果
        // X平台应该被设置授权URL
        ThirdBindingsDTO xBinding = bindingsDTOList.stream()
            .filter(b -> "X".equals(b.getPlatform()))
            .findFirst()
            .orElse(null);
        assertNotNull(xBinding);
        assertEquals("https://api.twitter.com/oauth/authorize", xBinding.getConnectUrl());
        
        // YouTube平台应该被设置授权URL（测试不区分大小写）
        ThirdBindingsDTO youtubeBinding = bindingsDTOList.stream()
            .filter(b -> "YouTube".equals(b.getPlatform()))
            .findFirst()
            .orElse(null);
        assertNotNull(youtubeBinding);
        assertEquals("https://accounts.google.com/oauth/authorize", youtubeBinding.getConnectUrl());
        
        // Discord平台已绑定，不应该被修改
        ThirdBindingsDTO discordBinding = bindingsDTOList.stream()
            .filter(b -> "Discord".equals(b.getPlatform()))
            .findFirst()
            .orElse(null);
        assertNotNull(discordBinding);
        assertEquals("https://discord.com/oauth2/authorize", discordBinding.getConnectUrl());
    }

    @Test
    void testFindAuthUrlForPlatform() throws Exception {
        // 使用反射调用私有方法
        Method method = AuthApiDelegateImpl.class.getDeclaredMethod(
            "findAuthUrlForPlatform", 
            String.class, 
            List.class
        );
        method.setAccessible(true);
        
        // 测试正常匹配
        String result1 = (String) method.invoke(authApiDelegate, "X", tasksAuthUrls.getObj());
        assertEquals("https://api.twitter.com/oauth/authorize", result1);
        
        // 测试不区分大小写匹配
        String result2 = (String) method.invoke(authApiDelegate, "YOUTUBE", tasksAuthUrls.getObj());
        assertEquals("https://accounts.google.com/oauth/authorize", result2);
        
        // 测试未找到的平台
        String result3 = (String) method.invoke(authApiDelegate, "Facebook", tasksAuthUrls.getObj());
        assertNull(result3);
        
        // 测试空参数
        String result4 = (String) method.invoke(authApiDelegate, null, tasksAuthUrls.getObj());
        assertNull(result4);
        
        String result5 = (String) method.invoke(authApiDelegate, "X", null);
        assertNull(result5);
    }

    @Test
    void testProcessBindingsWithNullData() throws Exception {
        // 使用反射调用私有方法
        Method method = AuthApiDelegateImpl.class.getDeclaredMethod(
            "processBindingsWithAuthUrls", 
            List.class, 
            WebResultAuthUrlsVO.class
        );
        method.setAccessible(true);
        
        // 测试空绑定列表
        assertDoesNotThrow(() -> {
            method.invoke(authApiDelegate, null, tasksAuthUrls);
        });
        
        // 测试空授权URL列表
        assertDoesNotThrow(() -> {
            method.invoke(authApiDelegate, bindingsDTOList, null);
        });
        
        // 测试空的授权URL对象
        WebResultAuthUrlsVO emptyAuthUrls = new WebResultAuthUrlsVO();
        emptyAuthUrls.setObj(null);
        assertDoesNotThrow(() -> {
            method.invoke(authApiDelegate, bindingsDTOList, emptyAuthUrls);
        });
    }
}
