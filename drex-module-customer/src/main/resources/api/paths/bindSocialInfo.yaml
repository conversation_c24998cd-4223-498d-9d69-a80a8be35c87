post:
  tags:
    - Customer
  summary: 保存社媒信息
  description: 保存社媒信息
  operationId: bindSocialInfo
  security:
    - jwtBearerAuth: [ ]
  parameters:
    - name: app_key
      in: header
      description: app_key
      required: true
      example: trex
      schema:
        type: string
  requestBody:
    content:
      application/json:
        schema:
          $ref: '../components/BindSocialInfoRequest.yaml'
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            $ref: '../components/BindSocialInfoResponse.yaml'
