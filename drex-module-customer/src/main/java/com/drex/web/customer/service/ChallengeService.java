package com.drex.web.customer.service;

import com.drex.web.customer.generated.model.v1.Challenge;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;

@Service
public interface ChallengeService {

    Challenge generateChallenge(@NotNull String address, String type, String walletType);

    String getChallenge(@NotNull String address,String type, String walletType);

    void deleteChallenge(@NotNull String address,String type, String walletType);
}
