package com.drex.web.common;

import lombok.Getter;

@Getter
public enum ErrorCode {

    SIGNATURE_INVALID("00001", "signature verify failed"),
    REFRESH_TOKEN_INVALID("00002", "token refresh failed"),

    REPEAT_OPERATION("00020", "repeat operation"),
    PARAMS_ILLEGAL("00021", "params illegal"),
    HANDLE_NAME_EXISTS("00022", "handle name exists"),
    SENSITIVE_WORDS("00023", "sensitive words"),

    UNKNOWN_ERROR("9999", "unknown error"),
    ;

    private String code;
    private String message;

    ErrorCode(String code, String message) {
        this.code = code;
        this.message = message;
    }
}
