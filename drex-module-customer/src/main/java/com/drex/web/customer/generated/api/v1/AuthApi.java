/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (6.6.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
package com.drex.web.customer.generated.api.v1;

import com.drex.web.customer.generated.model.v1.AccessTokenResponse;
import com.drex.web.customer.generated.model.v1.BindWalletRequest;
import com.drex.web.customer.generated.model.v1.BindWalletResponse;
import com.drex.web.customer.generated.model.v1.ChallengeResponse;
import com.drex.web.customer.generated.model.v1.LoginRequest;
import com.drex.web.customer.generated.model.v1.LogoutResponse;
import com.drex.web.customer.generated.model.v1.ThirdAuthorizeRequest;
import com.drex.web.customer.generated.model.v1.ThirdAuthorizeResponse;
import com.drex.web.customer.generated.model.v1.ThirdBindingsResponse;
import com.drex.web.customer.generated.model.v1.WalletTypeEnum;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
@Validated
@Tag(name = "Auth", description = "用户授权相关接口描述")
public interface AuthApi {

    default AuthApiDelegate getDelegate() {
        return new AuthApiDelegate() {};
    }

    /**
     * POST /auth/login : 登录
     * 登录
     *
     * @param loginRequest  (required)
     * @param osDeviceId 设备id (optional)
     * @return OK (status code 200)
     */
    @Operation(
        operationId = "authLogin",
        summary = "登录",
        description = "登录",
        tags = { "Auth" },
        responses = {
            @ApiResponse(responseCode = "200", description = "OK", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = AccessTokenResponse.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/auth/login",
        produces = { "application/json" },
        consumes = { "application/json" }
    )
    default ResponseEntity<AccessTokenResponse> authLogin(
        @Parameter(name = "LoginRequest", description = "", required = true) @Valid @RequestBody LoginRequest loginRequest,
        @Parameter(name = "os_device_id", description = "设备id", in = ParameterIn.HEADER) @RequestHeader(value = "os_device_id", required = false) String osDeviceId
    ) {
        return getDelegate().authLogin(loginRequest, osDeviceId);
    }


    /**
     * POST /passport/wallet/bind : 绑定钱包
     * 绑定钱包
     *
     * @param bindWalletRequest  (optional)
     * @return OK (status code 200)
     */
    @Operation(
        operationId = "bindWallet",
        summary = "绑定钱包",
        description = "绑定钱包",
        tags = { "Auth" },
        responses = {
            @ApiResponse(responseCode = "200", description = "OK", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = BindWalletResponse.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/passport/wallet/bind",
        produces = { "application/json" },
        consumes = { "application/json" }
    )
    default ResponseEntity<BindWalletResponse> bindWallet(
        @Parameter(name = "BindWalletRequest", description = "") @Valid @RequestBody(required = false) BindWalletRequest bindWalletRequest
    ) {
        return getDelegate().bindWallet(bindWalletRequest);
    }


    /**
     * GET /auth/challenge : 获取一个密钥
     * 获取一个密钥
     *
     * @param address 地址 (required)
     * @param type 登录送wallet or social，绑定钱包送bind_wallet (optional, default to social)
     * @param walletType 钱包类型, 默认送EVM (optional)
     * @return OK (status code 200)
     */
    @Operation(
        operationId = "challenge",
        summary = "获取一个密钥",
        description = "获取一个密钥",
        tags = { "Auth" },
        responses = {
            @ApiResponse(responseCode = "200", description = "OK", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = ChallengeResponse.class))
            })
        },
        security = {
            @SecurityRequirement(name = "jwtBearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/auth/challenge",
        produces = { "application/json" }
    )
    default ResponseEntity<ChallengeResponse> challenge(
        @NotNull @Parameter(name = "address", description = "地址", required = true, in = ParameterIn.QUERY) @Valid @RequestParam(value = "address", required = true) String address,
        @Parameter(name = "type", description = "登录送wallet or social，绑定钱包送bind_wallet", in = ParameterIn.QUERY) @Valid @RequestParam(value = "type", required = false, defaultValue = "social") String type,
        @Parameter(name = "walletType", description = "钱包类型, 默认送EVM", in = ParameterIn.QUERY) @Valid @RequestParam(value = "walletType", required = false) WalletTypeEnum walletType
    ) {
        return getDelegate().challenge(address, type, walletType);
    }


    /**
     * POST /auth/logout : 登出
     * 登出
     *
     * @return OK (status code 200)
     */
    @Operation(
        operationId = "logout",
        summary = "登出",
        description = "登出",
        tags = { "Auth" },
        responses = {
            @ApiResponse(responseCode = "200", description = "OK", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = LogoutResponse.class))
            })
        },
        security = {
            @SecurityRequirement(name = "jwtBearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/auth/logout",
        produces = { "application/json" }
    )
    default ResponseEntity<LogoutResponse> logout(
        
    ) {
        return getDelegate().logout();
    }


    /**
     * POST /auth/qr/login : 登录
     * 登录
     *
     * @param loginRequest  (required)
     * @param osDeviceId 设备id (optional)
     * @return OK (status code 200)
     */
    @Operation(
        operationId = "qrAuthLogin",
        summary = "登录",
        description = "登录",
        tags = { "Auth" },
        responses = {
            @ApiResponse(responseCode = "200", description = "OK", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = AccessTokenResponse.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/auth/qr/login",
        produces = { "application/json" },
        consumes = { "application/json" }
    )
    default ResponseEntity<AccessTokenResponse> qrAuthLogin(
        @Parameter(name = "LoginRequest", description = "", required = true) @Valid @RequestBody LoginRequest loginRequest,
        @Parameter(name = "os_device_id", description = "设备id", in = ParameterIn.HEADER) @RequestHeader(value = "os_device_id", required = false) String osDeviceId
    ) {
        return getDelegate().qrAuthLogin(loginRequest, osDeviceId);
    }


    /**
     * POST /auth/refresh : 刷新accessToken
     * 刷新
     *
     * @param accessToken accessToken (optional)
     * @param refreshToken refreshToken (optional)
     * @return OK (status code 200)
     */
    @Operation(
        operationId = "refreshAccessToken",
        summary = "刷新accessToken",
        description = "刷新",
        tags = { "Auth" },
        responses = {
            @ApiResponse(responseCode = "200", description = "OK", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = AccessTokenResponse.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/auth/refresh",
        produces = { "application/json" }
    )
    default ResponseEntity<AccessTokenResponse> refreshAccessToken(
        @Parameter(name = "accessToken", description = "accessToken", in = ParameterIn.QUERY) @Valid @RequestParam(value = "accessToken", required = false) String accessToken,
        @Parameter(name = "refreshToken", description = "refreshToken", in = ParameterIn.QUERY) @Valid @RequestParam(value = "refreshToken", required = false) String refreshToken
    ) {
        return getDelegate().refreshAccessToken(accessToken, refreshToken);
    }


    /**
     * POST /auth/third/authorize : /customer/auth/third/authorize
     * third platform authorize
     *
     * @param thirdAuthorizeRequest  (optional)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "thirdAuthorize",
        summary = "/customer/auth/third/authorize",
        description = "third platform authorize",
        tags = { "Auth" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = ThirdAuthorizeResponse.class))
            })
        },
        security = {
            @SecurityRequirement(name = "jwtBearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/auth/third/authorize",
        produces = { "application/json" },
        consumes = { "application/json" }
    )
    default ResponseEntity<ThirdAuthorizeResponse> thirdAuthorize(
        @Parameter(name = "ThirdAuthorizeRequest", description = "") @Valid @RequestBody(required = false) ThirdAuthorizeRequest thirdAuthorizeRequest
    ) {
        return getDelegate().thirdAuthorize(thirdAuthorizeRequest);
    }


    /**
     * GET /auth/third/bindings : /customer/auth/third/bindings
     * third platform bind lists
     *
     * @param authorization authorization (optional)
     * @return OK (status code 200)
     */
    @Operation(
        operationId = "thirdBindings",
        summary = "/customer/auth/third/bindings",
        description = "third platform bind lists",
        tags = { "Auth" },
        responses = {
            @ApiResponse(responseCode = "200", description = "OK", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = ThirdBindingsResponse.class))
            })
        },
        security = {
            @SecurityRequirement(name = "jwtBearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/auth/third/bindings",
        produces = { "application/json" }
    )
    default ResponseEntity<ThirdBindingsResponse> thirdBindings(
        @Parameter(name = "authorization", description = "authorization", in = ParameterIn.HEADER) @RequestHeader(value = "authorization", required = false) String authorization
    ) {
        return getDelegate().thirdBindings(authorization);
    }

}
