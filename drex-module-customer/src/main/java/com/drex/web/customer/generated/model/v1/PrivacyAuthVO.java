package com.drex.web.customer.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * PrivacyAuthVO
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class PrivacyAuthVO {

  private Boolean privacyAuthX = false;

  private Boolean privacyAuthYoutube = false;

  private Boolean popAuthPrompt = false;

  public PrivacyAuthVO privacyAuthX(Boolean privacyAuthX) {
    this.privacyAuthX = privacyAuthX;
    return this;
  }

  /**
   * X平台隐私授权状态
   * @return privacyAuthX
  */
  
  @Schema(name = "privacyAuthX", description = "X平台隐私授权状态", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("privacyAuthX")
  public Boolean getPrivacyAuthX() {
    return privacyAuthX;
  }

  public void setPrivacyAuthX(Boolean privacyAuthX) {
    this.privacyAuthX = privacyAuthX;
  }

  public PrivacyAuthVO privacyAuthYoutube(Boolean privacyAuthYoutube) {
    this.privacyAuthYoutube = privacyAuthYoutube;
    return this;
  }

  /**
   * youtube平台的隐私授权状态
   * @return privacyAuthYoutube
  */
  
  @Schema(name = "privacyAuthYoutube", description = "youtube平台的隐私授权状态", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("privacyAuthYoutube")
  public Boolean getPrivacyAuthYoutube() {
    return privacyAuthYoutube;
  }

  public void setPrivacyAuthYoutube(Boolean privacyAuthYoutube) {
    this.privacyAuthYoutube = privacyAuthYoutube;
  }

  public PrivacyAuthVO popAuthPrompt(Boolean popAuthPrompt) {
    this.popAuthPrompt = popAuthPrompt;
    return this;
  }

  /**
   * 是否弹出授权提示
   * @return popAuthPrompt
  */
  
  @Schema(name = "popAuthPrompt", description = "是否弹出授权提示", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("popAuthPrompt")
  public Boolean getPopAuthPrompt() {
    return popAuthPrompt;
  }

  public void setPopAuthPrompt(Boolean popAuthPrompt) {
    this.popAuthPrompt = popAuthPrompt;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PrivacyAuthVO privacyAuthVO = (PrivacyAuthVO) o;
    return Objects.equals(this.privacyAuthX, privacyAuthVO.privacyAuthX) &&
        Objects.equals(this.privacyAuthYoutube, privacyAuthVO.privacyAuthYoutube) &&
        Objects.equals(this.popAuthPrompt, privacyAuthVO.popAuthPrompt);
  }

  @Override
  public int hashCode() {
    return Objects.hash(privacyAuthX, privacyAuthYoutube, popAuthPrompt);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PrivacyAuthVO {\n");
    sb.append("    privacyAuthX: ").append(toIndentedString(privacyAuthX)).append("\n");
    sb.append("    privacyAuthYoutube: ").append(toIndentedString(privacyAuthYoutube)).append("\n");
    sb.append("    popAuthPrompt: ").append(toIndentedString(popAuthPrompt)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

