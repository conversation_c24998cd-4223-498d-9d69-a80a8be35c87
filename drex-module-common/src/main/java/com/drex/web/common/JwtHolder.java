package com.drex.web.common;

import com.drex.customer.api.response.CustomerDTO;

public class JwtHolder {

    private static final ThreadLocal<String> holder = new ThreadLocal<>();

    public static void bind(String jwt) {
        if (null == jwt) {
            return;
        }
        holder.set(jwt);
    }

    public static String jwt() {
        return holder.get();
    }

    public static void clear() {
        holder.remove();
    }
}
