package com.drex.web.core.delegate;

import com.drex.core.api.RemoteTransactionService;
import com.drex.core.api.response.WalletOperationDTO;
import com.drex.web.common.context.RequestContextHolder;
import com.drex.web.core.generated.api.v1.TxApiDelegate;
import com.drex.web.core.generated.model.v1.FaucetDripResponse;
import com.drex.web.core.generated.model.v1.FaucetDripResponseAllOfObj;
import com.kikitrade.framework.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;

@Slf4j
@Service
public class TxApiDelegateImpl implements TxApiDelegate {

    @DubboReference
    private RemoteTransactionService remoteTransactionService;

    @Override
    public ResponseEntity<FaucetDripResponse> faucetDrip(String address) {
        log.info("faucetDrip request:{}", address);
        Response<WalletOperationDTO> walletOperationDTOResponse = remoteTransactionService.faucetDrip(address, RequestContextHolder.getClientIp());
        if (walletOperationDTOResponse.isSuccess()) {
            FaucetDripResponse faucetDripResponse = new FaucetDripResponse();
            FaucetDripResponseAllOfObj obj = new FaucetDripResponseAllOfObj();
            obj.setTargetAddress(address);
            obj.setCallData(walletOperationDTOResponse.getData().getCallData());
            obj.setTxHash(walletOperationDTOResponse.getData().getTxHash());
            obj.setAmount(convertWeiToEth(walletOperationDTOResponse.getData().getValue()).toPlainString());
            faucetDripResponse.setObj(obj);
            faucetDripResponse.success();
            return ResponseEntity.ok(faucetDripResponse);
        }
        FaucetDripResponse faucetDripResponse = new FaucetDripResponse();
        faucetDripResponse.fail(walletOperationDTOResponse.getCode(), walletOperationDTOResponse.getMessage());
        return ResponseEntity.badRequest().body(faucetDripResponse);
    }

    private BigDecimal convertWeiToEth(BigInteger wei) {
        return new BigDecimal(wei).divide(new BigDecimal(BigInteger.TEN.pow(18)), 3, RoundingMode.DOWN);
    }
}
