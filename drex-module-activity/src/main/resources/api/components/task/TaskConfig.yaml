type: object
properties:
  taskId:
    type: string
    description: 任务id，唯一标识
  title:
    type: string
    description: 任务标题
  desc:
    type: string
    description: 任务的描述信息
  url:
    type: string
    description: 任务详情链接
  connectUrl:
    type: string
    description: 授权地址
  status:
    type: integer
    format: int32
    description: 完成状态 0-未完成 1-已完成
  btn:
    type: integer
    description: 0:不显示 1:go 2:verify
  link:
    type: object
    additionalProperties: true
    description: map结构，key表示文案，例如connect now，value表示链接地址
  code:
    type: string
    description: 任务code
  reward:
    type: number
    description: 页面展示奖励金额
  icon:
    type: string
    description: 任务图标