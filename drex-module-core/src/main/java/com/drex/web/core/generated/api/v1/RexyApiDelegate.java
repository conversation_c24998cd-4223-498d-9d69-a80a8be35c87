package com.drex.web.core.generated.api.v1;

import com.drex.web.core.generated.model.v1.ClaimBuildResponse;
import com.drex.web.core.generated.model.v1.ClaimResponse;
import com.drex.web.core.generated.model.v1.RewardCollectResponse;
import com.drex.web.core.generated.model.v1.RexyClaimRequest;
import com.drex.web.core.generated.model.v1.RexyInfoResponse;
import com.drex.web.core.generated.model.v1.RexyRewardCollectRequest;
import com.drex.web.core.generated.model.v1.RexyRewardLastResponse;
import com.drex.web.core.generated.model.v1.RexysResponse;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Generated;

/**
 * A delegate to be called by the {@link RexyApiController}}.
 * Implement this interface with a {@link org.springframework.stereotype.Service} annotated class.
 */
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public interface RexyApiDelegate {

    default Optional<NativeWebRequest> getRequest() {
        return Optional.empty();
    }

    /**
     * GET /rexys/me : 查询当前用户宠物信息
     * 查询当前用户宠物信息
     *
     * @return OK (status code 200)
     * @see RexyApi#rexy
     */
    default ResponseEntity<RexyInfoResponse> rexy() {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /rexys/claim : 领取积分奖励
     * 领取积分奖励
     *
     * @param rexyClaimRequest  (required)
     * @return OK (status code 200)
     * @see RexyApi#rexyClaim
     */
    default ResponseEntity<ClaimResponse> rexyClaim(RexyClaimRequest rexyClaimRequest) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /rexys/claim/build/{basketType} : 生成交易业务id
     * 生成交易业务id
     *
     * @param basketType 交易业务场景(normal, invite) (required)
     * @param address 钱包地址 (optional)
     * @return OK (status code 200)
     * @see RexyApi#rexyClaimBuild
     */
    default ResponseEntity<ClaimBuildResponse> rexyClaimBuild(String basketType,
        String address) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /rexys/reward/collect : 恐龙收集奖励
     * 恐龙收集奖励
     *
     * @param rexyRewardCollectRequest  (optional)
     * @return OK (status code 200)
     * @see RexyApi#rexyRewardCollect
     */
    default ResponseEntity<RewardCollectResponse> rexyRewardCollect(RexyRewardCollectRequest rexyRewardCollectRequest) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /rexys/reward/last : 查询最新恐龙蛋
     * 查询最新恐龙蛋
     *
     * @param platform 平台，可选值：X 或 YouTube (required)
     * @param contentId 内容id (optional)
     * @return OK (status code 200)
     * @see RexyApi#rexyRewardLast
     */
    default ResponseEntity<RexyRewardLastResponse> rexyRewardLast(String platform,
        String contentId) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /rexys/avatar : 查询恐龙列表
     * 查询恐龙列表
     *
     * @return OK (status code 200)
     * @see RexyApi#rexys
     */
    default ResponseEntity<RexysResponse> rexys() {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

}
