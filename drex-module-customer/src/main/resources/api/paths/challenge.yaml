get:
  tags:
    - Auth
  summary: 获取一个密钥
  description: 获取一个密钥
  operationId: challenge
  security:
    - jwtBearerAuth: [ ]
  parameters:
    - name: address
      in: query
      description: 地址
      required: true
      schema:
        type: string
    - name: type
      in: query
      description: 登录送wallet or social，绑定钱包送bind_wallet
      schema:
        type: string
        default: social
    - name: walletType
      in: query
      description: 钱包类型, 默认送EVM
      schema:
        $ref: '../components/WalletTypeEnum.yaml'
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            $ref: '../components/ChallengeResponse.yaml'
