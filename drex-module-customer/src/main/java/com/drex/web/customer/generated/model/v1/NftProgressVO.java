package com.drex.web.customer.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * nft conditions and progress
 */

@Schema(name = "NftProgressVO", description = "nft conditions and progress")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class NftProgressVO {

  private String condition;

  private String name;

  private String type;

  private String value;

  public NftProgressVO condition(String condition) {
    this.condition = condition;
    return this;
  }

  /**
   * condition number
   * @return condition
  */
  
  @Schema(name = "condition", description = "condition number", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("condition")
  public String getCondition() {
    return condition;
  }

  public void setCondition(String condition) {
    this.condition = condition;
  }

  public NftProgressVO name(String name) {
    this.name = name;
    return this;
  }

  /**
   * condition name
   * @return name
  */
  
  @Schema(name = "name", description = "condition name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("name")
  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public NftProgressVO type(String type) {
    this.type = type;
    return this;
  }

  /**
   * condition type
   * @return type
  */
  
  @Schema(name = "type", description = "condition type", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("type")
  public String getType() {
    return type;
  }

  public void setType(String type) {
    this.type = type;
  }

  public NftProgressVO value(String value) {
    this.value = value;
    return this;
  }

  /**
   * progress value
   * @return value
  */
  
  @Schema(name = "value", description = "progress value", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("value")
  public String getValue() {
    return value;
  }

  public void setValue(String value) {
    this.value = value;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    NftProgressVO nftProgressVO = (NftProgressVO) o;
    return Objects.equals(this.condition, nftProgressVO.condition) &&
        Objects.equals(this.name, nftProgressVO.name) &&
        Objects.equals(this.type, nftProgressVO.type) &&
        Objects.equals(this.value, nftProgressVO.value);
  }

  @Override
  public int hashCode() {
    return Objects.hash(condition, name, type, value);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class NftProgressVO {\n");
    sb.append("    condition: ").append(toIndentedString(condition)).append("\n");
    sb.append("    name: ").append(toIndentedString(name)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    value: ").append(toIndentedString(value)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

