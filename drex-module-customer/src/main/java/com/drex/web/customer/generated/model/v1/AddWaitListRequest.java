package com.drex.web.customer.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * AddWaitListRequest
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class AddWaitListRequest {

  private String name;

  private String project;

  private String email;

  private String evmAddress;

  /**
   * Default constructor
   * @deprecated Use {@link AddWaitListRequest#AddWaitListRequest(String, String)}
   */
  @Deprecated
  public AddWaitListRequest() {
    super();
  }

  /**
   * Constructor with only required parameters
   */
  public AddWaitListRequest(String name, String email) {
    this.name = name;
    this.email = email;
  }

  public AddWaitListRequest name(String name) {
    this.name = name;
    return this;
  }

  /**
   * 用户名
   * @return name
  */
  @NotNull 
  @Schema(name = "name", description = "用户名", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("name")
  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public AddWaitListRequest project(String project) {
    this.project = project;
    return this;
  }

  /**
   * 项目名
   * @return project
  */
  
  @Schema(name = "project", description = "项目名", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("project")
  public String getProject() {
    return project;
  }

  public void setProject(String project) {
    this.project = project;
  }

  public AddWaitListRequest email(String email) {
    this.email = email;
    return this;
  }

  /**
   * 邮箱
   * @return email
  */
  @NotNull 
  @Schema(name = "email", description = "邮箱", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("email")
  public String getEmail() {
    return email;
  }

  public void setEmail(String email) {
    this.email = email;
  }

  public AddWaitListRequest evmAddress(String evmAddress) {
    this.evmAddress = evmAddress;
    return this;
  }

  /**
   * 钱包地址
   * @return evmAddress
  */
  
  @Schema(name = "evmAddress", description = "钱包地址", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("evmAddress")
  public String getEvmAddress() {
    return evmAddress;
  }

  public void setEvmAddress(String evmAddress) {
    this.evmAddress = evmAddress;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    AddWaitListRequest addWaitListRequest = (AddWaitListRequest) o;
    return Objects.equals(this.name, addWaitListRequest.name) &&
        Objects.equals(this.project, addWaitListRequest.project) &&
        Objects.equals(this.email, addWaitListRequest.email) &&
        Objects.equals(this.evmAddress, addWaitListRequest.evmAddress);
  }

  @Override
  public int hashCode() {
    return Objects.hash(name, project, email, evmAddress);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class AddWaitListRequest {\n");
    sb.append("    name: ").append(toIndentedString(name)).append("\n");
    sb.append("    project: ").append(toIndentedString(project)).append("\n");
    sb.append("    email: ").append(toIndentedString(email)).append("\n");
    sb.append("    evmAddress: ").append(toIndentedString(evmAddress)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

