package com.drex.web.core.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * ExtraRewardInfo
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class ExtraRewardInfo {

  private String rexyRewardId;

  private String rexyRewardPoint;

  public ExtraRewardInfo rexyRewardId(String rexyRewardId) {
    this.rexyRewardId = rexyRewardId;
    return this;
  }

  /**
   * Get rexyRewardId
   * @return rexyRewardId
  */
  
  @Schema(name = "rexyRewardId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("rexyRewardId")
  public String getRexyRewardId() {
    return rexyRewardId;
  }

  public void setRexyRewardId(String rexyRewardId) {
    this.rexyRewardId = rexyRewardId;
  }

  public ExtraRewardInfo rexyRewardPoint(String rexyRewardPoint) {
    this.rexyRewardPoint = rexyRewardPoint;
    return this;
  }

  /**
   * Get rexyRewardPoint
   * @return rexyRewardPoint
  */
  
  @Schema(name = "rexyRewardPoint", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("rexyRewardPoint")
  public String getRexyRewardPoint() {
    return rexyRewardPoint;
  }

  public void setRexyRewardPoint(String rexyRewardPoint) {
    this.rexyRewardPoint = rexyRewardPoint;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ExtraRewardInfo extraRewardInfo = (ExtraRewardInfo) o;
    return Objects.equals(this.rexyRewardId, extraRewardInfo.rexyRewardId) &&
        Objects.equals(this.rexyRewardPoint, extraRewardInfo.rexyRewardPoint);
  }

  @Override
  public int hashCode() {
    return Objects.hash(rexyRewardId, rexyRewardPoint);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ExtraRewardInfo {\n");
    sb.append("    rexyRewardId: ").append(toIndentedString(rexyRewardId)).append("\n");
    sb.append("    rexyRewardPoint: ").append(toIndentedString(rexyRewardPoint)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

