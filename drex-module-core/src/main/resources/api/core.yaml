openapi: 3.0.3
info:
  license:
    name: Apache 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0
  description: The Kweb API v1.0
  title: Kweb API
  version: '1.0'
servers:
  - description: kweb server url
    url: https://api.trex.dev.dipbit.xyz/v1
tags:
  - name: Rexy
    description: 恐龙相关接口描述
  - name: News
    description: 新闻相关接口描述
  - name: Video
    description: 视频相关接口描述
paths:
  # 新闻列表
  /news:
    $ref: 'paths/news.yaml'
  /news/banner:
    $ref: 'paths/banner.yaml'
  # 通知消息
  /notice:
    $ref: 'paths/notice.yaml'
  #我的恐龙
  /rexys/me:
    $ref: 'paths/rexyMe.yaml'
  #恐龙列表
  /rexys/avatar:
    $ref: 'paths/rexys.yaml'
  #恐龙待领取的蛋
  /rexys/reward/last:
    $ref: 'paths/rexyRewardLast.yaml'
  #收集奖励
  /rexys/reward/collect:
    $ref: 'paths/rexyRewardCollect.yaml'
  #构建接口api
  /rexys/claim/build/{basketType}:
    $ref: 'paths/rexyClaimBuild.yaml'
  #claim奖励
  /rexys/claim:
    $ref: 'paths/rexyClaim.yaml'
  #水龙头领水
  /tx/faucet:
    $ref: 'paths/faucetDrip.yaml'
  /news/dicovery/blog:
    $ref: 'paths/discoveryBlog.yaml'
  /news/discovery/events:
    $ref: 'paths/discoveryEvents.yaml'
  /video/session/init:
    $ref: 'paths/videoSessionInit.yaml'
  /video/events/report:
    $ref: 'paths/videoEventsReport.yaml'

components:
  securitySchemes:
    jwtBearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: |
        使用JWT Bearer令牌进行身份验证。
        在请求头中添加 `Authorization` 字段，格式为 `Bearer <JWT令牌>`。
