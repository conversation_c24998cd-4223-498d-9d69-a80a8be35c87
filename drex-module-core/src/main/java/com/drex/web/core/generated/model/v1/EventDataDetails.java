package com.drex.web.core.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.drex.web.core.generated.model.v1.ActivityStateDetails;
import com.drex.web.core.generated.model.v1.BlurDetails;
import com.drex.web.core.generated.model.v1.FocusDetails;
import com.drex.web.core.generated.model.v1.PauseDetails;
import com.drex.web.core.generated.model.v1.PlayDetails;
import com.drex.web.core.generated.model.v1.SeekDetails;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;


@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public interface EventDataDetails {
}
