package com.drex.web.customer.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * ThirdAuthorizeInfo
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class ThirdAuthorizeInfo {

  private String customerId;

  private String socialUserId;

  private String socialHandleName;

  private String socialProfileImage;

  public ThirdAuthorizeInfo customerId(String customerId) {
    this.customerId = customerId;
    return this;
  }

  /**
   * 用户id
   * @return customerId
  */
  
  @Schema(name = "customerId", description = "用户id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("customerId")
  public String getCustomerId() {
    return customerId;
  }

  public void setCustomerId(String customerId) {
    this.customerId = customerId;
  }

  public ThirdAuthorizeInfo socialUserId(String socialUserId) {
    this.socialUserId = socialUserId;
    return this;
  }

  /**
   * 三方用户id
   * @return socialUserId
  */
  
  @Schema(name = "socialUserId", description = "三方用户id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("socialUserId")
  public String getSocialUserId() {
    return socialUserId;
  }

  public void setSocialUserId(String socialUserId) {
    this.socialUserId = socialUserId;
  }

  public ThirdAuthorizeInfo socialHandleName(String socialHandleName) {
    this.socialHandleName = socialHandleName;
    return this;
  }

  /**
   * 三方用户名
   * @return socialHandleName
  */
  
  @Schema(name = "socialHandleName", description = "三方用户名", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("socialHandleName")
  public String getSocialHandleName() {
    return socialHandleName;
  }

  public void setSocialHandleName(String socialHandleName) {
    this.socialHandleName = socialHandleName;
  }

  public ThirdAuthorizeInfo socialProfileImage(String socialProfileImage) {
    this.socialProfileImage = socialProfileImage;
    return this;
  }

  /**
   * 三方头像
   * @return socialProfileImage
  */
  
  @Schema(name = "socialProfileImage", description = "三方头像", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("socialProfileImage")
  public String getSocialProfileImage() {
    return socialProfileImage;
  }

  public void setSocialProfileImage(String socialProfileImage) {
    this.socialProfileImage = socialProfileImage;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ThirdAuthorizeInfo thirdAuthorizeInfo = (ThirdAuthorizeInfo) o;
    return Objects.equals(this.customerId, thirdAuthorizeInfo.customerId) &&
        Objects.equals(this.socialUserId, thirdAuthorizeInfo.socialUserId) &&
        Objects.equals(this.socialHandleName, thirdAuthorizeInfo.socialHandleName) &&
        Objects.equals(this.socialProfileImage, thirdAuthorizeInfo.socialProfileImage);
  }

  @Override
  public int hashCode() {
    return Objects.hash(customerId, socialUserId, socialHandleName, socialProfileImage);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ThirdAuthorizeInfo {\n");
    sb.append("    customerId: ").append(toIndentedString(customerId)).append("\n");
    sb.append("    socialUserId: ").append(toIndentedString(socialUserId)).append("\n");
    sb.append("    socialHandleName: ").append(toIndentedString(socialHandleName)).append("\n");
    sb.append("    socialProfileImage: ").append(toIndentedString(socialProfileImage)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

