package com.drex.web.core.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.drex.web.core.generated.model.v1.RexyInfoRexy;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * RexyInfo
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class RexyInfo {

  private String customerId;

  private String avatar;

  private Long point;

  private RexyInfoRexy rexy;

  public RexyInfo customerId(String customerId) {
    this.customerId = customerId;
    return this;
  }

  /**
   * 用户id
   * @return customerId
  */
  
  @Schema(name = "customerId", description = "用户id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("customerId")
  public String getCustomerId() {
    return customerId;
  }

  public void setCustomerId(String customerId) {
    this.customerId = customerId;
  }

  public RexyInfo avatar(String avatar) {
    this.avatar = avatar;
    return this;
  }

  /**
   * 头像
   * @return avatar
  */
  
  @Schema(name = "avatar", description = "头像", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("avatar")
  public String getAvatar() {
    return avatar;
  }

  public void setAvatar(String avatar) {
    this.avatar = avatar;
  }

  public RexyInfo point(Long point) {
    this.point = point;
    return this;
  }

  /**
   * 积分
   * @return point
  */
  
  @Schema(name = "point", description = "积分", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("point")
  public Long getPoint() {
    return point;
  }

  public void setPoint(Long point) {
    this.point = point;
  }

  public RexyInfo rexy(RexyInfoRexy rexy) {
    this.rexy = rexy;
    return this;
  }

  /**
   * Get rexy
   * @return rexy
  */
  @Valid 
  @Schema(name = "rexy", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("rexy")
  public RexyInfoRexy getRexy() {
    return rexy;
  }

  public void setRexy(RexyInfoRexy rexy) {
    this.rexy = rexy;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RexyInfo rexyInfo = (RexyInfo) o;
    return Objects.equals(this.customerId, rexyInfo.customerId) &&
        Objects.equals(this.avatar, rexyInfo.avatar) &&
        Objects.equals(this.point, rexyInfo.point) &&
        Objects.equals(this.rexy, rexyInfo.rexy);
  }

  @Override
  public int hashCode() {
    return Objects.hash(customerId, avatar, point, rexy);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RexyInfo {\n");
    sb.append("    customerId: ").append(toIndentedString(customerId)).append("\n");
    sb.append("    avatar: ").append(toIndentedString(avatar)).append("\n");
    sb.append("    point: ").append(toIndentedString(point)).append("\n");
    sb.append("    rexy: ").append(toIndentedString(rexy)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

