package com.drex.web.customer.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * CustomerInfoRexy
 */

@JsonTypeName("CustomerInfo_rexy")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class CustomerInfoRexy {

  private String id;

  private String name;

  private Integer basketPoint;

  private Integer limitPoint;

  private Integer rate;

  private String avatar;

  public CustomerInfoRexy id(String id) {
    this.id = id;
    return this;
  }

  /**
   * 恐龙id
   * @return id
  */
  
  @Schema(name = "id", description = "恐龙id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("id")
  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public CustomerInfoRexy name(String name) {
    this.name = name;
    return this;
  }

  /**
   * 恐龙名称
   * @return name
  */
  
  @Schema(name = "name", description = "恐龙名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("name")
  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public CustomerInfoRexy basketPoint(Integer basketPoint) {
    this.basketPoint = basketPoint;
    return this;
  }

  /**
   * 购物车积分
   * @return basketPoint
  */
  
  @Schema(name = "basketPoint", description = "购物车积分", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("basketPoint")
  public Integer getBasketPoint() {
    return basketPoint;
  }

  public void setBasketPoint(Integer basketPoint) {
    this.basketPoint = basketPoint;
  }

  public CustomerInfoRexy limitPoint(Integer limitPoint) {
    this.limitPoint = limitPoint;
    return this;
  }

  /**
   * 篮子积分上限
   * @return limitPoint
  */
  
  @Schema(name = "limitPoint", description = "篮子积分上限", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("limitPoint")
  public Integer getLimitPoint() {
    return limitPoint;
  }

  public void setLimitPoint(Integer limitPoint) {
    this.limitPoint = limitPoint;
  }

  public CustomerInfoRexy rate(Integer rate) {
    this.rate = rate;
    return this;
  }

  /**
   * 当前产生速率
   * @return rate
  */
  
  @Schema(name = "rate", description = "当前产生速率", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("rate")
  public Integer getRate() {
    return rate;
  }

  public void setRate(Integer rate) {
    this.rate = rate;
  }

  public CustomerInfoRexy avatar(String avatar) {
    this.avatar = avatar;
    return this;
  }

  /**
   * 头像
   * @return avatar
  */
  
  @Schema(name = "avatar", description = "头像", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("avatar")
  public String getAvatar() {
    return avatar;
  }

  public void setAvatar(String avatar) {
    this.avatar = avatar;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CustomerInfoRexy customerInfoRexy = (CustomerInfoRexy) o;
    return Objects.equals(this.id, customerInfoRexy.id) &&
        Objects.equals(this.name, customerInfoRexy.name) &&
        Objects.equals(this.basketPoint, customerInfoRexy.basketPoint) &&
        Objects.equals(this.limitPoint, customerInfoRexy.limitPoint) &&
        Objects.equals(this.rate, customerInfoRexy.rate) &&
        Objects.equals(this.avatar, customerInfoRexy.avatar);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, name, basketPoint, limitPoint, rate, avatar);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CustomerInfoRexy {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    name: ").append(toIndentedString(name)).append("\n");
    sb.append("    basketPoint: ").append(toIndentedString(basketPoint)).append("\n");
    sb.append("    limitPoint: ").append(toIndentedString(limitPoint)).append("\n");
    sb.append("    rate: ").append(toIndentedString(rate)).append("\n");
    sb.append("    avatar: ").append(toIndentedString(avatar)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

