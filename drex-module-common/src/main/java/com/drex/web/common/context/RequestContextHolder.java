package com.drex.web.common.context;

/**
 * 请求上下文持有者，使用 ThreadLocal 存储请求上下文
 */
public class RequestContextHolder {

    private static final ThreadLocal<RequestContext> CONTEXT_HOLDER = new ThreadLocal<>();

    /**
     * 绑定请求上下文
     *
     * @param context 请求上下文
     */
    public static void bind(RequestContext context) {
        CONTEXT_HOLDER.set(context);
    }

    /**
     * 获取请求上下文
     *
     * @return 请求上下文
     */
    public static RequestContext getContext() {
        RequestContext context = CONTEXT_HOLDER.get();
        if (context == null) {
            context = new RequestContext();
            CONTEXT_HOLDER.set(context);
        }
        return context;
    }

    /**
     * 清除请求上下文
     */
    public static void clear() {
        CONTEXT_HOLDER.remove();
    }

    /**
     * 获取客户端IP地址
     *
     * @return 客户端IP地址
     */
    public static String getClientIp() {
        return getContext().getClientIp();
    }

}
