package com.drex.web.core.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * DiscoveryBlog
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class DiscoveryBlog {

  private String id;

  private String title;

  private String type;

  private String summary;

  private String link;

  private String content;

  private String image;

  private Boolean isRecommend;

  private Long created;

  public DiscoveryBlog id(String id) {
    this.id = id;
    return this;
  }

  /**
   * id
   * @return id
  */
  
  @Schema(name = "id", description = "id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("id")
  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public DiscoveryBlog title(String title) {
    this.title = title;
    return this;
  }

  /**
   * 标题
   * @return title
  */
  
  @Schema(name = "title", description = "标题", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("title")
  public String getTitle() {
    return title;
  }

  public void setTitle(String title) {
    this.title = title;
  }

  public DiscoveryBlog type(String type) {
    this.type = type;
    return this;
  }

  /**
   * 子标题，灰色小字
   * @return type
  */
  
  @Schema(name = "type", description = "子标题，灰色小字", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("type")
  public String getType() {
    return type;
  }

  public void setType(String type) {
    this.type = type;
  }

  public DiscoveryBlog summary(String summary) {
    this.summary = summary;
    return this;
  }

  /**
   * 摘要
   * @return summary
  */
  
  @Schema(name = "summary", description = "摘要", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("summary")
  public String getSummary() {
    return summary;
  }

  public void setSummary(String summary) {
    this.summary = summary;
  }

  public DiscoveryBlog link(String link) {
    this.link = link;
    return this;
  }

  /**
   * 正文内容链接
   * @return link
  */
  
  @Schema(name = "link", description = "正文内容链接", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("link")
  public String getLink() {
    return link;
  }

  public void setLink(String link) {
    this.link = link;
  }

  public DiscoveryBlog content(String content) {
    this.content = content;
    return this;
  }

  /**
   * 正文内容
   * @return content
  */
  
  @Schema(name = "content", description = "正文内容", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("content")
  public String getContent() {
    return content;
  }

  public void setContent(String content) {
    this.content = content;
  }

  public DiscoveryBlog image(String image) {
    this.image = image;
    return this;
  }

  /**
   * 图片
   * @return image
  */
  
  @Schema(name = "image", description = "图片", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("image")
  public String getImage() {
    return image;
  }

  public void setImage(String image) {
    this.image = image;
  }

  public DiscoveryBlog isRecommend(Boolean isRecommend) {
    this.isRecommend = isRecommend;
    return this;
  }

  /**
   * 是否推荐置顶
   * @return isRecommend
  */
  
  @Schema(name = "isRecommend", description = "是否推荐置顶", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("isRecommend")
  public Boolean getIsRecommend() {
    return isRecommend;
  }

  public void setIsRecommend(Boolean isRecommend) {
    this.isRecommend = isRecommend;
  }

  public DiscoveryBlog created(Long created) {
    this.created = created;
    return this;
  }

  /**
   * 创建时间
   * @return created
  */
  
  @Schema(name = "created", description = "创建时间", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("created")
  public Long getCreated() {
    return created;
  }

  public void setCreated(Long created) {
    this.created = created;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DiscoveryBlog discoveryBlog = (DiscoveryBlog) o;
    return Objects.equals(this.id, discoveryBlog.id) &&
        Objects.equals(this.title, discoveryBlog.title) &&
        Objects.equals(this.type, discoveryBlog.type) &&
        Objects.equals(this.summary, discoveryBlog.summary) &&
        Objects.equals(this.link, discoveryBlog.link) &&
        Objects.equals(this.content, discoveryBlog.content) &&
        Objects.equals(this.image, discoveryBlog.image) &&
        Objects.equals(this.isRecommend, discoveryBlog.isRecommend) &&
        Objects.equals(this.created, discoveryBlog.created);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, title, type, summary, link, content, image, isRecommend, created);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DiscoveryBlog {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    title: ").append(toIndentedString(title)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    summary: ").append(toIndentedString(summary)).append("\n");
    sb.append("    link: ").append(toIndentedString(link)).append("\n");
    sb.append("    content: ").append(toIndentedString(content)).append("\n");
    sb.append("    image: ").append(toIndentedString(image)).append("\n");
    sb.append("    isRecommend: ").append(toIndentedString(isRecommend)).append("\n");
    sb.append("    created: ").append(toIndentedString(created)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

