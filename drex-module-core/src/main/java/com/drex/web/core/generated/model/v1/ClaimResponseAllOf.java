package com.drex.web.core.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.drex.web.core.generated.model.v1.ClaimResponseAllOfObj;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * ClaimResponseAllOf
 */

@JsonTypeName("ClaimResponse_allOf")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class ClaimResponseAllOf {

  private ClaimResponseAllOfObj obj;

  public ClaimResponseAllOf obj(ClaimResponseAllOfObj obj) {
    this.obj = obj;
    return this;
  }

  /**
   * Get obj
   * @return obj
  */
  @Valid 
  @Schema(name = "obj", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("obj")
  public ClaimResponseAllOfObj getObj() {
    return obj;
  }

  public void setObj(ClaimResponseAllOfObj obj) {
    this.obj = obj;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ClaimResponseAllOf claimResponseAllOf = (ClaimResponseAllOf) o;
    return Objects.equals(this.obj, claimResponseAllOf.obj);
  }

  @Override
  public int hashCode() {
    return Objects.hash(obj);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ClaimResponseAllOf {\n");
    sb.append("    obj: ").append(toIndentedString(obj)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

