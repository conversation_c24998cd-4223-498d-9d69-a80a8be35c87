package com.drex.web.customer.generated.api.v1;

import com.drex.web.customer.generated.model.v1.AddDeveloperWaitListUploadResponse;
import com.drex.web.customer.generated.model.v1.AddWaitCreatorListRequest;
import com.drex.web.customer.generated.model.v1.AddWaitCreatorListResponse;
import com.drex.web.customer.generated.model.v1.AddWaitDeveloperListRequest;
import com.drex.web.customer.generated.model.v1.AddWaitDeveloperListResponse;
import com.drex.web.customer.generated.model.v1.AddWaitListRequest;
import com.drex.web.customer.generated.model.v1.AddWaitListResponse;
import com.drex.web.customer.generated.model.v1.BindSocialInfoRequest;
import com.drex.web.customer.generated.model.v1.BindSocialInfoResponse;
import com.drex.web.customer.generated.model.v1.InviteBindRequest;
import com.drex.web.customer.generated.model.v1.InviteBindResponse;
import com.drex.web.customer.generated.model.v1.InviteInfoResponse;
import com.drex.web.customer.generated.model.v1.PrivacyAuthResponse;
import com.drex.web.customer.generated.model.v1.QueryBySocialInfoResponse;
import com.drex.web.customer.generated.model.v1.ReservePrivacyAuthResponse;
import com.drex.web.customer.generated.model.v1.ReversePrivacyAuth;
import com.drex.web.customer.generated.model.v1.SocialUploadRequest;
import com.drex.web.customer.generated.model.v1.SocialUploadResponse;
import com.drex.web.customer.generated.model.v1.UnBindSocialInfoResponse;
import com.drex.web.customer.generated.model.v1.UnbindSocialInfoRequest;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.CookieValue;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.*;
import javax.validation.Valid;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
@Controller
@RequestMapping("${openapi.kweb.base-path:/v1}")
public class CustomerApiController implements CustomerApi {

    private final CustomerApiDelegate delegate;

    public CustomerApiController(@Autowired(required = false) CustomerApiDelegate delegate) {
        this.delegate = Optional.ofNullable(delegate).orElse(new CustomerApiDelegate() {});
    }

    @Override
    public CustomerApiDelegate getDelegate() {
        return delegate;
    }

}
