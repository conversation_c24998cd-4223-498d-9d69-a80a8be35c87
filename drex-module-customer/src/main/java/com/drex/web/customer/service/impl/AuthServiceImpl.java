package com.drex.web.customer.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.drex.web.common.CacheKey;
import com.drex.web.common.Constant;
import com.drex.web.common.config.WebProperties;
import com.drex.web.common.utils.JwtUtils;
import com.drex.web.common.utils.Web3Utils;
import com.drex.web.customer.generated.model.v1.Challenge;
import com.drex.web.customer.service.AuthService;
import com.drex.web.customer.sign.AccountType;
import com.drex.web.customer.sign.LoginSignatureDTO;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.web3j.crypto.Keys;
import org.web3j.crypto.Sign;
import org.web3j.utils.Numeric;

import java.math.BigInteger;
import java.security.SecureRandom;
import java.time.Instant;
import java.util.Arrays;
import java.util.Date;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class AuthServiceImpl implements AuthService {

    @Resource
    private RedisTemplate<String,String> redisTemplate;
    @Resource
    private WebProperties webProperties;

    private static final long expireTime = TimeUnit.MINUTES.toSeconds(10);


    public String getChallenge(String address, String type, String walletType) {
        String cacheKey = CacheKey.Auth.CHALLENGE.getKey() + type + ":" + walletType + ":" + address;
        return redisTemplate.opsForValue().get(cacheKey);
    }

    /**
     * 统一签名验证入口
     * 自动判断签名类型并选择正确的验证方式
     *
     * @param address 钱包地址
     * @param signature 签名数据
     * @return 验证结果
     */
    @Override
    public LoginSignatureDTO verifySignature(String address, String message ,String signature) {
        try {
            LoginSignatureDTO signatureDTO = new LoginSignatureDTO();
            // 1. 通过recId判断是否为标准椭圆曲线签名
            boolean isEOASignature = isEOASignature(signature);
            log.info("verifySignature address: {}, signature: {}, isEOASignature: {}",
                    address, signature, isEOASignature);

            // 2. 根据签名类型选择验证方法
            if (isEOASignature) {
                // 标准椭圆曲线签名使用EOA验证
                signatureDTO.setIsValid(verifyEOASignature(message, signature, address));
                signatureDTO.setAccountType(AccountType.EOA);
            }
            return signatureDTO;
        } catch (Exception e) {
            log.error("verifySignature error: {}", e.getMessage());
            return null;
        }
    }

    private boolean isEOASignature(String signature) {
        try {
            // 对于无效格式的签名数据，直接返回false
            if (StringUtils.isBlank(signature)) {
                return false;
            }

            // 移除0x前缀
            if (signature.startsWith("0x")) {
                signature = signature.substring(2);
            }

            // EOA签名格式通常是65字节: r (32字节) + s (32字节) + v (1字节)
            if (signature.length() != 130) {  // 每个字节2个十六进制字符
                return false;
            }

            // 获取v值(recovery id)
            String vHex = signature.substring(128, 130);
            int v = Integer.parseInt(vHex, 16);

            // 标准化v值
            int recId;
            if (v >= 27) {
                recId = v - 27;
            } else {
                recId = v;
            }

            // 有效的recId范围是0-3
            return (recId >= 0 && recId <= 3);
        } catch (Exception e) {
            log.debug("isEOASignature: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 验证EOA账户的签名
     */
    private boolean verifyEOASignature(String message, String signature, String address) {
        try {
            // 准备前缀消息
            String prefix = "\u0019Ethereum Signed Message:\n" + message.length();
            byte[] prefixedMessage = (prefix + message).getBytes();
            byte[] signatureBytes = Numeric.hexStringToByteArray(signature);

            // 提取v, r, s
            byte v = signatureBytes[64];
            if (v < 27) {
                v += 27;
            }

            byte[] r = Arrays.copyOfRange(signatureBytes, 0, 32);
            byte[] s = Arrays.copyOfRange(signatureBytes, 32, 64);

            Sign.SignatureData signatureData = new Sign.SignatureData(v, r, s);

            // 恢复公钥
            BigInteger publicKey = Sign.signedMessageToKey(prefixedMessage, signatureData);
            String recoveredAddress = "0x" + Keys.getAddress(publicKey);

            // 比较地址
            boolean isValid = recoveredAddress.equalsIgnoreCase(address);
            log.info("EOA签名验证结果: {}, 恢复地址: {}, 期望地址: {}",
                    isValid, recoveredAddress, address);

            return isValid;
        } catch (Exception e) {
            log.error("EOA签名验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 登出销毁
     * @param accessToken
     * @return  true
     */
    public boolean logout(String accessToken) {
        Jws<Claims> jws = JwtUtils.getAccessToken(accessToken);
        if (jws == null) {
            return false;
        }
        Date expiration = jws.getBody().getExpiration();
        long expireTime = expiration.getTime() - System.currentTimeMillis();
        if (expireTime > 0) {
            redisTemplate.opsForValue().set(CacheKey.Auth.INVALID_TOKEN.getKey() + accessToken, "1", expireTime, TimeUnit.MILLISECONDS);
        }
        return true;
    }
}
