package com.drex.web.customer.service;

import com.drex.customer.api.response.CustomerDTO;
import com.drex.web.customer.generated.model.v1.CustomerInfo;
import com.drex.web.customer.generated.model.v1.CustomerInfoResponse;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.springframework.web.bind.annotation.Mapping;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CustomerMapperStruct {

    public CustomerInfo toCustomerInfo(CustomerDTO customerInfo);
}
