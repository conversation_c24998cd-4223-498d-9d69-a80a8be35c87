package com.drex.web.customer.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * LoginRequest
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class LoginRequest {

  private String walletAddress;

  private String signature;

  private String code;

  private String socialPlatform;

  /**
   * Default constructor
   * @deprecated Use {@link LoginRequest#LoginRequest(String, String)}
   */
  @Deprecated
  public LoginRequest() {
    super();
  }

  /**
   * Constructor with only required parameters
   */
  public LoginRequest(String walletAddress, String signature) {
    this.walletAddress = walletAddress;
    this.signature = signature;
  }

  public LoginRequest walletAddress(String walletAddress) {
    this.walletAddress = walletAddress;
    return this;
  }

  /**
   * 钱包地址
   * @return walletAddress
  */
  @NotNull 
  @Schema(name = "walletAddress", description = "钱包地址", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("walletAddress")
  public String getWalletAddress() {
    return walletAddress;
  }

  public void setWalletAddress(String walletAddress) {
    this.walletAddress = walletAddress;
  }

  public LoginRequest signature(String signature) {
    this.signature = signature;
    return this;
  }

  /**
   * thirdWeb签名
   * @return signature
  */
  @NotNull 
  @Schema(name = "signature", description = "thirdWeb签名", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("signature")
  public String getSignature() {
    return signature;
  }

  public void setSignature(String signature) {
    this.signature = signature;
  }

  public LoginRequest code(String code) {
    this.code = code;
    return this;
  }

  /**
   * 验证码
   * @return code
  */
  
  @Schema(name = "code", description = "验证码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("code")
  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public LoginRequest socialPlatform(String socialPlatform) {
    this.socialPlatform = socialPlatform;
    return this;
  }

  /**
   * 社媒类型
   * @return socialPlatform
  */
  
  @Schema(name = "socialPlatform", description = "社媒类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("socialPlatform")
  public String getSocialPlatform() {
    return socialPlatform;
  }

  public void setSocialPlatform(String socialPlatform) {
    this.socialPlatform = socialPlatform;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    LoginRequest loginRequest = (LoginRequest) o;
    return Objects.equals(this.walletAddress, loginRequest.walletAddress) &&
        Objects.equals(this.signature, loginRequest.signature) &&
        Objects.equals(this.code, loginRequest.code) &&
        Objects.equals(this.socialPlatform, loginRequest.socialPlatform);
  }

  @Override
  public int hashCode() {
    return Objects.hash(walletAddress, signature, code, socialPlatform);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class LoginRequest {\n");
    sb.append("    walletAddress: ").append(toIndentedString(walletAddress)).append("\n");
    sb.append("    signature: ").append(toIndentedString(signature)).append("\n");
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    socialPlatform: ").append(toIndentedString(socialPlatform)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

