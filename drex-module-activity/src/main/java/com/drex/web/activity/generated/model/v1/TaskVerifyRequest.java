package com.drex.web.activity.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * TaskVerifyRequest
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class TaskVerifyRequest {

  private String taskId;

  /**
   * Default constructor
   * @deprecated Use {@link TaskVerifyRequest#TaskVerifyRequest(String)}
   */
  @Deprecated
  public TaskVerifyRequest() {
    super();
  }

  /**
   * Constructor with only required parameters
   */
  public TaskVerifyRequest(String taskId) {
    this.taskId = taskId;
  }

  public TaskVerifyRequest taskId(String taskId) {
    this.taskId = taskId;
    return this;
  }

  /**
   * 任务id
   * @return taskId
  */
  @NotNull 
  @Schema(name = "taskId", description = "任务id", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("taskId")
  public String getTaskId() {
    return taskId;
  }

  public void setTaskId(String taskId) {
    this.taskId = taskId;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TaskVerifyRequest taskVerifyRequest = (TaskVerifyRequest) o;
    return Objects.equals(this.taskId, taskVerifyRequest.taskId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(taskId);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TaskVerifyRequest {\n");
    sb.append("    taskId: ").append(toIndentedString(taskId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

