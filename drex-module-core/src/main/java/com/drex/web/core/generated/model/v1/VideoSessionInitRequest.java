package com.drex.web.core.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * VideoSessionInitRequest
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class VideoSessionInitRequest {

  private String videoId;

  private String platform;

  /**
   * Default constructor
   * @deprecated Use {@link VideoSessionInitRequest#VideoSessionInitRequest(String, String)}
   */
  @Deprecated
  public VideoSessionInitRequest() {
    super();
  }

  /**
   * Constructor with only required parameters
   */
  public VideoSessionInitRequest(String videoId, String platform) {
    this.videoId = videoId;
    this.platform = platform;
  }

  public VideoSessionInitRequest videoId(String videoId) {
    this.videoId = videoId;
    return this;
  }

  /**
   * 视频标识符
   * @return videoId
  */
  @NotNull 
  @Schema(name = "videoId", description = "视频标识符", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("videoId")
  public String getVideoId() {
    return videoId;
  }

  public void setVideoId(String videoId) {
    this.videoId = videoId;
  }

  public VideoSessionInitRequest platform(String platform) {
    this.platform = platform;
    return this;
  }

  /**
   * 平台，例如YouTube
   * @return platform
  */
  @NotNull 
  @Schema(name = "platform", description = "平台，例如YouTube", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("platform")
  public String getPlatform() {
    return platform;
  }

  public void setPlatform(String platform) {
    this.platform = platform;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    VideoSessionInitRequest videoSessionInitRequest = (VideoSessionInitRequest) o;
    return Objects.equals(this.videoId, videoSessionInitRequest.videoId) &&
        Objects.equals(this.platform, videoSessionInitRequest.platform);
  }

  @Override
  public int hashCode() {
    return Objects.hash(videoId, platform);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class VideoSessionInitRequest {\n");
    sb.append("    videoId: ").append(toIndentedString(videoId)).append("\n");
    sb.append("    platform: ").append(toIndentedString(platform)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

