package com.drex.web.common;

import com.kikitrade.framework.common.model.Response;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @date: 2023/2/13 17:50
 */
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class WebResult implements Serializable {

    private boolean success;

    private String code;

    private String message;

    public void success() {
        this.success = true;
        this.code = "0000";
        this.message = "success";
    }

    public void fail(String code, String msgKey) {
        this.success = false;
        this.code = code;
        this.message = msgKey;
    }

    public void fail(Response response) {
        this.success = false;
        this.code = response.getCode();
        this.message = response.getMessage();
    }
}

