package com.drex.web.common.utils;

import org.web3j.utils.Convert;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class Web3Utils {

    private final static String signatureTemplate = "trex wants you to sign in with your Ethereum account:\n{0}\n\nPlease ensure that the domain above matches the URL of the current website.\n\nURI: {1}\nVersion: 1.0\nChain ID: {2}\nNonce: {3}\nIssued At: {4}\nExpiration Time: {5}\nNot Before: {6}";
    public final static String signatureTemplateForBind = "Sign in with your Wallet:\n{0}\n\nSign in with ethereum to T-Rex\n\n\nVersion:1\nChain ID:1962\nNonce:{1}\nIssued At:{2}";

    /**
     * 根据参数替换模板中的占位符
     * @param args 参数列表，按顺序替换到模板中的占位符
     * @return 替换完成后的字符串
     */
    public static String getSignatureMessage(String... args) {
        return getSignatureMessage(signatureTemplate, args);
    }

    public static String getSignatureMessage(String signatureTemplate, String[] args) {

        if (args == null || args.length == 0) {
            return signatureTemplate; // 如果模板或参数为空，直接返回模板
        }

        String result = signatureTemplate;
        for (int i = 0; i < args.length; i++) {
            result = result.replace("{" + i + "}", args[i] != null ? args[i] : "");
        }
        return result;
    }

    public static String[] parseSignatureParam(String result) {
        return parseSignatureParam(signatureTemplate,result);
    }

    /**
     * 根据结果字符串解析出模板中的参数值
     * @param result 替换占位符后的结果字符串
     * @return 一个长度为7的字符串数组，包含解析出的参数值
     */
    public static String[] parseSignatureParam(String signatureTemplate,String result) {
        if (result == null) {
            return new String[0]; // 如果模板或结果为空，返回空数组
        }

        // 使用正则表达式找到所有占位符
        Pattern pattern = Pattern.compile("\\{\\d+\\}");
        Matcher matcher = pattern.matcher(signatureTemplate);

        // 使用StringBuilder动态生成正则表达式
        StringBuilder regexBuilder = new StringBuilder();
        int lastIndex = 0;

        while (matcher.find()) {
            regexBuilder.append(Pattern.quote(signatureTemplate.substring(lastIndex, matcher.start())));
            regexBuilder.append("(.*?)"); // 捕获组匹配参数值
            lastIndex = matcher.end();
        }
        regexBuilder.append(Pattern.quote(signatureTemplate.substring(lastIndex))); // 追加模板末尾部分

        Pattern resultPattern = Pattern.compile(regexBuilder.toString());
        Matcher resultMatcher = resultPattern.matcher(result);

        String[] args = new String[7];
        if (resultMatcher.matches()) {
            for (int i = 1; i <= resultMatcher.groupCount(); i++) {
                args[i - 1] = resultMatcher.group(i); // 捕获的参数值按顺序存入数组
            }
        }

        return args;
    }

    public static Convert.Unit getUnitByPrecision(int precision) {
        switch (precision) {
            case 18:
                return Convert.Unit.ETHER;
            case 15:
                return Convert.Unit.FINNEY;
            case 12:
                return Convert.Unit.SZABO;
            case 9:
                return Convert.Unit.GWEI;
            case 6:
                return Convert.Unit.MWEI;
            case 0:
                return Convert.Unit.WEI;
            default:
                return null;
        }
    }
    public static Optional<Integer> getDecimalByAddress(Map<Long, List<Map<String, String>>> chainConfig, Long chainId, String address) {
        if (chainConfig == null || !chainConfig.containsKey(chainId)) {
            return Optional.empty();
        }

        List<Map<String, String>> configList = chainConfig.get(chainId);
        for (Map<String, String> config : configList) {
            if (address.toLowerCase().equalsIgnoreCase(config.get("address"))) {
                String decimalStr = config.get("decimal");
                if (decimalStr != null) {
                    try {
                        return Optional.of(Integer.parseInt(decimalStr));
                    } catch (NumberFormatException e) {
                        // 处理数字格式异常
                        return Optional.empty();
                    }
                }
            }
        }

        return Optional.empty();
    }
    public static void main(String[] args) {
        // 测试填充模板
        String filled = getSignatureMessage("Alice", "12345", "49.99", "New York", "2025-01-20", "123-456-7890", "XYZ123");
        System.out.println("Filled Template: " + filled);

        // 测试解析模板
        String[] parsedArgs = parseSignatureParam(filled);
        System.out.println("Parsed Arguments:");
        for (int i = 0; i < parsedArgs.length; i++) {
            System.out.println("Arg " + i + ": " + parsedArgs[i]);
        }
    }
}
