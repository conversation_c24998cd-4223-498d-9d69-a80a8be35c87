package com.drex.web.customer.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * ThirdBindingsInfoInner
 */

@JsonTypeName("ThirdBindingsInfo_inner")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class ThirdBindingsInfoInner {

  private String platform;

  private String status;

  private String connectUrl;

  private String socialHandleName;

  private String socialProfileImage;

  /**
   * Default constructor
   * @deprecated Use {@link ThirdBindingsInfoInner#ThirdBindingsInfoInner(String, String, String, String, String)}
   */
  @Deprecated
  public ThirdBindingsInfoInner() {
    super();
  }

  /**
   * Constructor with only required parameters
   */
  public ThirdBindingsInfoInner(String platform, String status, String connectUrl, String socialHandleName, String socialProfileImage) {
    this.platform = platform;
    this.status = status;
    this.connectUrl = connectUrl;
    this.socialHandleName = socialHandleName;
    this.socialProfileImage = socialProfileImage;
  }

  public ThirdBindingsInfoInner platform(String platform) {
    this.platform = platform;
    return this;
  }

  /**
   * 第三方平台名称
   * @return platform
  */
  @NotNull 
  @Schema(name = "platform", description = "第三方平台名称", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("platform")
  public String getPlatform() {
    return platform;
  }

  public void setPlatform(String platform) {
    this.platform = platform;
  }

  public ThirdBindingsInfoInner status(String status) {
    this.status = status;
    return this;
  }

  /**
   * 绑定状态 0-未绑定 1-已绑定
   * @return status
  */
  @NotNull 
  @Schema(name = "status", description = "绑定状态 0-未绑定 1-已绑定", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("status")
  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public ThirdBindingsInfoInner connectUrl(String connectUrl) {
    this.connectUrl = connectUrl;
    return this;
  }

  /**
   * 授权地址
   * @return connectUrl
  */
  @NotNull 
  @Schema(name = "connectUrl", description = "授权地址", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("connectUrl")
  public String getConnectUrl() {
    return connectUrl;
  }

  public void setConnectUrl(String connectUrl) {
    this.connectUrl = connectUrl;
  }

  public ThirdBindingsInfoInner socialHandleName(String socialHandleName) {
    this.socialHandleName = socialHandleName;
    return this;
  }

  /**
   * 社交账号名称
   * @return socialHandleName
  */
  @NotNull 
  @Schema(name = "socialHandleName", description = "社交账号名称", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("socialHandleName")
  public String getSocialHandleName() {
    return socialHandleName;
  }

  public void setSocialHandleName(String socialHandleName) {
    this.socialHandleName = socialHandleName;
  }

  public ThirdBindingsInfoInner socialProfileImage(String socialProfileImage) {
    this.socialProfileImage = socialProfileImage;
    return this;
  }

  /**
   * 社交账号头像URL
   * @return socialProfileImage
  */
  @NotNull 
  @Schema(name = "socialProfileImage", description = "社交账号头像URL", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("socialProfileImage")
  public String getSocialProfileImage() {
    return socialProfileImage;
  }

  public void setSocialProfileImage(String socialProfileImage) {
    this.socialProfileImage = socialProfileImage;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ThirdBindingsInfoInner thirdBindingsInfoInner = (ThirdBindingsInfoInner) o;
    return Objects.equals(this.platform, thirdBindingsInfoInner.platform) &&
        Objects.equals(this.status, thirdBindingsInfoInner.status) &&
        Objects.equals(this.connectUrl, thirdBindingsInfoInner.connectUrl) &&
        Objects.equals(this.socialHandleName, thirdBindingsInfoInner.socialHandleName) &&
        Objects.equals(this.socialProfileImage, thirdBindingsInfoInner.socialProfileImage);
  }

  @Override
  public int hashCode() {
    return Objects.hash(platform, status, connectUrl, socialHandleName, socialProfileImage);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ThirdBindingsInfoInner {\n");
    sb.append("    platform: ").append(toIndentedString(platform)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    connectUrl: ").append(toIndentedString(connectUrl)).append("\n");
    sb.append("    socialHandleName: ").append(toIndentedString(socialHandleName)).append("\n");
    sb.append("    socialProfileImage: ").append(toIndentedString(socialProfileImage)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

