/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.drex.web.core.websocket;

import com.alibaba.fastjson2.JSON;
import com.drex.core.api.RemoteVideoAntiCheatService;
import com.drex.core.api.request.VideoReportRequest;
import com.drex.core.api.response.VideoReportResponse;
import com.kikitrade.framework.common.model.Response;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @date 2019-08-10 15:46
 */
@Component
@Slf4j
public class WebSocketServer extends TextWebSocketHandler {

    @DubboReference
    private RemoteVideoAntiCheatService remoteVideoAntiCheatService;

    // 存储WebSocket会话
    private final Map<String, WebSocketSession> sessions = new ConcurrentHashMap<>();

    // 存储会话信息
    private final Map<String, SessionInfo> sessionInfoMap = new ConcurrentHashMap<>();

    // 心跳检测定时器
    private final ScheduledExecutorService heartbeatExecutor = Executors.newScheduledThreadPool(2);

    // 存储每个会话的心跳检测任务
    private final Map<String, ScheduledFuture<?>> heartbeatTasks = new ConcurrentHashMap<>();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String sessionId = session.getId();
        sessions.put(sessionId, session);

        // 初始化会话信息
        SessionInfo sessionInfo = new SessionInfo();
        sessionInfo.setLastHeartbeatTime(System.currentTimeMillis());
        sessionInfo.setLastEventTime(System.currentTimeMillis());
        sessionInfoMap.put(sessionId, sessionInfo);

        log.info("WebSocket connection established: {}", sessionId);

        // 发送连接确认消息
        WebSocketMessage welcomeMessage = WebSocketMessage.builder()
                .messageType(WebSocketMessage.MessageType.EVENT_ACK)
                .messageId(generateMessageId())
                .timestamp(System.currentTimeMillis())
                .responseData(WebSocketMessage.ResponseData.builder()
                        .status("CONNECTED")
                        .build())
                .build();

        sendMessage(session, welcomeMessage);

        // 启动心跳检测
        startHeartbeatCheck(sessionId);
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        try {
            String payload = message.getPayload().toString();
            WebSocketMessage wsMessage = JSON.parseObject(payload, WebSocketMessage.class);
            log.debug("Received WebSocket message: type={}, sessionId={}",
                    wsMessage.getMessageType(), session.getId());

            switch (wsMessage.getMessageType()) {
                case EVENT_REPORT:
                    handleEventReport(session, wsMessage);
                    break;
                case HEARTBEAT:
                    handleHeartbeat(session, wsMessage);
                    break;
                default:
                    log.warn("Unknown message type: {}", wsMessage.getMessageType());
                    break;
            }

        } catch (Exception e) {
            log.error("Failed to handle WebSocket message from session: {}", session.getId(), e);
            sendErrorMessage(session, "MESSAGE_PROCESSING_ERROR", "Failed to process message: " + e.getMessage());
        }
    }


    /**
     * 处理事件上报
     */
    private void handleEventReport(WebSocketSession session, WebSocketMessage wsMessage) {
        try {
            VideoReportRequest eventData = wsMessage.getEventData();
            if (eventData == null) {
                sendErrorMessage(session, "MISSING_EVENT_DATA", "Event data is required");
                return;
            }

            // 更新会话活跃时间 - 事件上报也表示连接活跃
            updateSessionActivity(session.getId());

            eventData.setTimestamp(wsMessage.getTimestamp());
            Response<VideoReportResponse> response = remoteVideoAntiCheatService.reportEvents(eventData);
            WebSocketMessage.RewardInfo rewardInfo = new WebSocketMessage.RewardInfo();
            if(response.getData() != null && response.getData().getRewardInfo() != null){
                BeanUtils.copyProperties(response.getData().getRewardInfo(), rewardInfo);
            }

            WebSocketMessage eventAck = WebSocketMessage.builder()
                    .messageType(WebSocketMessage.MessageType.EVENT_ACK)
                    .messageId(generateMessageId())
                    .timestamp(System.currentTimeMillis())
                    .responseData(WebSocketMessage.ResponseData.builder()
                            .status("SUCCESS")
                            .rewardInfo(rewardInfo)
                            .build())
                    .build();
            sendMessage(session, eventAck);

        } catch (Exception e) {
            log.error("Failed to handle event report from session: {}", session.getId(), e);
            sendErrorMessage(session, "EVENT_PROCESSING_ERROR", "Failed to process events: " + e.getMessage());
        }
    }

    /**
     * 处理心跳
     */
    private void handleHeartbeat(WebSocketSession session, WebSocketMessage wsMessage) {
        try {
            log.debug("Received heartbeat from session: {}", session.getId());

            // 更新会话活跃时间
            updateSessionActivity(session.getId());

            // 发送心跳确认
            WebSocketMessage heartbeatAck = WebSocketMessage.builder()
                    .messageType(WebSocketMessage.MessageType.HEARTBEAT_ACK)
                    .messageId(generateMessageId())
                    .timestamp(System.currentTimeMillis())
                    .responseData(WebSocketMessage.ResponseData.builder()
                            .status("HEARTBEAT_OK")
                            .build())
                    .build();

            sendMessage(session, heartbeatAck);

        } catch (Exception e) {
            log.error("Failed to handle heartbeat from session: {}", session.getId(), e);
        }
    }

    /**
     * 更新会话信息
     */
//    private SessionInfo updateSessionInfo(String wsSessionId, RexyReportRequest eventData,
//                                          List<SessionEvent> sessionEvents) {
//        SessionInfo sessionInfo = sessionInfoMap.computeIfAbsent(wsSessionId, k -> new SessionInfo());
//
//        sessionInfo.setYoutubeSessionId(eventData.getSessionId());
//        sessionInfo.setCustomerId(eventData.getCustomerId());
//        sessionInfo.setLastEventTime(System.currentTimeMillis());
//        sessionInfo.setLastHeartbeatTime(System.currentTimeMillis());
//
//        // 从事件中提取视频信息（这里需要根据实际事件结构调整）
//        if (!sessionEvents.isEmpty()) {
//            SessionEvent firstEvent = sessionEvents.get(0);
//            if (firstEvent.getEventData() != null) {
//                // 假设视频ID和时长在事件数据中
//                Object videoId = firstEvent.getEventData().get("videoId");
//                Object duration = firstEvent.getEventData().get("duration");
//
//                if (videoId != null) {
//                    sessionInfo.setVideoId(videoId.toString());
//                }
//                if (duration != null && duration instanceof Number) {
//                    sessionInfo.setVideoDurationSeconds(((Number) duration).intValue());
//                }
//            }
//        }
//
//        return sessionInfo;
//    }
    

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        log.error("WebSocket transport error for session: {}", session.getId(), exception);
        cleanupSession(session.getId());
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        log.info("WebSocket connection closed: {}, status: {}", session.getId(), closeStatus);
        cleanupSession(session.getId());
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }
    
    /**
     * 发送消息
     */
    private void sendMessage(WebSocketSession session, WebSocketMessage message) {
        try {
            if (session.isOpen()) {
                String json = JSON.toJSONString(message);
                session.sendMessage(new TextMessage(json));
            }
        } catch (IOException e) {
            log.error("Failed to send WebSocket message to session: {}", session.getId(), e);
        }
    }

    /**
     * 发送错误消息
     */
    private void sendErrorMessage(WebSocketSession session, String errorCode, String errorMessage) {
        WebSocketMessage errorMsg = WebSocketMessage.builder()
                .messageType(WebSocketMessage.MessageType.ERROR)
                .messageId(generateMessageId())
                .timestamp(System.currentTimeMillis())
                .errorInfo(WebSocketMessage.ErrorInfo.builder()
                        .errorCode(errorCode)
                        .errorMessage(errorMessage)
                        .retryable(false)
                        .build())
                .build();

        sendMessage(session, errorMsg);
    }

    /**
     * 更新会话活跃时间
     */
    private void updateSessionActivity(String sessionId) {
        SessionInfo sessionInfo = sessionInfoMap.get(sessionId);
        if (sessionInfo != null) {
            long currentTime = System.currentTimeMillis();
            sessionInfo.setLastHeartbeatTime(currentTime);
            sessionInfo.setLastEventTime(currentTime);
        }
    }

    /**
     * 启动心跳检测
     */
    private void startHeartbeatCheck(String sessionId) {
        ScheduledFuture<?> heartbeatTask = heartbeatExecutor.scheduleWithFixedDelay(() -> {
            try {
                SessionInfo sessionInfo = sessionInfoMap.get(sessionId);
                WebSocketSession session = sessions.get(sessionId);

                if (sessionInfo == null || session == null || !session.isOpen()) {
                    cleanupSession(sessionId);
                    return;
                }

                long now = System.currentTimeMillis();
                long lastActivity = Math.max(sessionInfo.getLastHeartbeatTime(), sessionInfo.getLastEventTime());

                // 如果超过90秒没有任何活动（心跳或事件上报），关闭连接
                // 增加超时时间，因为前端在观看视频时可能暂停心跳
                if (now - lastActivity > 90000) {
                    log.warn("Session {} activity timeout ({}ms since last activity), closing connection",
                            sessionId, now - lastActivity);
                    try {
                        session.close(CloseStatus.GOING_AWAY);
                    } catch (IOException e) {
                        log.error("Failed to close session: {}", sessionId, e);
                    }
                    cleanupSession(sessionId);
                }

            } catch (Exception e) {
                log.error("Error in heartbeat check for session: {}", sessionId, e);
            }
        }, 30, 30, TimeUnit.SECONDS);

        // 保存任务引用，用于后续清理
        heartbeatTasks.put(sessionId, heartbeatTask);
    }

    /**
     * 清理会话
     */
    private void cleanupSession(String sessionId) {
        sessions.remove(sessionId);
        sessionInfoMap.remove(sessionId);

        // 取消并清理心跳检测任务
        ScheduledFuture<?> heartbeatTask = heartbeatTasks.remove(sessionId);
        if (heartbeatTask != null && !heartbeatTask.isCancelled()) {
            heartbeatTask.cancel(true);
        }

        log.debug("Cleaned up session: {}", sessionId);
    }

    /**
     * 生成消息ID
     */
    private String generateMessageId() {
        return "msg_" + System.currentTimeMillis() + "_" + Thread.currentThread().getId();
    }
    

    /**
     * 会话信息
     */
    @Data
    private static class SessionInfo {
        private String youtubeSessionId;
        private String customerId;
        private String videoId;
        private int videoDurationSeconds;
        private long lastEventTime;
        private long lastHeartbeatTime;
    }
}
