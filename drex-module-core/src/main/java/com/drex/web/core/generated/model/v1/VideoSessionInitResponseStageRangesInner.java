package com.drex.web.core.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * VideoSessionInitResponseStageRangesInner
 */

@JsonTypeName("VideoSessionInitResponse_stageRanges_inner")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class VideoSessionInitResponseStageRangesInner {

  private Integer level;

  private String name;

  private Double minRatio;

  private Double maxRatio;

  private String rewardAmount;

  public VideoSessionInitResponseStageRangesInner level(Integer level) {
    this.level = level;
    return this;
  }

  /**
   * 阶段级别，例如：1, 2, 3
   * @return level
  */
  
  @Schema(name = "level", description = "阶段级别，例如：1, 2, 3", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("level")
  public Integer getLevel() {
    return level;
  }

  public void setLevel(Integer level) {
    this.level = level;
  }

  public VideoSessionInitResponseStageRangesInner name(String name) {
    this.name = name;
    return this;
  }

  /**
   * 阶段名称，例如：第一阶段
   * @return name
  */
  
  @Schema(name = "name", description = "阶段名称，例如：第一阶段", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("name")
  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public VideoSessionInitResponseStageRangesInner minRatio(Double minRatio) {
    this.minRatio = minRatio;
    return this;
  }

  /**
   * 最小比例值
   * @return minRatio
  */
  
  @Schema(name = "minRatio", description = "最小比例值", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("minRatio")
  public Double getMinRatio() {
    return minRatio;
  }

  public void setMinRatio(Double minRatio) {
    this.minRatio = minRatio;
  }

  public VideoSessionInitResponseStageRangesInner maxRatio(Double maxRatio) {
    this.maxRatio = maxRatio;
    return this;
  }

  /**
   * 最大比例值
   * @return maxRatio
  */
  
  @Schema(name = "maxRatio", description = "最大比例值", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("maxRatio")
  public Double getMaxRatio() {
    return maxRatio;
  }

  public void setMaxRatio(Double maxRatio) {
    this.maxRatio = maxRatio;
  }

  public VideoSessionInitResponseStageRangesInner rewardAmount(String rewardAmount) {
    this.rewardAmount = rewardAmount;
    return this;
  }

  /**
   * 奖励值
   * @return rewardAmount
  */
  
  @Schema(name = "rewardAmount", description = "奖励值", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("rewardAmount")
  public String getRewardAmount() {
    return rewardAmount;
  }

  public void setRewardAmount(String rewardAmount) {
    this.rewardAmount = rewardAmount;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    VideoSessionInitResponseStageRangesInner videoSessionInitResponseStageRangesInner = (VideoSessionInitResponseStageRangesInner) o;
    return Objects.equals(this.level, videoSessionInitResponseStageRangesInner.level) &&
        Objects.equals(this.name, videoSessionInitResponseStageRangesInner.name) &&
        Objects.equals(this.minRatio, videoSessionInitResponseStageRangesInner.minRatio) &&
        Objects.equals(this.maxRatio, videoSessionInitResponseStageRangesInner.maxRatio) &&
        Objects.equals(this.rewardAmount, videoSessionInitResponseStageRangesInner.rewardAmount);
  }

  @Override
  public int hashCode() {
    return Objects.hash(level, name, minRatio, maxRatio, rewardAmount);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class VideoSessionInitResponseStageRangesInner {\n");
    sb.append("    level: ").append(toIndentedString(level)).append("\n");
    sb.append("    name: ").append(toIndentedString(name)).append("\n");
    sb.append("    minRatio: ").append(toIndentedString(minRatio)).append("\n");
    sb.append("    maxRatio: ").append(toIndentedString(maxRatio)).append("\n");
    sb.append("    rewardAmount: ").append(toIndentedString(rewardAmount)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

