package com.drex.web.customer.delegate;

import com.alibaba.fastjson2.JSON;
import com.drex.customer.api.RemoteAuthService;
import com.drex.customer.api.RemotePassportService;
import com.drex.customer.api.constants.WalletConstant;
import com.drex.customer.api.request.AuthLoginRequest;
import com.drex.customer.api.request.BindWalletRequest;
import com.drex.customer.api.request.ThirdAuthRequest;
import com.drex.customer.api.response.PassportDTO;
import com.drex.customer.api.response.ThirdAuthDTO;
import com.drex.customer.api.response.ThirdBindingsDTO;
import com.drex.web.common.*;
import com.drex.web.common.config.WebProperties;
import com.drex.web.common.context.RequestContextHolder;
import com.drex.web.common.utils.JwtUtils;
import com.drex.web.common.utils.SignUtil;
import cn.hutool.core.codec.Base58;
import org.web3j.utils.Numeric;
import com.drex.web.customer.enums.WalletType;
import com.drex.web.customer.generated.api.v1.AuthApiDelegate;
import com.drex.web.customer.generated.model.v1.*;
import com.drex.web.customer.service.ChallengeService;
import com.drex.web.customer.service.AuthMapperStruct;
import com.drex.web.customer.service.AuthService;
import com.kikitrade.framework.common.model.Response;
import com.kikitrade.framework.observability.metrics.business.KiKiMonitor;
import com.kikitrade.gateway.client.QuestsApi;
import com.kikitrade.gateway.client.model.SocialAuthUrlResponse;
import com.kikitrade.gateway.client.model.WebResultAuthUrlsVO;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Arrays;
import java.util.List;
import java.util.Date;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class AuthApiDelegateImpl implements AuthApiDelegate {

    @Resource
    private AuthService authService;
    @Resource
    private ChallengeService challengeService;
    @Resource
    private KiKiMonitor kiKiMonitor;
    @DubboReference
    private RemoteAuthService remoteAuthService;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private AuthMapperStruct authMapperStruct;
    @DubboReference
    public RemotePassportService remotePassportService;
    @Resource
    private QuestsApi questsApi;
    @Resource
    private WebProperties webProperties;

    @Override
    public ResponseEntity<AccessTokenResponse> authLogin(LoginRequest loginRequest, String osDeviceId) {
        log.info("authLogin request:{}", JSON.toJSONString(loginRequest));
        kiKiMonitor.monitor(BusinessMonitor.LOGIN, new String[]{"code", "request"});
        AccessTokenResponse accessTokenResponse = new AccessTokenResponse();
        accessTokenResponse.success();
        String challenge = challengeService.getChallenge(loginRequest.getWalletAddress(), Constant.ChallengeType.social.name(), WalletType.EVM.name());
        if (challenge == null) {
            accessTokenResponse.fail(ErrorCode.SIGNATURE_INVALID.getCode(), "challenge is expired");
            return ResponseEntity.badRequest().body(accessTokenResponse);
        }
        challengeService.deleteChallenge(loginRequest.getWalletAddress(), Constant.ChallengeType.social.name(), WalletType.EVM.name());
        //验签
        boolean verified = SignUtil.verifyEOASignature(challenge, loginRequest.getSignature(), loginRequest.getWalletAddress());
        if (!verified) {
            accessTokenResponse.fail(ErrorCode.SIGNATURE_INVALID.getCode(), "signature is invalid");
            return ResponseEntity.badRequest().body(accessTokenResponse);
        }

        AuthLoginRequest authLoginRequest = new AuthLoginRequest();
        authLoginRequest.setWalletAddress(loginRequest.getWalletAddress());
        if (StringUtils.isNotEmpty(loginRequest.getSocialPlatform())) {
            authLoginRequest.setSubConnectProvider(WalletConstant.PlatformEnum.getEnumByCode(loginRequest.getSocialPlatform()));
        }
        authLoginRequest.setClientIp(RequestContextHolder.getClientIp());
        authLoginRequest.setDeviceInfo(osDeviceId);

        Response<PassportDTO> loginResponse = remoteAuthService.login(authLoginRequest);
        if(loginResponse.isSuccess()){
            log.info("{}::{}::{}::{}::{}::{}", Constant.STATISTICS_ORIGINAL_DATA, "LOGIN", loginResponse.getData().getPassportId(), RequestContextHolder.getClientIp(), osDeviceId, JSON.toJSONString(loginResponse.getData()));
            JwtToken accessToken = JwtUtils.generateTokenPair(String.valueOf(loginResponse.getData().getPassportId()), String.valueOf(loginResponse.getData().getPassportId()));
            accessTokenResponse.setObj(new AccessToken()
                    .accessToken(accessToken.getAccessToken())
                    .refreshToken(accessToken.getRefreshToken())
                    .isNewUser(loginResponse.getData().getIsNewUser())
                    .isNewPassport(loginResponse.getData().getIsNewPassport()));
            log.info("authLogin response:{}", JSON.toJSONString(accessTokenResponse));
            return ResponseEntity.ok(accessTokenResponse);
        }
        kiKiMonitor.monitor(BusinessMonitor.LOGIN, new String[]{"code", "login_failed"});
        accessTokenResponse.fail(loginResponse.getCode(), loginResponse.getMessage());
        return ResponseEntity.badRequest().body(accessTokenResponse);
    }

    @Override
    public ResponseEntity<AccessTokenResponse> qrAuthLogin(LoginRequest loginRequest, String osDeviceId) {
        log.info("authLogin request:{}", JSON.toJSONString(loginRequest));
        kiKiMonitor.monitor(BusinessMonitor.LOGIN, new String[]{"code", "request"});
        AccessTokenResponse accessTokenResponse = new AccessTokenResponse();
        accessTokenResponse.success();
        String challenge = challengeService.getChallenge(loginRequest.getWalletAddress(), Constant.ChallengeType.social.name(), WalletType.EVM.name());
        if (challenge == null) {
            accessTokenResponse.fail(ErrorCode.SIGNATURE_INVALID.getCode(), "challenge is expired");
            return ResponseEntity.badRequest().body(accessTokenResponse);
        }
        challengeService.deleteChallenge(loginRequest.getWalletAddress(), Constant.ChallengeType.social.name(), WalletType.EVM.name());
        //验签
        boolean verified = SignUtil.verifyEOASignature(challenge, loginRequest.getSignature(), loginRequest.getWalletAddress());
        if (!verified) {
            accessTokenResponse.fail(ErrorCode.SIGNATURE_INVALID.getCode(), "signature is invalid");
            return ResponseEntity.badRequest().body(accessTokenResponse);
        }

        AuthLoginRequest authLoginRequest = new AuthLoginRequest();
        authLoginRequest.setWalletAddress(loginRequest.getWalletAddress());
        if (StringUtils.isNotEmpty(loginRequest.getSocialPlatform())) {
            authLoginRequest.setSubConnectProvider(WalletConstant.PlatformEnum.getEnumByCode(loginRequest.getSocialPlatform()));
        }
        authLoginRequest.setClientIp(RequestContextHolder.getClientIp());
        authLoginRequest.setDeviceInfo(osDeviceId);
        authLoginRequest.setCode(StringUtils.isEmpty(loginRequest.getCode()) ? "-1" : loginRequest.getCode());

        Response<PassportDTO> loginResponse = remoteAuthService.login(authLoginRequest);
        if(loginResponse.isSuccess()){
            log.info("{}::{}::{}::{}::{}::{}", Constant.STATISTICS_ORIGINAL_DATA, "LOGIN", loginResponse.getData().getPassportId(), RequestContextHolder.getClientIp(), osDeviceId, JSON.toJSONString(loginResponse.getData()));
            JwtToken accessToken = JwtUtils.generateTokenPair(String.valueOf(loginResponse.getData().getPassportId()), String.valueOf(loginResponse.getData().getPassportId()));
            accessTokenResponse.setObj(new AccessToken()
                    .accessToken(accessToken.getAccessToken())
                    .refreshToken(accessToken.getRefreshToken())
                    .isNewUser(loginResponse.getData().getIsNewUser())
                    .isNewPassport(loginResponse.getData().getIsNewPassport())
                    .isRedeemedSuccessfully(loginResponse.getData().getIsRedeemedSuccessfully()));
            log.info("authLogin response:{}", JSON.toJSONString(accessTokenResponse));
            return ResponseEntity.ok(accessTokenResponse);
        }
        kiKiMonitor.monitor(BusinessMonitor.LOGIN, new String[]{"code", "login_failed"});
        accessTokenResponse.fail(loginResponse.getCode(), loginResponse.getMessage());
        return ResponseEntity.badRequest().body(accessTokenResponse);
    }

    /**
     * GET /auth/challenge : 获取一个密钥
     * 获取一个密钥
     *
     * @param address 地址 (required)
     * @param type    wallet or social (optional, default to social)
     * @return OK (status code 200)
     */
    @Override
    public ResponseEntity<ChallengeResponse> challenge(String address, String type, WalletTypeEnum walletType) {
        Assert.isTrue(Arrays.stream(Constant.ChallengeType.values()).anyMatch(t -> t.name().equals(type)), "type must be social or wallet");
        Challenge challenge = challengeService.generateChallenge(address, type, walletType != null ? walletType.getValue() : WalletType.EVM.name());
        log.info("challenge response:{}", challenge);
        ChallengeResponse challengeResponse = new ChallengeResponse();
        challengeResponse.success();
        challengeResponse.setObj(challenge);
        return ResponseEntity.ok(challengeResponse);
    }

    /**
     * POST /auth/logout : 登出
     * 登出
     *
     * @return OK (status code 200)
     */
    @Override
    public ResponseEntity<LogoutResponse> logout() {
        LogoutResponse response = new LogoutResponse();
        response.success();

        String jwt = JwtHolder.jwt();
        Jws<Claims> jws = JwtUtils.getAccessToken(jwt);
        if (jws == null) {
            return ResponseEntity.ok(response);
        }
        Date expiration = jws.getBody().getExpiration();
        long expireTime = expiration.getTime() - System.currentTimeMillis();
        if (expireTime > 0) {
            redisTemplate.opsForValue().set(CacheKey.Auth.INVALID_TOKEN.getKey() + jwt, "1", expireTime, TimeUnit.MILLISECONDS);
        }
        return ResponseEntity.ok(response);
    }

    /**
     * POST /auth/refresh : 刷新accessToken
     * 刷新
     *
     * @param accessToken  accessToken (optional)
     * @param refreshToken refreshToken (optional)
     * @return OK (status code 200)
     */
    @Override
    public ResponseEntity<AccessTokenResponse> refreshAccessToken(String accessToken, String refreshToken) {
        Jws<Claims> jws = JwtUtils.getAccessToken(refreshToken);
        if(jws == null){
            AccessTokenResponse response = new AccessTokenResponse();
            response.fail(ErrorCode.REFRESH_TOKEN_INVALID.getCode(), ErrorCode.REFRESH_TOKEN_INVALID.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
        JwtToken jwtToken = JwtUtils.generateTokenPair(jws.getBody().getSubject(), jws.getBody().getSubject());
        AccessTokenResponse accessTokenResponse = new AccessTokenResponse();
        accessTokenResponse.success();
        accessTokenResponse.setObj(new AccessToken().accessToken(jwtToken.getAccessToken()).refreshToken(jwtToken.getRefreshToken()).isNewUser(false));
        return ResponseEntity.ok(accessTokenResponse);
    }

    @Override
    public ResponseEntity<ThirdAuthorizeResponse> thirdAuthorize(ThirdAuthorizeRequest thirdAuthorizeRequest) {
        log.info("thirdAuthorize request:{}", JSON.toJSONString(thirdAuthorizeRequest));
        ThirdAuthorizeResponse response = new ThirdAuthorizeResponse();
        response.success();

        ThirdAuthRequest thirdAuthRequest = new ThirdAuthRequest();
        thirdAuthRequest.setPlatform(thirdAuthorizeRequest.getPlatform());
        thirdAuthRequest.setCode(thirdAuthorizeRequest.getCode());
        thirdAuthRequest.setCustomerId(PassportHolder.passport().getPassportId());
        Response<ThirdAuthDTO> thirdAuthDTO = remoteAuthService.thirdAuth(thirdAuthRequest);
        log.info("thirdAuthDTO:{}", JSON.toJSONString(thirdAuthDTO));
        if(thirdAuthDTO.isSuccess()){
            ThirdAuthorizeInfo thirdAuthorizeInfo = authMapperStruct.toThirdAuthorizeInfo(thirdAuthDTO.getData());
            response.setObj(thirdAuthorizeInfo);
            return ResponseEntity.ok(response);
        } else {
            response.fail(thirdAuthDTO.getCode(), thirdAuthDTO.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @Override
    public ResponseEntity<ThirdBindingsResponse> thirdBindings(String authorization) {
        ThirdBindingsResponse thirdBindingsResponse = new ThirdBindingsResponse();
        thirdBindingsResponse.success();

        Response<List<ThirdBindingsDTO>> thirdBindings = remoteAuthService.thirdBindings(PassportHolder.passport().getPassportId());
        List<ThirdBindingsDTO> bindingsDTOList = thirdBindings.getData();

        try {
            WebResultAuthUrlsVO tasksAuthUrls = questsApi.saas(webProperties.getSaasId()).task().getTasksAuthUrls(webProperties.getSaasId(), null);
            // 处理绑定列表，为未绑定的平台设置授权URL
            processBindingsWithAuthUrls(bindingsDTOList, tasksAuthUrls, PassportHolder.passport().getPassportId());

        } catch (Exception e) {
            log.error("获取任务授权URL失败", e);
        }
        if(thirdBindings.isSuccess()){
            thirdBindingsResponse.setObj(authMapperStruct.toThirdBindingsInfo(bindingsDTOList));
            return ResponseEntity.ok(thirdBindingsResponse);
        } else {
            thirdBindingsResponse.fail(thirdBindings.getCode(), thirdBindings.getMessage());
            return ResponseEntity.badRequest().body(thirdBindingsResponse);
        }
    }

    /**
     * POST /customers/wallet/bind : 绑定钱包
     * 绑定钱包
     *
     * bindWalletRequest
     * @return OK (status code 200)
     */
    @Override
    public ResponseEntity<BindWalletResponse> bindWallet(com.drex.web.customer.generated.model.v1.BindWalletRequest bindWalletRequest) {
        log.info("bindWallet request:{}", JSON.toJSONString(bindWalletRequest));
        String address = bindWalletRequest.getAddress();
        String signature = bindWalletRequest.getSignature();
        WalletTypeEnum walletType = bindWalletRequest.getWalletType();
        WalletProviderEnum walletProvider = bindWalletRequest.getWalletProvider();
        BindWalletSocialPlatformEnum socialPlatform = bindWalletRequest.getSocialPlatform();
        String accountDetail = bindWalletRequest.getAccountDetail();

        BindWalletResponse bindWalletResponse = new BindWalletResponse();
        if(StringUtils.isBlank(address) || StringUtils.isBlank(signature)){
            bindWalletResponse.fail(ErrorCode.SIGNATURE_INVALID.getCode(), "address or signature is null");
            return ResponseEntity.badRequest().body(bindWalletResponse);
        }

        String actualWalletType = walletType != null ? walletType.getValue() : WalletType.EVM.name();
        String challenge = authService.getChallenge(address, Constant.ChallengeType.bind_wallet.name(), actualWalletType);
        if(challenge == null){
            bindWalletResponse.fail(ErrorCode.SIGNATURE_INVALID.getCode(), "challenge is null");
            return ResponseEntity.badRequest().body(bindWalletResponse);
        }

        try{
            boolean checked;
            if(WalletType.SOLANA.name().equals(actualWalletType)) {
                // Solana 签名验证：参数顺序为 (publicKey, message, signature)
                // 需要将十六进制签名转换为 Base58 格式
                String base58Signature = convertHexToBase58Signature(signature);
                checked = SignUtil.verifySolanaSignature(address, challenge, base58Signature);
            } else {
                checked = SignUtil.checkEvmPersonalSign(signature, challenge, address);
            }

            if(!checked){
                log.error("signature check failed, signature:{}, challenges:{}, address:{}", signature, challenge, address);
                bindWalletResponse.fail(ErrorCode.SIGNATURE_INVALID.getCode(), "signature check failed");
                return ResponseEntity.badRequest().body(bindWalletResponse);
            }
        }catch (Exception e){
            bindWalletResponse.fail(ErrorCode.SIGNATURE_INVALID.getCode(), "signature check failed");
            return ResponseEntity.badRequest().body(bindWalletResponse);
        }

        BindWalletRequest.BindWalletRequestBuilder builder = BindWalletRequest.builder()
                .walletAddress(address)
                .walletProvider(WalletConstant.WalletProviderEnum.getEnumByCode(walletProvider.getValue()))
                .subConnectProvider(WalletConstant.PlatformEnum.getEnumByCode(socialPlatform.getValue()))
                .passportId(PassportHolder.passport().getPassportId())
                .connectType(WalletConstant.ConnectTypeEnum.BIND)
                .walletType(WalletConstant.WalletTypeEnum.getEnumByCode(walletType.getValue()))
                .accountDetail(accountDetail);

        Response<Boolean> success = remotePassportService.bindWallet(builder.build());
        if(success.isSuccess() && success.getData()){
            bindWalletResponse.success();
            return ResponseEntity.ok(bindWalletResponse);
        }
        bindWalletResponse.fail(success.getCode(), success.getMessage());
        return ResponseEntity.badRequest().body(bindWalletResponse);
    }

    /**
     * 处理绑定列表，为未绑定的平台设置授权URL
     *
     * @param bindingsDTOList 绑定列表
     * @param tasksAuthUrls 任务授权URL列表
     */
    private void processBindingsWithAuthUrls(List<ThirdBindingsDTO> bindingsDTOList, WebResultAuthUrlsVO tasksAuthUrls, String passportId) {
        if (bindingsDTOList == null || bindingsDTOList.isEmpty()) {
            log.warn("绑定列表为空，跳过处理");
            return;
        }

        if (tasksAuthUrls == null || tasksAuthUrls.getObj() == null) {
            log.warn("任务授权URL列表为空，跳过处理");
            return;
        }

        log.info("开始处理绑定列表，bindingsDTOList size: {}, tasksAuthUrls size: {}",
                bindingsDTOList.size(), tasksAuthUrls.getObj().size());

        // 遍历绑定列表
        for (ThirdBindingsDTO binding : bindingsDTOList) {
            if (binding == null) {
                continue;
            }

            // 只处理未绑定状态（status为0）的平台
            if (!"0".equals(binding.getStatus())) {
                log.debug("平台 {} 已绑定，status: {}，跳过处理", binding.getPlatform(), binding.getStatus());
                continue;
            }

            log.debug("处理未绑定的平台: {}", binding.getPlatform());

            String platform = Constant.SocialPlatform.valueOf(binding.getPlatform()).getPlatformName();
            // 查找匹配的授权URL
            String authUrl = findAuthUrlForPlatform(platform, tasksAuthUrls.getObj(), passportId);

            if (StringUtils.isNotBlank(authUrl)) {
                binding.setConnectUrl(authUrl);
                log.info("为平台 {} 设置connectUrl: {}", binding.getPlatform(), authUrl);
            } else {
                log.warn("未找到平台 {} 的授权URL", binding.getPlatform());
            }
        }
    }

    /**
     * 在授权URL列表中查找指定平台的授权URL
     *
     * @param platform 平台名称
     * @param authUrlList 授权URL列表
     * @return 匹配的授权URL，如果未找到则返回null
     */
    private String findAuthUrlForPlatform(String platform, List<SocialAuthUrlResponse> authUrlList, String passportId) {
        if (StringUtils.isBlank(platform) || authUrlList == null || authUrlList.isEmpty()) {
            return null;
        }

        for (SocialAuthUrlResponse socialAuthUrlResponse : authUrlList) {

            String urlPlatform = socialAuthUrlResponse.getPlatform();
            String authUrl = socialAuthUrlResponse.getAuthUrl();

            // 平台名称不区分大小写比对
            if (platform.equalsIgnoreCase(urlPlatform)) {
                return authUrl;
            }
        }
        return null;
    }

    /**
     * 将十六进制签名转换为 Base58 格式
     * Solana 签名通常是 64 字节的 Ed25519 签名
     *
     * @param hexSignature 十六进制格式的签名（可能带 0x 前缀）
     * @return Base58 编码的签名
     */
    private String convertHexToBase58Signature(String hexSignature) {
        try {
            // 移除 0x 前缀
            if (hexSignature.startsWith("0x")) {
                hexSignature = hexSignature.substring(2);
            }

            // 将十六进制字符串转换为字节数组
            byte[] signatureBytes = Numeric.hexStringToByteArray(hexSignature);

            // 转换为 Base58 编码
            return Base58.encode(signatureBytes);
        } catch (Exception e) {
            log.error("Failed to convert hex signature to Base58: {}", e.getMessage());
            throw new IllegalArgumentException("Invalid hex signature format", e);
        }
    }
}
