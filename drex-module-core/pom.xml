<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.drex</groupId>
        <artifactId>drex-web</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <artifactId>drex-module-core</artifactId>
    <name>drex-module-core</name>
    <url>http://maven.apache.org</url>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.property.path>..</project.property.path>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>

        <!-- STOMP protocol support -->
        <dependency>
            <groupId>org.webjars</groupId>
            <artifactId>sockjs-client</artifactId>
            <version>1.5.1</version>
        </dependency>

        <!-- For browser fallback options -->
        <dependency>
            <groupId>org.webjars</groupId>
            <artifactId>stomp-websocket</artifactId>
            <version>2.3.4</version>
        </dependency>
        <dependency>
            <groupId>com.drex</groupId>
            <artifactId>drex-endpoint-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.drex</groupId>
            <artifactId>core-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.drex</groupId>
            <artifactId>drex-module-common</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.drex</groupId>
            <artifactId>drex-asset-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
    </dependencies>
    <profiles>
        <profile>
            <id>local</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.openapitools</groupId>
                        <artifactId>openapi-generator-maven-plugin</artifactId>
                        <version>6.6.0</version>
                        <executions>
                            <execution>
                                <id>core</id>
                                <goals>
                                    <goal>generate</goal>
                                </goals>
                                <configuration>
                                    <inputSpec>${project.basedir}/src/main/resources/api/core.yaml</inputSpec>
                                    <generatorName>spring</generatorName>
                                    <apiPackage>com.drex.web.core.generated.api.v1</apiPackage>
                                    <modelPackage>com.drex.web.core.generated.model.v1</modelPackage>
                                    <generateApiTests>false</generateApiTests>
                                    <generateModelTests>false</generateModelTests>
                                    <addTestCompileSourceRoot>false</addTestCompileSourceRoot>
                                    <output>.</output>
                                    <importMappings>
                                        <importMapping>WebResult=com.drex.web.common.WebResult</importMapping>
                                        <importMapping>Date=java.util.Date</importMapping>
                                        <importMapping>Map=java.util.Map</importMapping>
                                    </importMappings>
                                    <configOptions>
                                        <delegatePattern>true</delegatePattern>
                                        <hideGenerationTimestamp>true</hideGenerationTimestamp>
                                        <useTags>true</useTags>
                                    </configOptions>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
