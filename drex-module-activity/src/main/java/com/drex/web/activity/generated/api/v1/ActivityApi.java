/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (6.6.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
package com.drex.web.activity.generated.api.v1;

import com.drex.web.activity.generated.model.v1.TaskSocialEventRequest;
import com.drex.web.activity.generated.model.v1.TaskSocialEventResponse;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
@Validated
@Tag(name = "Activity", description = "活动相关接口描述")
public interface ActivityApi {

    default ActivityApiDelegate getDelegate() {
        return new ActivityApiDelegate() {};
    }

    /**
     * POST /task/socialEvent : 社交事件
     * 
     *
     * @param taskSocialEventRequest  (optional)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "taskSocialEvent",
        summary = "社交事件",
        description = "",
        tags = { "Activity" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = TaskSocialEventResponse.class))
            })
        },
        security = {
            @SecurityRequirement(name = "jwtBearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/task/socialEvent",
        produces = { "application/json" },
        consumes = { "application/json" }
    )
    default ResponseEntity<TaskSocialEventResponse> taskSocialEvent(
        @Parameter(name = "TaskSocialEventRequest", description = "") @Valid @RequestBody(required = false) TaskSocialEventRequest taskSocialEventRequest
    ) {
        return getDelegate().taskSocialEvent(taskSocialEventRequest);
    }

}
