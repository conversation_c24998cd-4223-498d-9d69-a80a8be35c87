package com.drex.web.customer.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * AddDeveloperWaitListUploadResponseAllOfObj
 */

@JsonTypeName("AddDeveloperWaitListUploadResponse_allOf_obj")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class AddDeveloperWaitListUploadResponseAllOfObj {

  private String fileName;

  private String filePath;

  public AddDeveloperWaitListUploadResponseAllOfObj fileName(String fileName) {
    this.fileName = fileName;
    return this;
  }

  /**
   * 上传文件的名称
   * @return fileName
  */
  
  @Schema(name = "fileName", description = "上传文件的名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("fileName")
  public String getFileName() {
    return fileName;
  }

  public void setFileName(String fileName) {
    this.fileName = fileName;
  }

  public AddDeveloperWaitListUploadResponseAllOfObj filePath(String filePath) {
    this.filePath = filePath;
    return this;
  }

  /**
   * 上传文件的路径
   * @return filePath
  */
  
  @Schema(name = "filePath", description = "上传文件的路径", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("filePath")
  public String getFilePath() {
    return filePath;
  }

  public void setFilePath(String filePath) {
    this.filePath = filePath;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    AddDeveloperWaitListUploadResponseAllOfObj addDeveloperWaitListUploadResponseAllOfObj = (AddDeveloperWaitListUploadResponseAllOfObj) o;
    return Objects.equals(this.fileName, addDeveloperWaitListUploadResponseAllOfObj.fileName) &&
        Objects.equals(this.filePath, addDeveloperWaitListUploadResponseAllOfObj.filePath);
  }

  @Override
  public int hashCode() {
    return Objects.hash(fileName, filePath);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class AddDeveloperWaitListUploadResponseAllOfObj {\n");
    sb.append("    fileName: ").append(toIndentedString(fileName)).append("\n");
    sb.append("    filePath: ").append(toIndentedString(filePath)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

