package com.drex.web.activity.delegate;

import com.alibaba.fastjson.JSON;
import com.drex.core.api.RemoteTwitterAntiCheatService;
import com.drex.core.api.request.GenerateMaizeRequest;
import com.drex.core.api.request.SocialConstant;
import com.drex.core.api.request.SocialEventBody;
import com.drex.core.api.response.MaizeDTO;
import com.drex.web.activity.converter.ActivityConverter;
import com.drex.web.activity.generated.api.v1.ActivityApiDelegate;
import com.drex.web.activity.generated.model.v1.TaskSocialEventRequest;
import com.drex.web.activity.generated.model.v1.TaskSocialEventResponse;
import com.drex.web.common.ErrorCode;
import com.drex.web.common.PassportHolder;
import com.drex.web.common.config.WebProperties;
import com.drex.web.common.context.RequestContextHolder;
import com.drex.web.common.utils.SignUtil;
import com.kikitrade.framework.common.model.Response;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/4/25 14:06
 * @description:
 */
@Service
@Slf4j
public class ActivityApiDelegateImpl implements ActivityApiDelegate {

    @DubboReference
    private RemoteTwitterAntiCheatService remoteTwitterAntiCheatService;

    private static final String DEFAULT_APP_ID = "drex";

    @Resource
    private WebProperties webProperties;

    @Override
    public ResponseEntity<TaskSocialEventResponse> taskSocialEvent(TaskSocialEventRequest taskSocialEventRequest) {
        log.info("task social event, request: {}", JSON.toJSONString(taskSocialEventRequest));
        TaskSocialEventResponse taskSocialEventResponse = new TaskSocialEventResponse();

        boolean verified = SignUtil.verifyHmacSignature(String.format("%s|%s|%s", taskSocialEventRequest.getSocialContentId(), taskSocialEventRequest.getTime(), taskSocialEventRequest.getDeviceFinger()), taskSocialEventRequest.getSignature()
                , webProperties.getSecretKeyFull());
        if(!verified){
            taskSocialEventResponse.fail(ErrorCode.SIGNATURE_INVALID.getCode(), "signature verify failed");
            return ResponseEntity.badRequest().body(taskSocialEventResponse);
        }

        SocialConstant.PlatformEnum platformEnum = SocialConstant.PlatformEnum.valueOf(taskSocialEventRequest.getPlatform().getValue());
        SocialConstant.EventEnum eventEnum = SocialConstant.EventEnum.valueOf(taskSocialEventRequest.getType().getValue());
        SocialEventBody socialEventBody = SocialEventBody.builder()
                        .socialContentId(taskSocialEventRequest.getSocialContentId())
                        .replyContent(taskSocialEventRequest.getReplyContent())
                        .build();
        GenerateMaizeRequest request = GenerateMaizeRequest.builder()
                .customerId(PassportHolder.passport().getPassportId())
                .socialPlatform(platformEnum).socialEvent(eventEnum)
                .socialEventBody(socialEventBody).ipAddress(RequestContextHolder.getClientIp())
                .fingerprint(taskSocialEventRequest.getDeviceFinger())
                .build();

        Response<MaizeDTO> maizeDTOResponse = remoteTwitterAntiCheatService.reportEvents(request);
        if (maizeDTOResponse.isSuccess()) {
            TaskSocialEventResponse response = ActivityConverter.toTaskSocialEventResponse(maizeDTOResponse.getData());
            return ResponseEntity.ok(response);
        } else {
            taskSocialEventResponse.fail(maizeDTOResponse.getCode(), maizeDTOResponse.getMessage());
            return ResponseEntity.badRequest().body(taskSocialEventResponse);
        }

    }
}
