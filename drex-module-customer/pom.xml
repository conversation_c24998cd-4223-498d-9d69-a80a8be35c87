<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.drex</groupId>
        <artifactId>drex-web</artifactId>
        <version>0.0.1-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>drex-module-customer</artifactId>
    <name>drex-module-customer</name>
    <description>web 模块，对应 MVC 的 V 概念，存放视图层的逻辑</description>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.property.path>..</project.property.path>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.drex</groupId>
            <artifactId>drex-module-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.drex</groupId>
            <artifactId>customer-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>co.evg</groupId>
            <artifactId>achievement-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.drex</groupId>
            <artifactId>core-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.drex</groupId>
            <artifactId>drex-asset-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>5.2.3</version> <!-- or the latest version -->
        </dependency>
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>3.16.1</version> <!-- or the version you're using -->
        </dependency>
    </dependencies>
    <profiles>
        <profile>
            <id>local</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.openapitools</groupId>
                        <artifactId>openapi-generator-maven-plugin</artifactId>
                        <version>6.6.0</version>
                        <executions>
                            <execution>
                                <id>customer</id>
                                <goals>
                                    <goal>generate</goal>
                                </goals>
                                <configuration>
                                    <inputSpec>${project.basedir}/src/main/resources/api/customer.yaml</inputSpec>
                                    <generatorName>spring</generatorName>
                                    <apiPackage>com.drex.web.customer.generated.api.v1</apiPackage>
                                    <modelPackage>com.drex.web.customer.generated.model.v1</modelPackage>
                                    <generateApiTests>false</generateApiTests>
                                    <generateModelTests>false</generateModelTests>
                                    <addTestCompileSourceRoot>false</addTestCompileSourceRoot>
                                    <output>.</output>
                                    <importMappings>
                                        <importMapping>WebResult=com.drex.web.common.WebResult</importMapping>
                                        <importMapping>Date=java.util.Date</importMapping>
                                        <importMapping>Map=java.util.Map</importMapping>
                                    </importMappings>
                                    <configOptions>
                                        <delegatePattern>true</delegatePattern>
                                        <hideGenerationTimestamp>true</hideGenerationTimestamp>
                                        <useTags>true</useTags>
                                    </configOptions>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>
