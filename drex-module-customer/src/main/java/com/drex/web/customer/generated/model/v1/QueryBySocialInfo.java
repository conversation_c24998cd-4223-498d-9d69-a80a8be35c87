package com.drex.web.customer.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * QueryBySocialInfo
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class QueryBySocialInfo {

  private String platform;

  private String passportId;

  private String socialUserId;

  private String socialHandleName;

  private String socialEmail;

  public QueryBySocialInfo platform(String platform) {
    this.platform = platform;
    return this;
  }

  /**
   * 授权平台
   * @return platform
  */
  
  @Schema(name = "platform", description = "授权平台", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("platform")
  public String getPlatform() {
    return platform;
  }

  public void setPlatform(String platform) {
    this.platform = platform;
  }

  public QueryBySocialInfo passportId(String passportId) {
    this.passportId = passportId;
    return this;
  }

  /**
   * passportId
   * @return passportId
  */
  
  @Schema(name = "passportId", description = "passportId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("passportId")
  public String getPassportId() {
    return passportId;
  }

  public void setPassportId(String passportId) {
    this.passportId = passportId;
  }

  public QueryBySocialInfo socialUserId(String socialUserId) {
    this.socialUserId = socialUserId;
    return this;
  }

  /**
   * 社媒userId
   * @return socialUserId
  */
  
  @Schema(name = "socialUserId", description = "社媒userId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("socialUserId")
  public String getSocialUserId() {
    return socialUserId;
  }

  public void setSocialUserId(String socialUserId) {
    this.socialUserId = socialUserId;
  }

  public QueryBySocialInfo socialHandleName(String socialHandleName) {
    this.socialHandleName = socialHandleName;
    return this;
  }

  /**
   * 社媒用户名
   * @return socialHandleName
  */
  
  @Schema(name = "socialHandleName", description = "社媒用户名", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("socialHandleName")
  public String getSocialHandleName() {
    return socialHandleName;
  }

  public void setSocialHandleName(String socialHandleName) {
    this.socialHandleName = socialHandleName;
  }

  public QueryBySocialInfo socialEmail(String socialEmail) {
    this.socialEmail = socialEmail;
    return this;
  }

  /**
   * 社媒邮箱
   * @return socialEmail
  */
  
  @Schema(name = "socialEmail", description = "社媒邮箱", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("socialEmail")
  public String getSocialEmail() {
    return socialEmail;
  }

  public void setSocialEmail(String socialEmail) {
    this.socialEmail = socialEmail;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    QueryBySocialInfo queryBySocialInfo = (QueryBySocialInfo) o;
    return Objects.equals(this.platform, queryBySocialInfo.platform) &&
        Objects.equals(this.passportId, queryBySocialInfo.passportId) &&
        Objects.equals(this.socialUserId, queryBySocialInfo.socialUserId) &&
        Objects.equals(this.socialHandleName, queryBySocialInfo.socialHandleName) &&
        Objects.equals(this.socialEmail, queryBySocialInfo.socialEmail);
  }

  @Override
  public int hashCode() {
    return Objects.hash(platform, passportId, socialUserId, socialHandleName, socialEmail);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class QueryBySocialInfo {\n");
    sb.append("    platform: ").append(toIndentedString(platform)).append("\n");
    sb.append("    passportId: ").append(toIndentedString(passportId)).append("\n");
    sb.append("    socialUserId: ").append(toIndentedString(socialUserId)).append("\n");
    sb.append("    socialHandleName: ").append(toIndentedString(socialHandleName)).append("\n");
    sb.append("    socialEmail: ").append(toIndentedString(socialEmail)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

