package com.drex.web.customer.generated.model.v1;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * ThirdAuthorizeRequest
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class ThirdAuthorizeRequest {

  private String platform;

  private String code;

  /**
   * Default constructor
   * @deprecated Use {@link ThirdAuthorizeRequest#ThirdAuthorizeRequest(String, String)}
   */
  @Deprecated
  public ThirdAuthorizeRequest() {
    super();
  }

  /**
   * Constructor with only required parameters
   */
  public ThirdAuthorizeRequest(String platform, String code) {
    this.platform = platform;
    this.code = code;
  }

  public ThirdAuthorizeRequest platform(String platform) {
    this.platform = platform;
    return this;
  }

  /**
   * 授权平台
   * @return platform
  */
  @NotNull 
  @Schema(name = "platform", description = "授权平台", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("platform")
  public String getPlatform() {
    return platform;
  }

  public void setPlatform(String platform) {
    this.platform = platform;
  }

  public ThirdAuthorizeRequest code(String code) {
    this.code = code;
    return this;
  }

  /**
   * 授权code
   * @return code
  */
  @NotNull 
  @Schema(name = "code", description = "授权code", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("code")
  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ThirdAuthorizeRequest thirdAuthorizeRequest = (ThirdAuthorizeRequest) o;
    return Objects.equals(this.platform, thirdAuthorizeRequest.platform) &&
        Objects.equals(this.code, thirdAuthorizeRequest.code);
  }

  @Override
  public int hashCode() {
    return Objects.hash(platform, code);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ThirdAuthorizeRequest {\n");
    sb.append("    platform: ").append(toIndentedString(platform)).append("\n");
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

