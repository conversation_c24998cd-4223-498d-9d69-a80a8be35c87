openapi: 3.0.3
info:
  license:
    name: Apache 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0
  description: The Kweb API v1.0
  title: Kweb API
  version: "1.0"
servers:
  - description: kweb server url
    url: https://api.trex.dev.dipbit.xyz/v1
tags:
  - name: Passport
    description: 用户相关接口描述
  - name: Customer
    description: 用户相关接口描述
  - name: Auth
    description: 用户授权相关接口描述
paths:
  /auth/challenge:
    $ref: "paths/challenge.yaml"
  /auth/qr/login:
    $ref: "paths/qrLogin.yaml"
  /auth/login:
    $ref: "paths/login.yaml"
  /auth/logout:
    $ref: "paths/logout.yaml"
  /auth/refresh:
    $ref: "paths/refresh.yaml"
  /auth/third/authorize:
    $ref: "paths/authorize.yaml"
  /auth/third/bindings:
    $ref: "paths/bindings.yaml"
  /customers/invites/bind:
    $ref: "paths/invite_bind.yaml"
  /customers/invites/me:
    $ref: "paths/invite.yaml"
  /customers/invites/qrCode:
    $ref: "paths/invite_qrCode.yaml"
  /customers/addWaitList:
    $ref: "paths/addWaitList.yaml"
  /customers/social/upload:
    $ref: "paths/socialUpload.yaml"
  /customers/addDeveloperWaitList:
    $ref: "paths/addWaitDeveloperList.yaml"
  /customers/addDeveloperWaitList/upload:
    $ref: "paths/addDeveloperWaitListUpload.yaml"
  /customers/addCreateWaitList:
    $ref: "paths/addWaitCreatorList.yaml"
  /passport/setHandleName:
    $ref: "paths/setHandleName.yaml"
  /passport/me:
    $ref: "paths/passport.yaml"
  /passport/connect:
    $ref: "paths/passportConnect.yaml"
  /passport/wallet/bind:
    $ref: "paths/wallet_bind.yaml"
  /customers/privacy/auth:
    $ref: "paths/privacyAuth.yaml"
  /customers/privacy/reverseAuth:
    $ref: "paths/reversePrivacyAuth.yaml"
  /s2s/customers/bindSocialInfo:
    $ref: "paths/bindSocialInfo.yaml"
  /s2s/customers/unbindSocialInfo:
    $ref: "paths/unbindSocialInfo.yaml"
  /s2s/customers/queryBySocialInfo:
    $ref: "paths/queryBySocialInfo.yaml"
  #我的邀请人
  /s2s/customers/invites/me:
    $ref: "paths/inviteForS2S.yaml"

components:
  securitySchemes:
    jwtBearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: |
        使用JWT Bearer令牌进行身份验证。
        在请求头中添加 `Authorization` 字段，格式为 `Bearer <JWT令牌>`。
