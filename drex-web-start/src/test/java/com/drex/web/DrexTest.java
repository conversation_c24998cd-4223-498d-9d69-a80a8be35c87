package com.drex.web;

import com.alibaba.fastjson2.JSON;
import com.drex.customer.api.response.CustomerDTO;
import com.drex.customer.api.response.PassportDTO;
import com.drex.web.activity.generated.api.v1.ActivityApiDelegate;
import com.drex.web.activity.generated.model.v1.TaskListResponse;
import com.drex.web.activity.generated.model.v1.TaskVerifyRequest;
import com.drex.web.activity.generated.model.v1.TaskVerifyResponse;
import com.drex.web.common.PassportHolder;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;

/**
 * <AUTHOR>
 * @date 2025/4/25 15:35
 * @description:
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = DrexWebApplication.class)
@TestPropertySource(locations = "classpath:application-local.properties")
@Slf4j
public class DrexTest {
}

